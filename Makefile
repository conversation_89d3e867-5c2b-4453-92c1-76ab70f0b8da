# -*- coding:utf-8 -*-



uname=`uname -s`

goos=$(shell echo $(uname) | tr A-Z a-z)
# goos=windows
# goos=linux


goarch=amd64

.PHONY: clean all

all: app

#  pg vd1800
# pg vdtcp

#CGO_ENABLED=0 GOOS=${goos} GOARCH=amd64 go build -ldflags "-X main.BuildTime=$(date -u +%Y%m%d.%H%M%S)" -o ${BINARY_NAME}_${goos}.bin
#CGO_ENABLED=1 GOOS=${goos} GOARCH=amd64 go build -ldflags "-s -w" -o ${BINARY_NAME}_${goos}.bin

app:
		@mkdir -p ./build
		CGO_ENABLED=0 CC=musl-gcc GOOS=${goos} GOARCH=${goarch}  go build -o build/app_${goos}.bin  ./main.go


clean:
		go clean
		@rm -rf build
