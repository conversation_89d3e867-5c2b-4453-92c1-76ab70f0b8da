# http服务：iot、sl的后台管理端，数据库、API等
# gateway    http server port : 8100
# app         http server port: 8200
# sl651       tcp server port : 10020
# sl206       tcp server port : 10021
# sl427       tcp server port : 10022
# sl2022      tcp server port : 10030
# proxy       tcp server port : 10019
# iot-tcp     tcp server port : 10018

# mqtt_broker_tcp:  1883

#ip_hunan1: "*************" # 湖南1服务器
#ip_hunan2: "*************" # 湖南2服务器
# ========================================================================
deploy_id: "123"
deploy_license : "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.Qj920C4IjO0vLwI21N2o53uNeGddnCr3AeKTwjQfXg8"

service_app:
  name: "app"
  http_port: 8200
  db_log_level: "debug"
  
# ========================================================================
# sl tcp服务, sl651
service_buca:
  name: "buca"
  host: "ebox-buca:8180" # 容器名字
  # host: "http://*************:8180" # iP地址，也可以换成容器名字
  
# 其他全局配置:每一个服务有自己的日志文件，以服务名为前缀
misc:
  log_level: "debug"
  format: "text"
  sl651_response_with_eot: false

minio:
  endpoint: "ebox-minio:9000"
  domain: "https://nuc.beithing.com:59000"  #  minio域名，外部访问的地址
  access_key: "beithing"
  secret_key: "Root@1024Minio"
  bucket_image: "ebox-image"
  bucket_other: "ebox-other"
  bucket_project: "ebox-project"



redis:
  address: "ebox-redis:6379"
  password: "123456"

postgres:
  username: "root"
  address: "ebox-postgres:5432"
  password: "root1024"
  port: "5432"
  app_db_name: "app"



# 获取 ebox的数据的key
ebox:
  app_key: "d93hgq7o6so8"
  app_secrect: "u3Y4tHPCrwO9etIzMOZhXWjAOt0VFZBb" 
