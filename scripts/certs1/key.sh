#!/usr/bin/env bash

#Country Name (2 letter code)
Country="CN"

#State or Province Name (full name)
Province="GuangDong"

#Locality Name (eg, city)
City="ShenZhen"

#Organization Name (eg, company)
Organization="BeiSheng"

#Organizational Unit Name (eg, section)
Organizational="RD"

#Common Name (eg, fully qualified host name)
CommonName="EBOX"

function caGen() {
  echo "生成根证书"

  #生成 ca 证书密钥
  openssl genrsa -out ca.key 2048
  #生成 ca 证书公钥
  openssl req -new -x509 -days 3650 -key ca.key -out ca.pem  -subj "/C=$Country/ST=$Province/L=$City/O=$Organization/OU=$Organizational/CN=$CommonName"
}

function serverGen(){
  echo "生成服务端证书"

  openssl genpkey -algorithm RSA -out server.key
  openssl req -new -nodes -key server.key -out server.csr -days 3650 -subj "/C=$Country/ST=$Province/L=$City/O=$Organization/OU=$Organizational/CN=$CommonName" -config ./openssl.cnf -extensions v3_req
  openssl x509 -req -days 3650 -in server.csr -out server.pem -CA ca.pem -CAkey ca.key -CAcreateserial -extfile ./openssl.cnf -extensions v3_req
}

function doClean() {
    rm -rf server*
    rm -rf ca*
}

#switch-case
case $1 in
    clean)
      doClean
      ;;
    ca)
      caGen
      ;;
    server)
      serverGen
      ;;
    *)
      echo "usage: ./key.sh ca | client | server"
esac