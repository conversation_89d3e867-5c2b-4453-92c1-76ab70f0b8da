#!/bin/bash

# MQTT 监控程序启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🔍 MQTT 监控程序启动脚本${NC}"
echo "=================================="

# 检查 Go 环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ Go 未安装或不在 PATH 中${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Go 环境检查通过${NC}"

# 进入项目目录
cd "$PROJECT_ROOT"

# 检查 MQTT 监控程序是否存在
MONITOR_DIR="cmd/mqtt_monitor"
if [ ! -d "$MONITOR_DIR" ]; then
    echo -e "${RED}❌ 找不到 MQTT 监控程序目录: $MONITOR_DIR${NC}"
    exit 1
fi

if [ ! -f "$MONITOR_DIR/main.go" ]; then
    echo -e "${RED}❌ 找不到 MQTT 监控程序: $MONITOR_DIR/main.go${NC}"
    exit 1
fi

echo -e "${GREEN}✅ MQTT 监控程序文件检查通过${NC}"

# 检查依赖
echo -e "${YELLOW}📦 检查 Go 模块依赖...${NC}"
go mod tidy

# 编译并运行
echo -e "${YELLOW}🚀 启动 MQTT 监控程序...${NC}"
echo "=================================="

# 设置信号处理
trap 'echo -e "\n${YELLOW}🛑 收到中断信号，正在停止监控程序...${NC}"; exit 0' INT TERM

# 运行监控程序
cd "$MONITOR_DIR"
go run main.go

echo -e "${GREEN}✅ MQTT 监控程序已停止${NC}"
