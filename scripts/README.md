## gRPC tls单向认证

```
# 用 openssl 生成 ca 和双方 SAN 证书
# ubuntu 16.04 -> /etc/ssl/openssl.cnf 需要进行一些修改
# 1、找到 [ CA_default ] 取消注释 # copy_extensions = copy
# 2、找到[ req ] 取消注释 # req_extensions = v3_req # The extensions to add to a certificate request
# 找到[ v3_req ] 添加 subjectAltName = @alt_names
# 添加新的标签 [ alt_names ] 和标签字段
[ alt_names ]
DNS.1 = localhost
DNS.2 = *.ronething.cn
DNS.3 = *.ronething.com
```
此处 DNS 按需填写。

使用脚本 keys.sh client ，生成服务器端需要的证书


#### 目录

```
├── ca.key(根密钥)
├── ca.pem(根证书)
├── client
│   ├── client.csr(客户端证书签发请求)
│   ├── client.key(客户端密钥)
│   └── client.pem(客户端证书)
└── server
    ├── server.csr(服务端证书签发请求)
    ├── server.key(服务端密钥)
    └── server.pem(服务端证书)
```

