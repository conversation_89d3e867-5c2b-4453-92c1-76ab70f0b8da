package cmd

import (
	"fmt"
	"os"
	"time"

	"bs.com/app/internal/dm"
	"bs.com/app/internal/gw/model"
	"github.com/spf13/cobra"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"

	"bs.com/app/config"
	"bs.com/app/global"
	"bs.com/app/internal/gw"
)

var GW = &cobra.Command{
	Use:   "gw",
	Short: "start app server",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		xlog.Debugf("gw pre run ...")
		cfg := config.Get()
		misc := config.Get().Misc
		//初始化logger

		level, name, path, format := misc.LogLevel, cfg.ServiceApp.Name, misc.PathLog, misc.Format

		// 调试阶段，使用串口日志
		if os.Getenv("RUN_MODE") == "dev" {
			xlog.Info("run mode ------------- dev")
			level = "debug"
			path = ""
			format = "text"
		}

		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)

		// global.CheckLicense() //检查 license 信息

		//全局容器
		global.InitGlobal()

		//检查influx数据库的连接
		global.InitInflux()

		//初始化mysql 和 redis
		global.InitRedis(0)

		// minio存储服务
		// global.InitMinio()

		// 检查 pgsql 数据库是否存在，如果不存在，则 autoMigrate之后，初始化表
		dbName := cfg.Postgres.AppDBName
		dbExisted := global.PgCheckOrCreate(dbName)

		// 初始化数据库的连接
		global.InitPostgres(dbName)

		// 初始化查询表
		_ = model.NewToolInstance()

		//创建表 automigrate
		err := model.PGAutoMigrate()
		if err != nil {
			xlog.Error("auto migrate err : ", err.Error())
			os.Exit(1)
		} else {
			xlog.Info("auto migrate success.")
		}

		// 如果开始时候,数据库不存在,则导入初始化一些必要的数据
		if !dbExisted {
			model.InitDBData()
		}
	},

	Run: func(cmd *cobra.Command, args []string) {
		cfg := config.Get()

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		//运行 shell
		wg.Go(gw.NewGatewayShell())

		wg.Go(dm.NewDeviceManger())  // mqtt device manager
		wg.Go(dm.NewConsumerThing()) // 一个 nats 消费者，消费来自设备的消息

		wg.Go(gw.NewHttpServer(cfg.ServiceApp.HttpPort)) //运行http server

		wg.Wait()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("............... by")
	},
}
