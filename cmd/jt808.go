package cmd

import (
	"fmt"
	"os"
	"time"

	"github.com/spf13/cobra"

	"bs.com/app/config"
	apjt808 "bs.com/app/internal/ap_jt808"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

var Jt808 = &cobra.Command{
	Use:   "jt808",
	Short: "start jt808",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {
		xlog.Debugf("jt808 pre run ...")
		misc := config.Get().Misc
		name := "jt808"
		//初始化logger
		level, name, path, format := misc.LogLevel, name, misc.PathLog, misc.Format

		// 调试阶段，使用串口日志
		if os.Getenv("RUN_MODE") == "dev" {
			xlog.Info("run mode ------------- dev")
			level = "debug"
			path = ""
			format = "text"
		}

		//初始化logger
		xlog.InitLogger(format, cmd.Use, level, name, path)
	},

	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		wg.Go(apjt808.NewJt808App())

		wg.Go(apjt808.NewJt808Shell())

		wg.Wait()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}
