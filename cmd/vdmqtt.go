package cmd

import (
	"fmt"
	"time"

	"github.com/spf13/cobra"

	"bs.com/app/pkg/xwg"

	"bs.com/app/internal/vdmqtt"
)

var VDmqtt = &cobra.Command{
	Use:   "vdmqtt",
	Short: "start vdmqtt",

	//初始化
	PreRun: func(cmd *cobra.Command, args []string) {

	},

	Run: func(cmd *cobra.Command, args []string) {

		wg := xwg.NewWorkerGroup()

		//监听信号
		wg.Go(xwg.NewSignalServer())

		//运行 shell
		wg.Go(vdmqtt.NewVDmqttShell())

		wg.Go(vdmqtt.NewMqttDevice())

		wg.Wait()

		time.Sleep(200 * time.Millisecond) //200ms
		fmt.Println("by")
	},
}
