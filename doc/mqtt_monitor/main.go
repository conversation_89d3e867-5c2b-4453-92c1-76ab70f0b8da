package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// MQTT 配置
const (
	MQTT_BROKER     = "tcp://*************:1883" // 根据你的配置文件
	CLIENT_ID       = "mqtt_system_monitor"
	KEEP_ALIVE      = 60 * time.Second
	CONNECT_TIMEOUT = 10 * time.Second
)

// 系统监控主题列表
var SYS_TOPICS = []string{
	// 客户端相关
	"$SYS/broker/clients/connected",    // 当前连接的客户端数量
	"$SYS/broker/clients/disconnected", // 断开连接的客户端数量
	"$SYS/broker/clients/total",        // 总客户端数量
	"$SYS/broker/clients/maximum",      // 最大连接数

	// 消息统计
	"$SYS/broker/messages/received", // 接收的消息数
	"$SYS/broker/messages/sent",     // 发送的消息数
	"$SYS/broker/messages/dropped",  // 丢弃的消息数

	// 系统信息
	"$SYS/broker/version",       // broker 版本
	"$SYS/broker/uptime",        // 运行时间
	"$SYS/broker/system/memory", // 内存使用
}

// 业务主题列表 - 监控设备消息
var BUSINESS_TOPICS = []string{
	"thing/up/+/+/+",   // 所有设备上行消息
	"thing/down/+/+/+", // 所有设备下行消息
}

type MQTTMonitor struct {
	client mqtt.Client
	ctx    context.Context
	cancel context.CancelFunc
}

func NewMQTTMonitor() *MQTTMonitor {
	ctx, cancel := context.WithCancel(context.Background())
	return &MQTTMonitor{
		ctx:    ctx,
		cancel: cancel,
	}
}

func (m *MQTTMonitor) onConnect(client mqtt.Client) {
	fmt.Printf("✅ 成功连接到 MQTT Broker: %s\n", MQTT_BROKER)
	fmt.Printf("📅 连接时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println(strings.Repeat("-", 60))

	// 订阅系统主题
	fmt.Println("📡 订阅系统监控主题:")
	for _, topic := range SYS_TOPICS {
		if token := client.Subscribe(topic, 0, m.onSystemMessage); token.Wait() && token.Error() != nil {
			fmt.Printf("❌ 订阅失败: %s, 错误: %v\n", topic, token.Error())
		} else {
			fmt.Printf("   📊 %s\n", topic)
		}
	}

	// 订阅业务主题
	fmt.Println("\n📡 订阅业务消息主题:")
	for _, topic := range BUSINESS_TOPICS {
		if token := client.Subscribe(topic, 0, m.onBusinessMessage); token.Wait() && token.Error() != nil {
			fmt.Printf("❌ 订阅失败: %s, 错误: %v\n", topic, token.Error())
		} else {
			fmt.Printf("   🔔 %s\n", topic)
		}
	}
	fmt.Println(strings.Repeat("-", 60))
}

func (m *MQTTMonitor) onConnectionLost(client mqtt.Client, err error) {
	fmt.Printf("⚠️  连接丢失: %v\n", err)
}

func (m *MQTTMonitor) onSystemMessage(client mqtt.Client, msg mqtt.Message) {
	topic := msg.Topic()
	payload := string(msg.Payload())
	timestamp := time.Now().Format("15:04:05")

	fmt.Printf("[%s] 📊 %s\n", timestamp, topic)
	fmt.Printf("           💾 数据: %s\n", payload)

	// 特殊处理某些主题的数据格式
	switch {
	case strings.Contains(topic, "memory") && isNumeric(payload):
		if memBytes, err := strconv.ParseInt(payload, 10, 64); err == nil {
			memoryMB := float64(memBytes) / (1024 * 1024)
			fmt.Printf("           📈 内存: %.2f MB\n", memoryMB)
		}
	case strings.Contains(topic, "uptime") && isNumeric(payload):
		if uptime, err := strconv.ParseInt(payload, 10, 64); err == nil {
			uptimeHours := float64(uptime) / 3600
			fmt.Printf("           ⏰ 运行时间: %.2f 小时\n", uptimeHours)
		}
	case strings.Contains(topic, "version"):
		fmt.Printf("           🔖 版本: %s\n", payload)
	}
	fmt.Println()
}

func (m *MQTTMonitor) onBusinessMessage(client mqtt.Client, msg mqtt.Message) {
	topic := msg.Topic()
	payload := string(msg.Payload())
	timestamp := time.Now().Format("15:04:05")

	// 解析 topic 获取设备信息
	parts := strings.Split(topic, "/")
	if len(parts) >= 5 {
		direction := parts[1]    // up/down
		msgType := parts[2]      // attri/event/action
		operation := parts[3]    // report/get/set/post/call/reply
		deviceTypeID := parts[4] // 设备类型ID
		deviceID := ""
		if len(parts) > 5 {
			deviceID = parts[5] // 设备ID
		}

		directionIcon := "⬆️"
		if direction == "down" {
			directionIcon = "⬇️"
		}

		fmt.Printf("[%s] %s 设备消息\n", timestamp, directionIcon)
		fmt.Printf("           📱 设备: %s/%s\n", deviceTypeID, deviceID)
		fmt.Printf("           📋 类型: %s.%s\n", msgType, operation)
		fmt.Printf("           📄 主题: %s\n", topic)
		fmt.Printf("           💾 数据: %s\n", payload)
	} else {
		fmt.Printf("[%s] 🔔 业务消息\n", timestamp)
		fmt.Printf("           📄 主题: %s\n", topic)
		fmt.Printf("           💾 数据: %s\n", payload)
	}
	fmt.Println()
}

func (m *MQTTMonitor) Start() error {
	fmt.Println("🚀 启动 MQTT 系统监控程序")
	fmt.Printf("🔗 连接地址: %s\n", MQTT_BROKER)
	fmt.Printf("⏱️  心跳间隔: %v\n", KEEP_ALIVE)
	fmt.Println(strings.Repeat("=", 60))

	// 配置 MQTT 客户端选项
	opts := mqtt.NewClientOptions()
	opts.AddBroker(MQTT_BROKER)
	opts.SetClientID(CLIENT_ID)
	opts.SetKeepAlive(KEEP_ALIVE)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetConnectTimeout(CONNECT_TIMEOUT)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(30 * time.Second)
	opts.SetCleanSession(true)

	// 设置回调函数
	opts.SetOnConnectHandler(m.onConnect)
	opts.SetConnectionLostHandler(m.onConnectionLost)

	// 创建客户端
	m.client = mqtt.NewClient(opts)

	// 连接到 broker
	if token := m.client.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("连接失败: %v", token.Error())
	}

	return nil
}

func (m *MQTTMonitor) Stop() {
	fmt.Println("\n🛑 正在停止监控...")
	if m.client != nil && m.client.IsConnected() {
		m.client.Disconnect(250)
	}
	m.cancel()
	fmt.Println("✅ 监控程序已停止")
}

func isNumeric(s string) bool {
	_, err := strconv.ParseInt(s, 10, 64)
	return err == nil
}

func main() {
	fmt.Println("🔍 MQTT 系统监控程序")
	fmt.Println(strings.Repeat("=", 60))

	// 创建监控实例
	monitor := NewMQTTMonitor()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动监控
	if err := monitor.Start(); err != nil {
		log.Fatalf("❌ 启动监控失败: %v", err)
	}

	// 等待中断信号
	<-sigChan

	// 停止监控
	monitor.Stop()
}
