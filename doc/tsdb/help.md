

## 删除数据


```sh
influx delete \
  --bucket ebox \
  --start 2024-08-25T04:30:00Z \
  --stop 2024-08-25T04:55:00Z \
  --predicate 'ssn="430621EF03"'
```


```sh
influx delete \
  --bucket ebox \
  --start 2025-01-20T04:30:00Z \
  --stop 2025-01-23T08:00:00Z \
  --predicate 'ssn="0075663710"'
```

```
./influx delete \
  --bucket ebox\
  --start 2025-01-20T04:30:00Z \
  --stop 2025-01-23T08:00:00Z \
  --predicate 'ssn="4408834238"'

./influx delete \
  --bucket ebox \
  --start 2025-01-20T04:30:00Z \
  --stop 2025-01-23T08:00:00Z \
  --predicate 'ssn="4408835453"'

./influx delete \
  --bucket ebox \
  --start 2025-01-20T04:30:00Z \
  --stop 2025-01-23T08:00:00Z \
  --predicate 'ssn="4408838235"'

```



```sh
# 删除一段时间的异常数据，注意时间是 UTC 时间，CN 的时间 -8h
influx delete \
  --bucket ebox \
  --start 2024-09-26T15:00:00Z \
  --stop 2024-11-18T11:00:00Z \
  --predicate 'ssn="0075685515"'
```

指定 measurement 是否可行？
```
influx delete \
  --bucket ebox \
  --start 2024-09-26T15:00:00Z \
  --stop 2024-11-18T11:00:00Z \
  --predicate '_measurement="your_measurement_name" AND ssn="0075685515"'
```


## 在 influxdb 的管理后台执行 flux 语句

把数据从 ebox 迁移到 new_ebox，只取最近 6 个月的数据，并修改  `_measurement` 名字去掉 `ebox_` 前缀

```
from(bucket: "ebox")
  |> range(start: 0)
  |> filter(fn: (r) => r._measurement == "ebox_rain")
  |> map(fn: (r) => ({ r with _measurement: "rain" }))
  |> to(bucket: "new_ebox", org: "beithing")


from(bucket: "ebox")
  |> range(start: 0)
  |> filter(fn: (r) => r._measurement == "ebox_water")
  |> map(fn: (r) => ({ r with _measurement: "water" }))
  |> to(bucket: "new_ebox", org: "beithing")

from(bucket: "ebox")
  |> range(start: 0)
  |> filter(fn: (r) => r._measurement == "ebox_image")
  |> map(fn: (r) => ({ r with _measurement: "image" }))
  |> to(bucket: "new_ebox", org: "beithing")

```