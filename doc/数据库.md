
>暂时以mysql为例。通过dao做接口封装，后期可以mock或者转 pg
>
> 

#### 登录数据库

```sql
mysql -h 127.0.0.1 -uroot -p
```

#### 创建数据库

```sql
create database dayu default character set utf8mb4 collate utf8mb4_unicode_ci;
```


## 数据库备份

```shell
#!/bin/bash

port=3306

cur_date="`date +%Y.%m.%d.%H`"

echo $cur_date
mkdir $cur_date

#mysqldump -h127.0.0.1  -uroot -p123456 dayu > bak/$cur_date/dayu.sql
mysqldump -h127.0.0.1  -uroot -p123456 dayu --tables user product dict firmware device > bak/$cur_date/data.sql

#tar -zcvf dayu-$cur_date.tar.gz $cur_date


```