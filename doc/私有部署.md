
## 需求

- 私有部署的时候，希望程序有一定的保护作用，不被拿去二次部署。
- 支持内网部署，但是部署的时候，可以访问外网。
- 方便使用。部署的时候，执行程序，向后台管理端请求 license。部署结束之后，程序可以离线校验 license。

工作流程

- 假设客户端部署的服务是 A，后台管理端是 B
- 在 B 上添加新的私有部署，主要是填写 deploy_id。
- A 启动，带上 deploy_id、本机硬件信息，调用 B 的接口，获取到 license（jwt token 的方式编码了一些信息）
- A 启动的时候，解码 license，校验deploy_id、硬件信息签名。如果校验不通过，则系统无法正常工作。


## 实现方案

在部署的时候，提交所在设备的唯一信息（比如 CPUID、DISK UUID 、公网 IP 地址等），后台通过方法生成一个授权 license。

在运行的时候，根据提交的唯一信息，以及授权 license，校验是否合法。如果合法则运行，不合法则退出。

### 在宿主机获取 dmi 信息

docker 部署的时候，通过 dmidecode 获取运行环境的信息，也可以获取到运行环境所在外网 IP（非必要，可能内网部署）。
dmidecode 是一个 Linux 下的命令行工具，用来读取计算机的 DMI (Desktop Management Interface) 表，DMI 表记录了系统硬件的详细信息，如 BIOS 信息、主板、处理器、内存、系统序列号、UUID 等。这些信息通常由系统 BIOS 提供。
dmidecode 解析这些 DMI 数据并以人类可读的方式显示出来。它适用于系统管理员或开发人员来查看系统的硬件配置，而不需要打开机器或访问硬件手册。


```yaml
volumes:
      - "$PWD/data:/app"
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - $PWD/data/deploy/dmidecode:/usr/sbin/dmidecode:rw  ## 这里需要使用alpine的dmidecode
      - type: bind
        source: /dev/mem
        target: /dev/mem
privileged: true
```
- /sys/class/dmi/id/bios_vendor：计算机BIOS的制造商。
- /sys/class/dmi/id/chassis_type：机箱类型。
- /sys/class/dmi/id/chassis_vendor：机箱制造商。
- /sys/class/dmi/id/product_name：计算机产品名称。
- /sys/class/dmi/id/product_serial：计算机产品序列号。
- /sys/class/dmi/id/product_uuid：计算机产品的UUID（通用唯一识别码）。


```shell
u200@NUC11TNKi5:~/deploy/ebox$ sudo dmidecode -t
  # system
  # baseboard
  # processor


# CPUID
dmidecode -t processor | grep ID |sort -u |awk -F': ' '{print $2}'
# C1 06 08 00 FF FB EB BF

# system
dmidecode -t system

# 可以读取这些行
# Manufacturer: Intel(R) Client Systems
# Product Name: NUC11TNKi5
# Serial Number: BTTN143004VD
# UUID: 37a32938-a6f0-f0dc-8dfd-48210b25caf2
# SKU Number: BNUC11TNKi50000/1/2


# baseboard
dmidecode -t baseboard 

# Manufacturer: Intel Corporation
# Product Name: NUC11TNBi5
# Version: M11904-403
# Serial Number: BTTN143004VD


# DISK ID
fdisk -l |grep "Disk identifier" |awk {'print $3'}
# 1DCDA2EC-27AD-404B-90DD-8CB1C3C1C393


# machine-id
# /etc/machine-id 是一个 Linux 系统中的文件，通常用于存储系统的唯一标识符（UUID），该 ID 通常在系统首次安装或启动时生成，并且在后续的系统使用过程中保持不变。该文件中的内容可以用来标识系统的唯一性，类似于硬件的唯一 ID。
cat /etc/machine-id
# 88054445b3624dffb9012873871ea20d

# hostname
cat /etc/hostname


```
或者直接用 proc 文件系统：

```shell
sudo ls /sys/class/dmi/id/
bios_date     bios_version     board_serial   chassis_asset_tag  chassis_vendor   power		  product_serial  product_version  uevent
bios_release  board_asset_tag  board_vendor   chassis_serial	 chassis_version  product_family  product_sku	  subsystem
bios_vendor   board_name       board_version  chassis_type	 modalias	  product_name	  product_uuid	  sys_vendor
```

读取程序

```go
func readProc(filename string)string{
    fileContent, err := os.ReadFile(filename)
	if err != nil {
		return ""
	}
    return string(fileContent)
}
```

