#!/usr/bin/env python3
# coding=utf-8

import requests
import sys


defaultSecret = 'r5ld09ngxltR9DGnUmiFT96iTV3brMKU'

def GET(url, method='GET', headers=None, params=None, data=None):
    host = "http://*************:55080"
    url = host + url
    default_params = {'secret': defaultSecret}
    if params:
        default_params.update(params)
    try:
        response = requests.request(method, url, headers=headers, params=default_params, data=data)
        # 检查是否成功接收响应
        print(response.text)
    except Exception as e:
        print("程序异常", e)


def func_basic():
    # test_api("/index/api/getApiList", method="GET")
    # test_api("/index/api/getAllSession")
    # test_api("/index/api/getServerConfig", method="GET")
    # test_api("/index/api/getMediaList", method="GET", params={'schema': 'rtmp'})
    GET("/index/api/getMediaList", method="GET")


def func_get_player_list():
    GET("/index/api/getMediaPlayerList",params={
        "schema":"rtmp",
        "vhost": "__defaultVhost__",
        "app":"live",
        "stream":"test001",
    })

def func_version():
    GET("/index/api/version")

def func_add_stream_proxy():
# stream 需保证唯一
    GET("/index/api/addStreamProxy", params={
        "vhost": "__defaultVhost__",
        "app":"live",
        "stream":"test001",
        "url":"rtsp://admin:test2024@************:554/Streaming/Channels/101",
        "enable_rtmp":True,
        "enable_rtsp":True,
        "rtmp_demand":True,
        # "auto_close":True
        })

# 返回
# ➜  zlmtest ./run.sh
# {
# 	"code" : 0,
# 	"data" :
# 	{
# 		"key" : "__defaultVhost__/live/test"
# 	}
# }

# 关闭拉流代理
def func_del_stream_proxy():
    GET("/index/api/delStreamProxy", params={
        "key" : "__defaultVhost__/live/test",
    })


# 获取一个截图地址，直接在浏览器可以下载截图
def func_get_snap_url():
    url = "/index/api/getSnap"
    host = "http://*************:55080"
    base_url = host + url

    params={
        "timeout_sec":3,
        "expire_sec":3,
        "url":"rtsp://admin:test2024@************:554/Streaming/Channels/101",
    }
    

    default_params = {'secret': defaultSecret}
    

    if params:
        default_params.update(params)

    query_string = '&'.join([f"{key}={value}" for key, value in default_params.items()])
    result =  f"{base_url}?{query_string}"
    print(result)


def func_get_rtp_info():
    GET("/index/api/getRtpInfo", params={
        "stream_id" : "DAD123",  # RTP 的 ssrc，16 进制字符串或者是流的 id(openRtpServer 接口指定)
    })


# 创建 GB28181 RTP 接收端口，如果该端口接收数据超时，则会自动被回收(不用调用 closeRtpServer 接口)
def func_open_rtp_server():
    GET("/index/api/openRtpServer", params={
        "port" : 0,  # 接收端口，0 则为随机端口
        "tcp_mode":1, # 0 udp 模式，1 tcp 被动模式, 2 tcp 主动模式。 (兼容 enable_tcp 为 0/1)
        "stream_id":"DAD123", # 该端口绑定的流 ID，该端口只能创建这一个流(而不是根据 ssrc 创建多个)
    })


def func_list_rtp_server():
    GET("/index/api/listRtpServer")



def func_close_rtp_server():
    GET("/index/api/closeRtpServer", params={
        "stream_id" : "DAD123",  # RTP 的 ssrc，16 进制字符串或者是流的 id(openRtpServer 接口指定)
    })


def func_restart_server():
    GET("/index/api/restartServer")


def func_start_record():
    GET("/index/api/startRecord", params={
        "type" : 1, #0 为 hls，1 为 mp4
        "vhost": '__defaultVhost__',
        "app":"live",
        "stream":"test001",
    })


def func_stop_record():
    GET("/index/api/stopRecord", params={
        "type" : 1, #0 为 hls，1 为 mp4
        "vhost": '__defaultVhost__',
        "app":"live",
        "stream":"test001",
    })

def func_status_record():
    GET("/index/api/isRecording", params={
        "type" : 1, #0 为 hls，1 为 mp4
        "vhost": '__defaultVhost__',
        "app":"live",
        "stream":"test001",
    })

# 执行主函数
if __name__ == "__main__":
    if len(sys.argv) > 1:
        print("--------------------- 1")
        if sys.argv[1] == 'rtp_start':
            func_open_rtp_server()
        elif sys.argv[1] == 'rtp_stop':
            func_close_rtp_server()
        elif sys.argv[1] == "rtp_list":
            func_list_rtp_server()
        elif sys.argv[1] == "rtp_get":
            func_get_rtp_info()
        elif sys.argv[1] == "record_start":
            func_start_record()
        elif sys.argv[1] == "record_stop":
            func_stop_record()
        elif sys.argv[1] == "record_status":
            func_status_record()

    else:
        print("--------------------- 2")
        func_version()
        func_basic()
        # func_add_stream_proxy()

        
        

    