package main

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"io"
	"net"
	"os"
	"time"
)

var done chan int

func init() {
	done = make(chan int, 10000)
}

func sendRtpFile(fileName, protocol, addr string, n int) {
	defer func() {
		err := recover()
		if err != nil {
			fmt.Println("sendRtpFile err:", err)
		}
		done <- 1
	}()

	switch protocol {
	case "tcp":
		//
	case "udp":
		//
	default:
		fmt.Println("Protocol error:", protocol)
		return
	}

	f, err := os.Open(fileName)
	if err != nil {
		fmt.Println("Open file err:", err)
		return
	}

	defer f.Close()

	conn, err := net.Dial(protocol, addr)
	if err != nil {
		fmt.Printf("%s server err: %s\n", protocol, err)
		return
	}

	defer conn.Close()

	buf := bufio.NewWriter(conn)

	for i := 0; i < n; i++ {
		sizeBuf := make([]byte, 2)
		n, err := f.Read(sizeBuf)

		for n == 2 && err == nil {
			size := int(binary.BigEndian.Uint16(sizeBuf))
			if size == 0 {
				fmt.Println("Read size error!")
				break
			}

			if protocol == "tcp" {
				buf.Write(sizeBuf)
			}

			io.CopyN(buf, f, int64(size))
			buf.Flush()
			time.Sleep(2000 * time.Microsecond)
			n, err = f.Read(sizeBuf)
		}
		fmt.Println("Number of reads:", i+1)
		f.Seek(0, 0)
	}
}

func main() {
	// rtp文件
	fileName := "/data/rtp/gb_play_37021100002005000101_34020000001320000011.rtp"
	// 主机
	host := "127.0.0.1"
	// 起始端口 30000,30002,30004,...
	port := 30000
	// 模拟总的路数
	coroutines := 300
	// 使用的协议
	protocol := "udp"

	for i := 0; i < coroutines; i++ {
		go sendRtpFile(fileName, protocol, fmt.Sprintf("%s:%d", host, port), 1)
		time.Sleep(10 * time.Millisecond)
		port += 2
	}

	for i := 0; i < coroutines; i++ {
		<-done
	}
	fmt.Println("exit.")
}
