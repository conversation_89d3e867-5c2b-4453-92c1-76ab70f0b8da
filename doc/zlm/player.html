<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>



    <script src="https://cdn.bootcss.com/flv.js/1.5.0/flv.min.js"></script>
    <video id="videoElement" style="height: auto;width: 800px;"></video>
    <button class="btn">播放FLV</button>
    <script>
        if (flvjs.isSupported()) {
            var videoElement = document.getElementById('videoElement');
            var flvPlayer = flvjs.createPlayer({
                type: 'flv',
                url: 'http://*************:55080/live/test001.live.flv'
            });
            flvPlayer.attachMediaElement(videoElement);
            flvPlayer.load();


            // flvPlayer.play();
            document.querySelector('.btn').addEventListener('click', () => {
                flvPlayer.play();
            })
        }
    </script>

</body>

</html>