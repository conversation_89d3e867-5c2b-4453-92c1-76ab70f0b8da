

## wvp-pro

注意要先编译前端


```
1. 编译前端页面
cd wvp-GB28181-pro/web_src/
npm --registry=https://registry.npm.taobao.org install
npm run build


2. 先将wvp的api的认证去掉
找到 WebSecurityConfig.java 文件(一般在src/main/java/com/genersoft/iot/vmp/conf/security目录下)  
将配置拦截规则中的 // .anyRequest().authenticated() 注释掉
```java
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.headers().contentTypeOptions().disable()
                .and().cors().configurationSource(configurationSource())
                .and().csrf().disable()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)

                // 配置拦截规则
                .and()
                .authorizeRequests()
                .requestMatchers(CorsUtils::isPreFlightRequest).permitAll()
                .antMatchers(userSetting.getInterfaceAuthenticationExcludes().toArray(new String[0])).permitAll()
                .antMatchers("/api/user/login", "/index/hook/**","/index/hook/abl/**", "/swagger-ui/**", "/doc.html").permitAll()
                // .anyRequest().authenticated() // 去掉api的认证
                // 异常处理器
                .and()
                .exceptionHandling()
                .authenticationEntryPoint(anonymousAuthenticationEntryPoint)
                .and().logout().logoutUrl("/api/user/logout").permitAll()
                .logoutSuccessHandler(logoutHandler)
        ;
        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

    }
```


3. 编译后端
cd wvp-GB28181-pro
mvn package

4. 配置后端
cd wvp-GB28181-pro/target
cp ../src/main/resources/application-dev.yml application.yml 


运行
java -jar wvp-pro-*.jar 
```

对于 docker 部署，需先创建数据库

```mysql
CREATE DATABASE wvp2 CHARACTER SET utf8 COLLATE utf8_general_ci;
```


```
收流超时一般有一下几个原因，按照常见程度排序，可以参考着排查下：

wvp和zlm都部署在公网服务，但是media.ip只配置了一个内网的ip，导致发送给设备的收流IP是内网IP，设备发流时就指向了内网IP，导致服务端无法收到流。解决办法： 配置media下的sdp-ip和steam-ip为公网IP。sdp-ip不支持域名，stream-ip支持。
wvp和zlm都部署在公网服务，sdp-ip和steam-ip为公网IP，但是收流的端口段没有在公网的入栈协议中开放，导致服务端无法收到流。解决办法：开放收流端口段，对应的值为media下的port-range，tcp+udp都开。
wvp和zlm都部署在公网服务，sdp-ip和steam-ip为公网IP，收流的端口段在公网的入栈协议也开放，但是服务器的系统里开启防火墙。导致服务端无法收到流。解决办法：关闭防火墙或者在防火墙配置端口段，对应的值为media下的port-range，tcp+udp都开。
wvp部署在内网，zlm部署在公网，导致zlm收到流无法发送hook消息给wvp，导致收流超时，解决办法，zlm和wvp都部署到局域网，如果需要公网观看视频，可以在公网再搭建一套，通过国标级联共享到公网观看。
media.ip使用公网IP也有可能导致，无法调用zlm接口而点播失败，有的局域网服务器不能访问自己的公网IP。解决办法：media.ip使用局域网IP，如果同一台服务器，最好使用127.0.0.1,同时配置sdp-ip和steam-ip。
排除以上的情况后仍然收留超时，考虑一些其他的网络情况：

设备使用4G网络接入网络，4G网络不稳定，导致点播消息没有下发到设备，大概率出现点播超时。
设备所在的局域网使用对称NAT，导致点播消息没有下发到设备，大概率出现点播超时。
流传输模式使用了TCP主动，但是服务端无法连接到设备端，导致收流超市。
其他的情况暂时想不到了。后续想到再更新。
```




## 问题


应该是 ms 的 ip 和端口，设备 ID、通道 ID、后缀，组合成媒体地址



web 端主要是这个接口有可能出问题，现象就是，看日志zlm 那边已经提示注册视频流成功，生成了播放地址。

```
2024-06-14 00:51:44.698 I [MediaServer] [2601502-event poller 3] MediaSource.cpp:522 emitEvent | 媒体注册:fmp4://__defaultVhost__/rtp/34020000001320000001_34020000001320000001
2024-06-14 00:51:44.698 I [MediaServer] [2601502-event poller 3] MultiMediaSourceMuxer.cpp:561 onAllTrackReady | stream: rtp://__defaultVhost__/rtp/34020000001320000001_34020000001320000001 , codec info: H264[1920/1080/25]
2024-06-14 00:51:44.698 I [MediaServer] [2601502-event poller 3] MediaSource.cpp:522 emitEvent | 媒体注册:rtsp://__defaultVhost__/rtp/34020000001320000001_34020000001320000001
2024-06-14 00:51:44.737 D [MediaServer] [2601502-event poller 1] MediaSource.cpp:461 operator() | 收到媒体注册事件,回复播放器:rtmp://__defaultVhost__/rtp/34020000001320000001_34020000001320000001
2024-06-14 00:51:44.738 I [MediaServer] [2601502-event poller 3] MediaSource.cpp:522 emitEvent | 媒体注册:rtmp://__defaultVhost__/rtp/34020000001320000001_34020000001320000001
2024-06-14 00:51:44.740 I [MediaServer] [2601502-event poller 3] MediaSource.cpp:522 emitEvent | 媒体注册:ts://__defaultVhost__/rtp/34020000001320000001_34020000001320000001
2024-06-14 00:51:46.727 I [MediaServer] [2601502-event poller 3] MediaSource.cpp:522 emitEvent | 媒体注册:hls://__defaultVhost__/rtp/34020000001320000001_34020000001320000001
2024-06-14 00:52:16.226 W [MediaServer] [2601502-event poller 1] HttpSession.cpp:157 onError | 51-76(***************:64391) FLV/TS/FMP4播放器(__defaultVhost__/rtp/34020000001320000001_34020000001320000001)断开:255(socket send timeout),耗时(s):39
```

但是 wvp2 这个请求就是返回不了：



此时手动拼接播放地址，在 jessibuca 里面播放是可以的。  播放地址： http://jessibuca.monibuca.com/player.html

```
在 wvp 管理后台，是这个地址。*************是 zlm 的公网 ip
http://*************:55080/rtp/34020000001320000001_34020000001320000001.live.flv

在 nuc 主页上是这个地址
wss://nuc.beithing.com:60080/rtp/34020000001320000001_34020000001320000001.live.flv
```

## 截图请求

http://*************:60091/api/device/query/snap/34020000001320000001/34020000001320000001

返回值是一个 image/png，请求方可以定期保存

## paly start 接口
接口请求正常的是这样的：


```
请求网址:
http://***************:60091/api/play/start/34020000001320000003/34020000001320000003
请求方法:
GET
状态代码:
200 OK



{
    "code": 0,
    "msg": "成功",
    "data": {
        "app": "rtp",
        "stream": "34020000001320000003_34020000001320000003",
        "ip": null,
        "flv": "http://***************:55080/rtp/34020000001320000003_34020000001320000003.live.flv",
        "https_flv": "https://***************:55443/rtp/34020000001320000003_34020000001320000003.live.flv",
        "ws_flv": "ws://***************:55080/rtp/34020000001320000003_34020000001320000003.live.flv",
        "wss_flv": "wss://***************:55443/rtp/34020000001320000003_34020000001320000003.live.flv",
        "fmp4": "http://***************:55080/rtp/34020000001320000003_34020000001320000003.live.mp4",
        "https_fmp4": "https://***************:55443/rtp/34020000001320000003_34020000001320000003.live.mp4",
        "ws_fmp4": "ws://***************:55080/rtp/34020000001320000003_34020000001320000003.live.mp4",
        "wss_fmp4": "wss://***************:55443/rtp/34020000001320000003_34020000001320000003.live.mp4",
        "hls": "http://***************:55080/rtp/34020000001320000003_34020000001320000003/hls.m3u8",
        "https_hls": "https://***************:55443/rtp/34020000001320000003_34020000001320000003/hls.m3u8",
        "ws_hls": "ws://***************:55080/rtp/34020000001320000003_34020000001320000003/hls.m3u8",
        "wss_hls": "wss://***************:55443/rtp/34020000001320000003_34020000001320000003/hls.m3u8",
        "ts": "http://***************:55080/rtp/34020000001320000003_34020000001320000003.live.ts",
        "https_ts": "https://***************:55443/rtp/34020000001320000003_34020000001320000003.live.ts",
        "ws_ts": "ws://***************:55080/rtp/34020000001320000003_34020000001320000003.live.ts",
        "wss_ts": null,
        "rtmp": "rtmp://***************:1935/rtp/34020000001320000003_34020000001320000003",
        "rtmps": null,
        "rtsp": "rtsp://***************:554/rtp/34020000001320000003_34020000001320000003",
        "rtsps": null,
        "rtc": "http://***************:55080/index/api/webrtc?app=rtp&stream=34020000001320000003_34020000001320000003&type=play",
        "rtcs": "https://***************:55443/index/api/webrtc?app=rtp&stream=34020000001320000003_34020000001320000003&type=play",
        "mediaServerId": "zlm-local",
        "mediaInfo": {
            "app": "rtp",
            "stream": "34020000001320000003_34020000001320000003",
            "mediaServer": {
                "id": "zlm-local",
                "ip": "zlmedia",
                "hookIp": "wvp2",
                "sdpIp": "***************",
                "streamIp": "***************",
                "httpPort": 55080,
                "httpSSlPort": 55443,
                "rtmpPort": 1935,
                "flvPort": 55080,
                "flvSSLPort": 55443,
                "wsFlvPort": 55080,
                "wsFlvSSLPort": 55443,
                "rtmpSSlPort": 0,
                "rtpProxyPort": 10000,
                "rtspPort": 554,
                "rtspSSLPort": 0,
                "autoConfig": true,
                "secret": "r5ld09ngxltR9DGnUmiFT96iTV3brMKU",
                "hookAliveInterval": 10.0,
                "rtpEnable": true,
                "status": true,
                "rtpPortRange": "50000,50050",
                "sendRtpPortRange": "50050,50100",
                "recordAssistPort": 18081,
                "createTime": "2024-05-18 18:30:39",
                "updateTime": "2024-06-14 00:54:27",
                "lastKeepaliveTime": null,
                "defaultServer": true,
                "recordDay": 7,
                "recordPath": "",
                "type": "zlm"
            },
            "schema": "rtsp",
            "readerCount": 0,
            "videoCodec": "H265",
            "width": 1920,
            "height": 1080,
            "audioCodec": null,
            "audioChannels": null,
            "audioSampleRate": null,
            "duration": null,
            "online": true,
            "originType": 3,
            "aliveSecond": 0,
            "bytesSpeed": 0,
            "callId": "4ed5500a49d8feee1405a71c8fcad00f@0.0.0.0"
        },
        "startTime": null,
        "endTime": null,
        "downLoadFilePath": null,
        "progress": 0.0
    }
}

```
