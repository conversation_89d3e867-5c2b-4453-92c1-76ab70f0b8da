## ZLM 使用 docker


```
docker pull zlmediakit/zlmediakit:master

查看镜像历史
docker image  history  zlmediakit/zlmediakit:master
```

使用 docker 部署 zlm 的时候，注意：
拉流地址的拼接，貌似是通过mvp 对media_server的 ip 和端口来完成的。在 mvp 的容器里面通过访问 zlm 服务名和内部端口，会导致拼接的地址是内部端口。
所以建议：zlm 内部不要用 80 端口。内部和外部端口隐射一样。避免了容器内和容器外的混淆。


```
暴露端口	内部端口	配置说明
8180	80	流媒体内部的HTTP服务，用于提供接口供平台调用
443	443	HTTPS服务端口
9100	10000	TCP/UDP 端口，用于传输动态 RTP 流
10001-10005	10001-10005	多个动态端口，每个摄像头使用一个端口，用于传输 RTP 流，若摄像头路数较多请自行调大范围值
```



## ZLM编译和运行



```
# 国内用户推荐从 Gitee 下载
git clone --depth 1 https://gitee.com/xia-chu/ZLMediaKit
cd ZLMediaKit
# 初始化子模块 （必须执行）
git submodule update --init

安装依赖
sudo apt install libssl-dev
sudo apt install libsdl-dev
sudo apt install libavcodec-dev
sudo apt install libavutil-dev
sudo apt install ffmpeg

编译
cd ZLMediaKit
mkdir build
cd build
cmake ..
make -j4

运行
cd ZLMediaKit/release/linux/Debug
# 通过 -h 可以了解启动参数
./MediaServer -h
# 以守护进程模式启动
./MediaServer -d &

```




## 推流

- rtsp方式推流

```
# h264推流
ffmpeg -re -i "/path/to/test.mp4" -vcodec h264 -acodec aac -f rtsp -rtsp_transport tcp rtsp://127.0.0.1/live/test
# h265推流
ffmpeg -re -i "/path/to/test.mp4" -vcodec h265 -acodec aac -f rtsp -rtsp_transport tcp rtsp://127.0.0.1/live/test


# 播放地址

rtsp://*************/live/test22  // 默认 554端口
rtmp://*************/live/test22  // 默认 rtmp 是 1935端口

```



- rtmp方式推流

```
#如果未安装FFmpeg，你也可以用obs推流
ffmpeg -re -i "/path/to/test.mp4" -vcodec h264 -acodec aac -f flv rtmp://127.0.0.1/live/test
# RTMP标准不支持H265,但是国内有自行扩展的，如果你想让FFmpeg支持RTMP-H265,请按照此文章编译：https://github.com/ksvc/FFmpeg/wiki/hevcpush
```



- rtp方式推流

```
# h264推流
ffmpeg -re -i "/path/to/test.mp4" -vcodec h264 -acodec aac -f rtp_mpegts rtp://127.0.0.1:10000
# h265推流
ffmpeg -re -i "/path/to/test.mp4" -vcodec h265 -acodec aac -f rtp_mpegts rtp://127.0.0.1:10000
```





## 播放地址



ZLMediaKit中的流媒体源

```
以rtsp://somedomain.com:554/live/0?token=abcdefg&field=value为例,该url分为以下几个部分：

协议(scheam) : rtsp协议,默认端口554
虚拟主机(vhost) : somedomain.com,该字段既可以是域名也可以是ip，如果是ip则对应的虚拟主机为__defaultVhost__
服务端口号(port) : 554,如果不指定端口号，则使用协议默认端口号
应用名(app) : live
流ID(streamid) : 0
参数(args) : token=abcdefg&field=value
```



在ZLMediaKit中，流媒体源是一种可以被用于直播转发、推流转发等功能的数据对象，在本项目中被称作为`MediaSource`，目前支持5种类型的流媒体源，分别是`RtspMediaSource`、`RtmpMediaSource`、`HlsMediaSource`、`TSMediaSource`、`FMP4MediaSource`。

定位一个流媒体源，主要通过4个元素(我们后续称其为4元组)，分别是:

- `协议(scheam)`
- `虚拟主机(vhost)`
- `应用名(app)`
- `流ID(streamid) `

`RtspMediaSource`支持 rtsp播放、rtsp推流、webrtc播放、webrtc推流。

`RtmpMediaSource`支持 rtmp推流/播放、http-flv播放、ws-flv播放。

`HlsMediaSource`支持 hls播放。

`TSMediaSource` 支持 http-ts播放、ws-ts播放。

`FMP4MediaSource` 支持 http-fmp4播放、ws-fmp4播放。





假定有一个`RtspMediaSource`，它的4元组分别为 `rtsp(RtspMediaSource固定为rtsp)`、`somedomain.com`、`live`、`0` 那么播放这个流媒体源的url对应为:

- `rtsp://somedomain.com/live/0`
- `rtsps://somedomain.com/live/0`
- `rtsp://127.0.0.1/live/0?vhost=somedomain.com`
- `rtsps://127.0.0.1/live/0?vhost=somedomain.com`



如果有一个`RtmpMediaSource`，它的4元组分别为 `rtmp(RtmpMediaSource固定为rtmp)`、`somedomain.com`、`live`、`0` 那么播放这个流媒体源的url对应为:

- `rtmp://somedomain.com/live/0`
- `rtmps://somedomain.com/live/0`
- `rtmp://127.0.0.1/live/0?vhost=somedomain.com`
- `rtmps://127.0.0.1/live/0?vhost=somedomain.com`

http-flv 和 websocket-flv 的地址：

- `http://somedomain.com/live/0.live.flv`
- `https://somedomain.com/live/0.live.flv`
- `http://127.0.0.1/live/0.live.flv?vhost=somedomain.com`
- `https://127.0.0.1/live/0.live.flv?vhost=somedomain.com`
- `ws://somedomain.com/live/0.live.flv`
- `wss://somedomain.com/live/0.live.flv`
- `ws://127.0.0.1/live/0.live.flv?vhost=somedomain.com`
- `wss://127.0.0.1/live/0.live.flv?vhost=somedomain.com`





## 国标推流


### 固定收流接口 10000



ZLMediaKit支持GB28181的 ps-rtp推流，支持的编码格式分别为 `h264/h265/aac/g711/opus`。 在收到GB28181推流后，ZLMediaKit会依次做以下事情：

- rtp排序去重。
- rtp解析成ps或ts。
- ps或ts解析成`h264/h265/aac/g711/opus`。
- 输入到复用器，生成rtsp/rtmp/ts/fmp4等格式，以便转换成其他协议或容器。



ZLMediaKit默认开启10000端口用于接收UDP/TCP的GB28181推流，由于国标推流不好测试，ZLMediaKit同时也支持rtp_mpegts推流，代码会自适应判断是否为ps还是ts。 所以如果大家没有摄像头的情况下，可以用FFmpeg简单测试，基本上体验跟国标推流并无二致。

- ffmpeg推流命令:

```
 ffmpeg -re -i www/record/robot.mp4 -vcodec h264 -acodec aac -f rtp_mpegts rtp://127.0.0.1:10000
```



```
zlmedia  | 2024-05-14 15:49:33.764 D [MediaServer] [1-event poller 7] MediaSink.cpp:162 emitAllTrackReady | All track ready use 109ms
zlmedia  | 2024-05-14 15:49:33.764 T [MediaServer] [1-event poller 7] MediaSink.cpp:280 addMuteAudioTrack | Mute aac track added
zlmedia  | 2024-05-14 15:49:33.765 I [MediaServer] [1-event poller 7] MediaSource.cpp:517 emitEvent | 媒体注册:fmp4://__defaultVhost__/rtp/E7D1F786
zlmedia  | 2024-05-14 15:49:33.765 I [MediaServer] [1-event poller 7] MultiMediaSourceMuxer.cpp:561 onAllTrackReady | stream: rtp://__defaultVhost__/rtp/E7D1F786 , codec info: mpeg4-generic[8000/1/16] H264[1920/1080/25]
zlmedia  | 2024-05-14 15:49:33.765 I [MediaServer] [1-event poller 7] MediaSource.cpp:517 emitEvent | 媒体注册:rtmp://__defaultVhost__/rtp/E7D1F786
zlmedia  | 2024-05-14 15:49:33.765 I [MediaServer] [1-event poller 7] MediaSource.cpp:517 emitEvent | 媒体注册:rtsp://__defaultVhost__/rtp/E7D1F786
zlmedia  | 2024-05-14 15:49:33.765 I [MediaServer] [1-event poller 7] MediaSource.cpp:517 emitEvent | 媒体注册:ts://__defaultVhost__/rtp/E7D1F786
zlmedia  | 2024-05-14 15:49:33.765 T [MediaServer] [1-event poller 7] Stamp.cpp:124 revise_l | Relative stamp changed: 0 -> 120
zlmedia  | 2024-05-14 15:49:33.843 T [MediaServer] [1-event poller 7] Stamp.cpp:124 revise_l | Relative stamp changed: 160 -> 232
zlmedia  | 2024-05-14 15:49:33.843 T [MediaServer] [1-event poller 7] Stamp.cpp:129 revise_l | Relative stamp changed: 0 -> 8
```

rtmp 播放地址是

```
rtmp://*************/rtp/E7D1F786
```



这个推流的rtp ssrc为 E7D1F786 (16进制打印)



需要指出的是，国标推流的app固定为rtp，你只能通过代码来修改它，stream_id为rtp流的ssrc，这个是随机的，在FFmpeg中貌似没法控制。

另外，每次推流时，请更换ssrc，否则ZLMediaKit发现推流端ip和端口变化后，会直接丢弃rtp包(现象如此[issue](https://github.com/xia-chu/ZLMediaKit/issues/267))；这样做的目的是为了防止两个设备使用同一个ssrc推流时互相干扰。



### 动态收流接口

在推流给10000端口时，您可能发现有个缺陷，就是stream_id是ssrc，比较抽象，可能还没法控制。

那么我们能否自定义stream_id? 

答案是肯定的，ZLMediaKit通过[restful api](https://github.com/xia-chu/ZLMediaKit/wiki/MediaServer支持的HTTP-API#24indexapiopenrtpserver)可以动态开启国标收流端口(同时支持udp/tcp模式)。

在使用 openRtpServer  接口，指定stream_id， 动态开启国标收流端口后，这个端口只能产生一个流，也就是说，一个摄像头需要一个服务器端口用于接收国标推流。

```
如果openRtpServer接口创建的端口一直没收到流（或者解析不出流），默认 15 秒后，会自动关闭和释放。
```



```
zlmedia  | 2024-05-14 17:46:59.844 D [MediaServer] [1-event poller 5] WebApi.cpp:265 http api debug |
zlmedia  | # request:
zlmedia  | GET /index/api/openRtpServer?secret=r5ld09ngxltR9DGnUmiFT96iTV3brMKU&port=0&tcp_mode=1&stream_id=DAD123
zlmedia  | # header:
zlmedia  | Accept : */*
zlmedia  | Accept-Encoding : gzip, deflate
zlmedia  | Connection : keep-alive
zlmedia  | Host : *************:55080
zlmedia  | User-Agent : python-requests/2.31.0
zlmedia  | # content:
zlmedia  |
zlmedia  | # response:
zlmedia  | {
zlmedia  | 	"code" : 0,
zlmedia  | 	"port" : 30036
zlmedia  | }
zlmedia  |
zlmedia  |
zlmedia  | 2024-05-14 17:46:59.849 T [MediaServer] [1-event poller 5] TcpServer.cpp:173 operator() | 1-33(**********:51013) mediakit::HttpSession on err: 1(end of file)
zlmedia  | 2024-05-14 17:46:59.849 T [MediaServer] [1-event poller 5] TcpServer.h:51 operator() | 1-33(**********:51013) ~mediakit::HttpSession
zlmedia  | 2024-05-14 17:47:10.518 I [MediaServer] [1-event poller 5] RtpProcess.cpp:269 operator() | DAD123(**********:54119) 允许RTP推流
zlmedia  | 2024-05-14 17:47:10.520 I [MediaServer] [1-event poller 5] GB28181Process.cpp:178 onRtpDecode | DAD123 judged to be TS
zlmedia  | 2024-05-14 17:47:10.520 I [MediaServer] [1-event poller 5] Decoder.cpp:143 onTrack | Got track: H264
zlmedia  | 2024-05-14 17:47:10.520 I [MediaServer] [1-event poller 5] Decoder.cpp:97 onStream | Add track finished
zlmedia  | 2024-05-14 17:47:10.632 D [MediaServer] [1-event poller 5] MediaSink.cpp:162 emitAllTrackReady | All track ready use 113ms
zlmedia  | 2024-05-14 17:47:10.632 T [MediaServer] [1-event poller 5] MediaSink.cpp:280 addMuteAudioTrack | Mute aac track added
zlmedia  | 2024-05-14 17:47:10.633 I [MediaServer] [1-event poller 5] MediaSource.cpp:517 emitEvent | 媒体注册:fmp4://__defaultVhost__/rtp/DAD123
zlmedia  | 2024-05-14 17:47:10.633 I [MediaServer] [1-event poller 5] MultiMediaSourceMuxer.cpp:561 onAllTrackReady | stream: rtp://__defaultVhost__/rtp/DAD123 , codec info: mpeg4-generic[8000/1/16] H264[1920/1080/25]
zlmedia  | 2024-05-14 17:47:10.633 I [MediaServer] [1-event poller 5] MediaSource.cpp:517 emitEvent | 媒体注册:rtmp://__defaultVhost__/rtp/DAD123
zlmedia  | 2024-05-14 17:47:10.633 I [MediaServer] [1-event poller 5] MediaSource.cpp:517 emitEvent | 媒体注册:rtsp://__defaultVhost__/rtp/DAD123
zlmedia  | 2024-05-14 17:47:10.633 I [MediaServer] [1-event poller 5] MediaSource.cpp:517 emitEvent | 媒体注册:ts://__defaultVhost__/rtp/DAD123
zlmedia  | 2024-05-14 17:47:10.633 T [MediaServer] [1-event poller 5] Stamp.cpp:124 revise_l | Relative stamp changed: 0 -> 120
zlmedia  | 2024-05-14 17:47:10.707 T [MediaServer] [1-event poller 5] Stamp.cpp:124 revise_l | Relative stamp changed: 160 -> 232
```



推流命令

```
ffmpeg -re -i "./10-56-46-0.mp4" -vcodec h264 -acodec aac -f rtp_mpegts rtp://*************:30036
```





调试文件生成

如果你的MediaServer能收到国标推流，但是未出现`媒体注册`相关日志，那么有可能是流有些异常，你可以修改配置文件`rtp_proxy.dumpDir`指定调试文件导出目录， 这样ZLMediaKit会把国标流导出到该文件夹，就像这样：



如果丢包严重，可以试试修改系统参数：

```
/proc/sys/net/core/rmem_max

/proc/sys/net/core/wmem_max
```

可以改成 4M





## 优化性能

配置文件中，以下配置全部改成1也能提高性能的。转协议比较损耗性能，zlm默认开启一直转协议(而不是按需)

```
[general]
###### 以下是按需转协议的开关，在测试ZLMediaKit的接收推流性能时，请把下面开关置1
###### 如果某种协议你用不到，你可以把以下开关置1以便节省资源(但是还是可以播放，只是第一个播放者体验稍微差点)，
###### 如果某种协议你想获取最好的用户体验，请置0(第一个播放者可以秒开，且不花屏)

#hls协议是否按需生成，如果hls.segNum配置为0(意味着hls录制)，那么hls将一直生成(不管此开关)
hls_demand=0
#rtsp[s]协议是否按需生成
rtsp_demand=0
#rtmp[s]、http[s]-flv、ws[s]-flv协议是否按需生成
rtmp_demand=0
#http[s]-ts协议是否按需生成
ts_demand=0
#http[s]-fmp4、ws[s]-fmp4协议是否按需生成
fmp4_demand=0
```



## wvp-pro对接

1、局域网中，使用按需拉流的方式，拉去摄像机的 rtsp 的流。—— 使用拉流代理。——但是不支持 PTZ。

2、使用 GB28181 的 信令服务，不限于局域网内，让设备推流到 ZLM 的 rtp 端口。



### 局域网按需拉流

1、先配置 ZLM的 hook

这个配置也可以让 ebox 用代码去实现。流程如下：

- 步骤 1：配置zlm 的 ip 地址和http 服务的端口
- 步骤 2：ebox  定时轮询查看 /index/api/version 接口，探测到 zlm 服务正常启动。
- 步骤 3：查询 zlm 的配置，如果没有 hook，则配置 hook 相关信息。
- 步骤 4：通过 hook 接受 zlm 的心跳消息。如果 zlm 掉线，则回到步骤 2。

```
[hook]
alive_interval=10.0
enable=1
on_flow_report=
on_http_access=
on_play=http://*************:8280/api/hook/on_play
on_publish=
on_record_mp4=
on_record_ts=
on_rtp_server_timeout=http://*************:8280/api/hook/on_rtp_server_timeout
on_rtsp_auth=
on_rtsp_realm=
on_send_rtp_stopped=
on_server_exited=http://*************:8280/api/hook/on_server_exited
on_server_keepalive=
on_server_started=http://*************:8280/api/hook/on_server_started
on_shell_login=
on_stream_changed=
on_stream_none_reader=http://*************:8280/api/hook/on_stream_none_reader
on_stream_not_found=http://*************:8280/api/hook/on_stream_not_found
retry=1
retry_delay=3.0
stream_changed_schemas=rtsp/rtmp/fmp4/ts/hls/hls.fmp4
timeoutSec=10

```

相关接口： addStreamProxy

```python
test_api("/index/api/addStreamProxy", params={
    'vhost': '__defaultVhost__',
    "app":"live",
    "stream":"test",
    "url":"rtsp://admin:test2024@************:554/Streaming/Channels/101",
    "enable_rtmp":True,
    "rtmp_demand":True,
    # "auto_close":True
    })
    
```



参数描述

|       参数        | 参数类型 |                             释意                             | 是否必选 |
| :---------------: | :------: | :----------------------------------------------------------: | :------: |
|      secret       | `string` |                  api 操作密钥(配置文件配置)                  |    Y     |
|       vhost       | `string` |          添加的流的虚拟主机，例如`__defaultVhost__`          |    Y     |
|        app        | `string` |                 添加的流的应用名，例如 live                  |    Y     |
|      stream       | `string` |                 添加的流的 id 名，例如 test                  |    Y     |
|        url        | `string` |   拉流地址，例如 rtmp://live.hkstv.hk.lxdns.com/live/hks2    |    Y     |
|    retry_count    |  `int`   |               拉流重试次数，默认为-1 无限重试                |    N     |
|     rtp_type      |  `int`   |        rtsp 拉流时，拉流方式，0：tcp，1：udp，2：组播        |    N     |
|    timeout_sec    |  `int`   |               拉流超时时间，单位秒，float 类型               |    N     |
|   `enable_hls`    |  `bool`  |                  是否转换成 hls-mpegts 协议                  |    N     |
| `enable_hls_fmp4` |  `bool`  |                   是否转换成 hls-fmp4 协议                   |    N     |
|   `enable_mp4`    |  `bool`  |                      是否允许 mp4 录制                       |    N     |
|   `enable_rtsp`   |  `bool`  |                       是否转 rtsp 协议                       |    N     |
|   `enable_rtmp`   |  `bool`  |                     是否转 rtmp/flv 协议                     |    N     |
|    `enable_ts`    |  `bool`  |                  是否转 http-ts/ws-ts 协议                   |    N     |
|   `enable_fmp4`   |  `bool`  |                是否转 http-fmp4/ws-fmp4 协议                 |    N     |
|   `hls_demand`    |  `bool`  |                   该协议是否有人观看才生成                   |    N     |
|   `rtsp_demand`   |  `bool`  |                   该协议是否有人观看才生成                   |    N     |
|   `rtmp_demand`   |  `bool`  |                   该协议是否有人观看才生成                   |    N     |
|    `ts_demand`    |  `bool`  |                   该协议是否有人观看才生成                   |    N     |
|   `fmp4_demand`   |  `bool`  |                   该协议是否有人观看才生成                   |    N     |
|  `enable_audio`   |  `bool`  |                     转协议时是否开启音频                     |    N     |
| `add_mute_audio`  |  `bool`  |            转协议时，无音频是否添加静音 aac 音频             |    N     |
|  `mp4_save_path`  | `string` |             mp4 录制文件保存根目录，置空使用默认             |    N     |
| `mp4_max_second`  |  `int`   |                   mp4 录制切片大小，单位秒                   |    N     |
|  `mp4_as_player`  |  `bool`  |            MP4 录制是否当作观看者参与播放人数计数            |    N     |
|  `hls_save_path`  | `string` |             hls 文件保存保存根目录，置空使用默认             |    N     |
|  `modify_stamp`   |  `int`   | 该流是否开启时间戳覆盖(0:绝对时间戳/1:系统时间戳/2:相对时间戳) |    N     |
|   `auto_close`    |  `bool`  |         无人观看是否自动关闭流(不触发无人观看 hook)          |    N     |



返回值

```
{
	"code" : 0,
	"data" :
	{
		"key" : "__defaultVhost__/live/test"   # 流的唯一标识
	}
}
```



所以，可以直接用下面的方式播放：

```
http://*************:55080/live/test001.live.flv
```



调用接口生成截图链接

```py
test_gen("/index/api/getSnap", params={
    "timeout_sec":3,
    "expire_sec":10,
    "url":"rtsp://admin:test2024@************:554/Streaming/Channels/101",
})
```



得到截图地址

```
http://*************:55080/index/api/getSnap?secret=EdGK5hvwJOoSXHUUNwhbltczhSwdLaD1&timeout_sec=3&expire_sec=10&url=rtsp://admin:test2024@************:554/Streaming/Channels/101
```




2、我们需要添加摄像头信息到数据库：

```
摄像头地址：不同的品牌，摄像头的 rtsp 的地址不同，这个需要明确。

一、海康RTSP协议取流格式
海康威视官方的RTSP最新取流格式（2023.9）如下（车载录像机不支持RTSP取流）：

rtsp://用户名:密码@IP:554/Streaming/Channels/101

末尾101的第一个1表示第几通道，比如：取第2个通道的主码流预览：rtsp://用户名:密码@IP:554/Streaming/Channels/201

最后一个1表示第几码流，比如：

取第1个通道的主码流预览：rtsp://用户名:密码@IP:554/Streaming/Channels/101
取第1个通道的子码流预览：rtsp://用户名:密码@IP:554/Streaming/Channels/102
取第1个通道的第三码流预览：rtsp://用户名:密码@IP:554/Streaming/Channels/103

Tips：网上很多人说url格式为“rtsp://用户名:密码@IP:554/h264/ch1/main/av_stream”，实际上这是2012年之前的海康摄像头的RTSP取流协议格式，现在已经更新为上面的格式。

以我们办公室这台视频监控设备为例

rtsp://admin:test2024@************:554/Streaming/Channels/101

用 VLC 测试可以播放
```



我们需要配置 hook 到 ebox 服务，然后 ebox 也会通过 restful 接口和 ZLM 交互。

对于一个流，有一下字段需要记录：

```
vhost	string	添加的流的虚拟主机，例如__defaultVhost__
app	string   添加的流的应用名，例如 live
stream	string	添加的流的 id 名，例如 test
url	string	拉流地址，例如 rtmp://live.hkstv.hk.lxdns.com/live/hks2
enable_rtsp	bool	是否转 rtsp 协议
enable_rtmp	bool	是否转 rtmp/flv 协议
rtsp_demand	bool	该协议是否有人观看才生成
rtmp_demand	bool	该协议是否有人观看才生成
```



客户端拉流的时候，ZLM 会发现没有这个流，会从拉流地址里面解析出 app 和 stream、vhost，读数据库中读取对应的 rtsp-url，实现动态的拉流。

addStreamProxy 的返回值 key 需保存，不过也是有规则，是 vhost  app  stream组合的。

delStreamProxy 可以通过 key 来删除这个流。


### GB信令方式

需要使用 WVP-pro 配合 ZLM 使用。这款 GB28181 的信令服务器比较成熟。

WVP-PRO通过调用ZLMediaKit的RESTful接口实现对ZLMediaKit行为的控制; ZLMediaKit通过Web Hook 接口把消息通知WVP-PRO。



设备会动态注册到信令服务器上，数据库需要存储设备信息：



## zlm restful api

HTTP层面统一返回200状态码，body统一为json。
body一般为以下样式：

{
    "code" : 0,
    "data":{},
    "msg" : "OK"
}

```
typedef enum {
    Exception = -400,//代码抛异常
    InvalidArgs = -300,//参数不合法
    SqlFailed = -200,//sql执行失败
    AuthFailed = -100,//鉴权失败
    OtherFailed = -1,//业务代码执行失败，
    Success = 0//执行成功
} ApiErr;
```