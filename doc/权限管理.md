## 需求分析
这是一个典型的多租户 saas 平台的需求。


- 当前平台实现三种功能：
    - 物联网应用平台：
        - 根据客户需求，基于数据平台，开发应用。
        - 不同的业务需求，有不同的界面。
        - 需要实现多项目、多角色、多用户的权限管理。
    - 物联网数据中台：
        - 用户管理业务数据：设备类型定义、物模型定义、设备接入
        - 界面和功能固定
        - 支持标准南向协议（MQTT + JSON），如果需要支持非标协议，需要提供协议网关做转换。
        - 支持标准北向接口。
        - 提供数据的接入、存储、分发。或基于此中台做低代码应用开发，如 APP、大屏可视化、组态等。
        - 大多数物联网平台实际上是这样的思路。
    - 物联网设备管理平台：
        - 用户创建产品，管理硬件，包括硬件的升级、配置、日志、故障等。
        - 没有标准协议。和硬件深度耦合。
        - 界面和功能固定。


当然也可以综合以上三种需求，但是设备开发者、数据中台、应用开发者，三者的需求是不耦合的。

- 数据中台关注的是 deviceType，device的概念，围绕着物模型在做。
- 设备管理关注的是 product、hardware、firmware 的概念，围绕着硬件的远程运维需求。
- 应用开发者关注的是 业务相关数据。

三者都有系统管理功能：人员、角色、日志等。

常规的物联网平台，只是我们上述功能的【数据中台】这一部分。所以实现上我们还是不能照搬的。

## 如何使用

对于一个新的客户，我们会添加一个公司，对这个公司授予一个或者多个角色。第一个用户就是公司管理员，可以管理公司内部的资源，包括用户、角色、日志、项目。
进入项目，会有不同的权限：
- 数据管理：包括设备类型、产品定义、设备、硬件、固件、物模型等，这是通常物联网平台负责的事情。
- 设备管理：包括产品、设备、硬件、固件等。按需可选。
- 应用管理：包括应用、数据分析、大屏可视化等，也区分不同的行业应用。按需可选

当然也可以把应用功能独立出来。但是放在一起也有好处，避免维护另外一套软硬件，且复用了很多功能。权限和数据又可以完全隔离开来。


## 租户 vs 项目

老平台的做法
我们老平台是用公司作为资源隔离的载体的。但是也增加了 project 的概念，在公司之下。在项目外面，使用 companyID 作为资源隔离，项目里面的相关资源都是用 projectCode 来隔离的。
这有如下一些问题：
- 我们的菜单、角色是用 companyID 作为资源隔离的。所以一个公司下面的所有项目，共享同样的菜单、角色。对于业务需求开发不是很灵活。
- 对于用户来说，公司下面有设备，项目下面也有设备，容易混淆。


所以在新平台，我们参考老平台的做法，基于 company 表，抽象租户和项目两个概念。如下实现：

- company 表中，parentID 是 1 的是租户，parentID > 1 的是项目。
    - 限制普通用户只能创建项目，不可以创建租户。
    - 仅超级用户可以创建租户。 

- 租户：
    - 管理用户、角色、权限、菜单。可以为用户赋予不同的角色。
- 项目：
    - 从属于租户，不同的项目有不同的角色（对应的是不同的菜单）


和老平台的区别其实不大，就是规范 租户 和 项目 对不同的资源的管理，避免混淆。底层实现都是 company 这个表。

- 超级用户登陆的时候，会列出所有的租户。进入租户，会列出所有的项目。
- 普通用户登录的时候，就列出当前租户下的所有项目。
- 切换租户和切换项目是一样的，就是 company_switch。切换之后，会返回当前company 的菜单。此时后端根据切换到的 company 是租户还是项目，对菜单进行过滤。
    - 如果是租户，仅保留用户、角色、项目、用户日志等的管理功能。
    - 如果是项目，则过滤掉用户、角色、项目、用户日志等功能。

租户下的所有人，都可以访问当前租户下的所有项目，但是每个人的角色是可以由管理员来定义的。

- 问题 1：如何限定某些用户、对于所属租户的某些项目的访问 —— 不考虑。如需隔离开，就开通多一个租户。

```
可以实现，但是增加复杂度
    - 1、创建项目的时候，确定是私有的，还是租户公有的 —— 那是否要记录项目的创建者，当改为私有的时候，就只有创建者可以看到？
    - 2、添加用户的时候，可以选择有权限的项目。
    - 3、后台维护用户和项目的绑定关系。
    - 4、某个人登录之后，返回的项目列表，根据绑定关系来过滤。
```

- 问题 2：是否有必要复用 company 这个表？添加一个 project 表是否可行？
    - 不行。添加 project 表的话，如何为 project 定制角色？现在的 company-role 和 user-role 已经很复杂了。我们没必要维护一套 project-role。

- 问题 3：是否可以用 casbin 这样的库来实现我们上述权限管理。从需求上看，我们需要用户、公司有多个角色。角色之间还有继承关系。
    - 可以考虑。



## 评估方案
 
1. 实现简单。
    - 只需在两个接口（ login、company_switch ）中处理判断逻辑，后端返回菜单的时候过滤一下。前端不需要做任何修改。
    - 复用了 company 表，仅使用 company 的 parentID 来区分租户和项目。
2. 业务逻辑清晰
    - 租户切换 → 返回用户管理、角色管理、项目管理菜单
    - 项目切换 → 返回数据管理、设备管理、应用管理菜单   
    - 其他所有业务逻辑都基于统一的 companyID
3. 实现成本最低
    - 无需重构现有数据结构
    - 无需修改大量业务代码
    - 权限检查逻辑保持不变