## 请求方法

有两种风格：

```
风格一：学院派

GET    /articles                – 列出/分页/过滤文章列表
GET    /articles?articleId=123  – 获取文章详情
POST   /articles      – 创建新文章
PATCH  /articles      – 更新文章详情
DELETE /articles      – 删除指定ID的文章

风格2：工程实践派。不使用PUT和DELETE。 如果有 add、update 和 delete 的需求，则使用POST，在 url 上面体现语义

GET  /articles                – 列出/分页/过滤文章列表
GET  /articles?articleId=123  – 获取文章详情
POST /articles/add     – 创建新文章
POST /articles/update  – 更新文章详情
POST /articles/delete  – 删除指定ID的文章

```

建议使用方案2。


## URL

- url统一使用小写字母。单词之间可以用`-`来连接。
- /api/版本号/命名空间/资源/动作
- 资源名词必须清晰易懂


## 参数传递

- GET操作，使用 Query String， 例如 /api/v1/user?id=123。禁止使用 uri path parameter。
- GET查询操作按需增加缓存。
- POST操作，使用 json 作为参数编码方式。
- 为保持与数据库表结构的一致性，json 编码统一使用下划线方式，而不是大小写混编。
- 参数名字与数据库表中的字段名同步。



## 返回值

为方便前端解析，定义统一返回值结构：

```
code： 状态码，后台可以维护一套统一的状态码；
message： 描述信息，接口调用成功/失败的提示信息；
data： 返回数据。
```
其中，code = 0，为请求成功。code 不为0，则data为空，message为错误提示信息。

## 状态码

正常响应的返回状态码统一使用 200。返回值内自定义了错误码，由前端和后端统一约定。

不使用标准的HTTP返回码


## 开放接口鉴权

一般通过接口签名来鉴权，具体如下：

```

在请求头携带以下信息： appid，appkey，timestamp，nonce，sign。
通过以上几个参数，以及相关签名算法，来获取调用系统的凭证。

appid和appkey可以直接通过平台线上申请，也可以线下直接颁发。appid是全局唯一的，每个appid将对应一个客户，appkey需要高度保密。
timestamp是时间戳，使用系统当前的unix时间戳。时间戳的目的就是为了减轻DOS攻击。防止请求被拦截后一直尝试请求接口。服务器端设置时间戳阀值，如果请求时间戳和服务器时间超过阀值，则响应失败。
nonce是随机值。随机值主要是为了增加sign的多变性，也可以保护接口的幂等性，相邻的两次请求nonce不允许重复，如果重复则认为是重复提交，响应失败。
sign是参数签名，将appkey，timestamp，nonce拼接起来进行md5加密（当然使用其他方式进行不可逆加密也没问题）。
token，使用参数appid，timestamp，nonce，sign来获取token，作为系统调用的唯一凭证。token可以设置一次有效（这样安全性更高），也可以设置时效性，这里推荐设置时效性。如果一次有效的话这个接口的请求频率可能会很高。token推荐加到请求头上，这样可以跟业务参数完全区分开来。

HMAC : Hash-based Message Authentication Code
是一种更安全的消息摘要算法。
它要求通信双方共享密钥、约定算法、对报文进行Hash运算，形成固定长度的认证码。通信双方通过认证码的校验来确定报文的合法性。 HMAC算法可以用来作加密、数字签名、报文验证等 。

Hmac算法总是和某种哈希算法配合起来用的。例如，我们使用MD5算法，对应的就是HmacMD5算法，它相当于“加盐”的MD5。 或者 SHA-256。

```