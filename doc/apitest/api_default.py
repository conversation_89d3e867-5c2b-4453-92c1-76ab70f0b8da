#!/usr/bin/env python3
# coding=utf-8

from re import T
import requests
import sys
import os
import json
import time
from datetime import datetime, timedelta

from utils_http import GET, POST ,get_time_range,user_login

# ===============================================================================================
def  user_profile():
    GET("/api/user/profile")


def  system_info():
    GET("/api/user/system_info")


def  product_get():
    GET("/api/iot/get_product", params={
        "product_key": "ct4e8172nf00",
    })

def  product_find():
    GET("/api/iot/find_product", params={
        "product_key": "ct4e8172nf00",
    })


# 请求网址:
# https://nuc.beithing.com:60080/api/station/query_station_tsdata?station_sn=0075685515&time_window=1h&start=1732591417&end=1732677817&measure=vwp_osm_water_3

# 时序数据相关
def tsdata_read_tsdata():
    start, end = get_time_range("1d")
    GET("/api/station/query_station_tsdata", params= {
        "station_sn": "B220372390",
        "measure":"ff_vwp_water_9",
        "time_window":"5min",
        # "time_window":"1h",
        "start": start,
        "end": end,
        "has_stats":True,
    })     
    
def tsdata_read_tsdata_stats():
    start, end = get_time_range("1d")
    GET("/api/station/query_station_tsdata_stats", params= {
        "station_sn": "B220372390",
        "measure":"ff_vwp_water_9",
        "start": start,
        "end": end,
        # "filed":["value", "timestamp"],
        "agg":"mean",
    })     
    
def tsdata_delete():
    start, end = get_time_range("1d")
    GET("/api/station/station_data_delete", params= {
        # "station_sn": "0623646239",
        "measure":"gongkuang",
        # "start": start,
        # "end": end,
    })

def jt808_get_data():
    GET("/api/jt808/history/position/list", params= {
        "device_type_id": "GCtUBMRQrx",
        "device_id": "12345678901",
    })


# 物模型相关

# 设备属性和命令下发接口测试
def device_down_attri():
    # 下发属性
    POST("/api/device/down/attribute", body={
        "device_id": "qgby8nYj7B",  # 设备ID
        "method": "attri_set", 
        "data": {
            "temp_threshold": 35,  # 温度属性
            "humid_threshold": 50,       # 湿度属性
        }
    })
    
# 下发命令1
def device_down_command1():
    POST("/api/device/down/command", body={
        "device_id": "qgby8nYj7B",  # 设备ID
        "command": "set_temp_threshold",            # 命令标识符
        "data": {
           "temp_threshold": 35,  # 温度属性
        }
    })
    
# 下发设置阈值命令 2
def device_down_command2():
    POST("/api/device/down/command", body={
        "device_id": "qgby8nYj7B",  # 设备ID
        "command": "reboot",      # 命令标识符
    })

# -================================================================================================================================

def attri_history_get():
    deviceID = "9gZUkH9NgV"
    attriID = "0950857333337"
    # attriID = "3651779557359"
    start, end = get_time_range("1d")
    # /attribute/history
    GET("/api/device/attribute/history", params={
        "device_id": deviceID,
        "attr_id": attriID,
        "start": start,
        "end": end,
    })
    

# 执行主函数
if __name__ == "__main__":
    if len(sys.argv) > 1:
        print("--------------------- 1")
        print("arg : ", sys.argv[1])
        one = sys.argv[1] 
        if one == 'login':
            user_login()
        elif one == "profile":
            user_profile()
        else:
            print("not support")
            

    else:
        print("--------------------- 2")
        # 测试设备属性和命令下发接口
        # device_down_test()
        #device_down_attri()
        # jt808_get_data()
        # device_down_command1()
        # device_down_command2()
        attri_history_get()
      
