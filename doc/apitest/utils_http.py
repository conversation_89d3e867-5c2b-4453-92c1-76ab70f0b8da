#!/usr/bin/env python3
# coding=utf-8

from re import T
import requests
import sys
import os
import json
import time
from datetime import datetime, timedelta


hostNuc= "http://*************:53006"
hostLocal = "http://localhost:8200"
hostDev = "http://dev.beithing.com:53006"

defaultHost = hostNuc

def print_date():
    # 获取当前时间
    now = datetime.now()
    # 格式化时间，包含毫秒，并去掉多余的微秒部分
    formatted_time = now.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    # 打印当前时间，精确到毫秒
    print(formatted_time)

# 获取 N 天前的 unix 时间戳
def get_past_timestamp(days):
    past_time = datetime.now() - timedelta(days=days)
    # 将时间转换为Unix时间戳
    timestamp = int(time.mktime(past_time.timetuple()))
    print("timestamp = ", timestamp)
    return timestamp


def get_default_headers():
    if os.path.exists("token.txt"):
        # 从token.txt中读取token值
        with open("token.txt", "r") as f:
            token = f.read().strip()
    else:
        print("Token file not found")
        sys.exit(0)


    # 设置鉴权参数
    headers = {
        # "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
    }
    return headers


# =====================================================================================

def GET(url, headers=None, params=None, auth=True, host=defaultHost):
    url = host + url

    print("URL: ", url)
    print("参数 : ", params) 

    print("返回值 : ") 
    # 添加默认header
    all_headers = {}
    if headers:
        all_headers.update(headers) 
    if auth:
        default_headers = get_default_headers()
        all_headers.update(default_headers)
 
    try:
        response = requests.get(url, headers=all_headers, params=params)
        print(json.dumps(response.json(), indent=4, ensure_ascii=False))
        data= response.json()
        if data['code'] == 0:
            print("操作成功")
        else:
            print("------------------------------------ 操作失败")
            time.sleep(1)
        return data

    except KeyboardInterrupt:
        print("程序被用户中断")
        exit(0)
    # except Exception as e:
    #     # sys.exit(0)
    #     print("程序异常", e)


def POST(url, headers=None, body=None,auth=True, host=defaultHost):    
    url = host + url
   
    # 添加默认header
    all_headers = {}
    if headers:
        all_headers.update(headers) 
    if auth:
        default_headers = get_default_headers()
        all_headers.update(default_headers)

    try:
        response = requests.post( url, headers=all_headers,  json=body)
        print(json.dumps(response.json(), indent=4, ensure_ascii=False))
        data = response.json()
        if data['code'] == 0:
            print("操作成功")
        else:
            print("------------------------------------ 操作失败")
            time.sleep(1)
        return data

    except KeyboardInterrupt:
        print("程序被用户中断")
        exit(0)
    except Exception as e:
        print("程序异常", e)
        sys.exit(0)


def upload_file(url, file_path, data=None, headers=None, auth=True,host=defaultHost):
    url = host + url

    # 添加默认 header
    all_headers = {}
    if headers:
        all_headers.update(headers)
    if auth:
        default_headers = get_default_headers()
        # 移除 `Content-Type`，让 `requests` 自动处理 `multipart/form-data`
        default_headers.pop("Content-Type", None)
        all_headers.update(default_headers)

    # 准备文件和 JSON 数据
    try:
        # 需要上传的文件
        files = {
            'file': open(file_path, 'rb')
        }

        # 发送 POST 请求
        response = requests.post(url, headers=all_headers, files=files, data=data)
        print(json.dumps(response.json(), indent=4, ensure_ascii=False))
        data = response.json()
        if data['code'] == 0:
            print("操作成功")
        else:
            print("------------------------------------ 操作失败")
            time.sleep(1)
        return data

    except KeyboardInterrupt:
        print("程序被用户中断")
        exit(0)

    except Exception as e:
        print("程序异常", e)
        sys.exit(0)



# -================================================================================================================================


def print_date():
    # 获取当前时间
    now = datetime.now()
    # 格式化时间，包含毫秒，并去掉多余的微秒部分
    formatted_time = now.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    # 打印当前时间，精确到毫秒
    print(formatted_time)

# 获取 N 天前的 unix 时间戳
def get_past_timestamp(days):
    past_time = datetime.now() - timedelta(days=days)
    # 将时间转换为Unix时间戳
    timestamp = int(time.mktime(past_time.timetuple()))
    print("timestamp = ", timestamp)
    return timestamp



# 返回时间戳，单位 ms
def get_time_range(time_window="1h"):
    """获取时间范围，返回start和end时间戳
    
    Args:
        time_window: 时间窗口，支持:
            - 5min: 5分钟
            - 15min: 15分钟
            - 30min: 30分钟
            - 1h: 1小时
            - 6h: 6小时
            - 12h: 12小时
            - 1d: 1天
            - 7d: 7天
            - 30d: 30天
    
    Returns:
        tuple: (start_timestamp, end_timestamp)
    """
    import time
    from datetime import datetime, timedelta
    
    # 解析时间窗口
    unit = time_window[-1]  # 最后一个字符是单位
    value = int(time_window[:-1])  # 前面的数字是值
    
    # 获取当前时间
    now = datetime.now()
    
    # 计算时间范围
    if unit == 'd':  # 天
        delta = timedelta(days=value)
    elif unit == 'h':  # 小时
        delta = timedelta(hours=value)
    elif unit == 'w':  # 周
        delta = timedelta(weeks=value)
    else:  # 分钟
        delta = timedelta(minutes=value)
    
    # 计算开始时间
    start_time = now - delta
    
    # 转换为时间戳（毫秒级）
    end_timestamp = int(now.timestamp() * 1000)
    start_timestamp = int(start_time.timestamp() * 1000)
    
    return start_timestamp, end_timestamp



# 登陆
def user_login(host=defaultHost):
    data = {
        "username":    "shujun",
        "password":    "shujun123",
        "verify_code": "12345",
        "captcha_id":  "test"
    }
    
    response = POST("/api/user/login", body = data,auth=False, host=host)

    if response["code"] == 0:
        token = response["payload"]["accessToken"]
        with open("token.txt", "w") as f:
            f.write(token)
        print("Token saved to token.txt")
    else:
        print("Login failed:", response["msg"])
