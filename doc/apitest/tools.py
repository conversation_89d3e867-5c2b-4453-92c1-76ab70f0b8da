#!/usr/bin/env python3
# coding=utf-8
import hmac
import hashlib

def hmac_sha256(key, message):
    """
    计算 HMAC-SHA256 签名
    :param key: 密钥
    :param message: 待签名的消息
    :return: 16进制字符串形式的签名
    """
    signature = hmac.new(
        key.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    return signature

# 示例
key = "your_secret_key"
message = "data_to_sign"
print("HMAC-SHA256:", hmac_sha256(key, message))