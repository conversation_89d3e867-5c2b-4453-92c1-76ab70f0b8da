# MQTT 监控程序

这里提供了两个版本的 MQTT 监控程序，用于订阅和监控 MQTT broker 的系统状态和业务消息。

## 功能特性

### 系统监控主题
- 📊 客户端连接统计（连接数、断开数、总数、最大数）
- 📈 消息统计（接收数、发送数、丢弃数）
- 🔖 系统信息（版本、运行时间、内存使用）
- 📊 负载统计（1分钟消息负载）
- 📋 订阅和保留消息统计

### 业务消息监控
- ⬆️ 设备上行消息（属性上报、事件上报、方法响应）
- ⬇️ 设备下行消息（属性读取/设置、事件响应、方法调用）
- 📱 设备信息解析（设备类型ID、设备ID）
- 📋 消息类型识别（attri/event/action）

## 使用方法

### Go 版本（推荐）

#### 1. 编译运行
```bash
# 在项目根目录下
cd cmd/mqtt_monitor
go run main.go
```

#### 2. 编译为可执行文件
```bash
go build -o mqtt_monitor cmd/mqtt_monitor/main.go
./mqtt_monitor
```

#### 3. 配置说明
在 `cmd/mqtt_monitor/main.go` 中修改连接配置：
```go
const (
    MQTT_BROKER   = "tcp://*************:1883" // 修改为你的 MQTT broker 地址
    CLIENT_ID     = "mqtt_system_monitor"
    KEEP_ALIVE    = 60 * time.Second
)
```

### Python 版本

#### 1. 安装依赖
```bash
pip install paho-mqtt
```

#### 2. 运行程序
```bash
cd doc/mqtt
python main.py
```

#### 3. 配置说明
在 `doc/mqtt/main.py` 中修改连接配置：
```python
MQTT_BROKER = "*************"  # 修改为你的 MQTT broker 地址
MQTT_PORT = 1883
```

## 输出示例

### 系统监控消息
```
[14:30:15] 📊 $SYS/broker/clients/connected
           💾 数据: 5

[14:30:16] 📊 $SYS/broker/system/memory
           💾 数据: 1048576
           📈 内存: 1.00 MB

[14:30:17] 📊 $SYS/broker/uptime
           💾 数据: 7200
           ⏰ 运行时间: 2.00 小时
```

### 业务消息监控
```
[14:30:20] ⬆️ 设备消息
           📱 设备: device_type_001/device_001
           📋 类型: attri.report
           📄 主题: thing/up/attri/report/device_type_001/device_001
           💾 数据: {"sn":"123","ts":1677762028638,"params":{"temperature":25.6,"humidity":60.2}}

[14:30:25] ⬇️ 设备消息
           📱 设备: device_type_001/device_001
           📋 类型: attri.set
           📄 主题: thing/down/attri/set/device_type_001/device_001
           💾 数据: {"sn":"124","ts":1677762033638,"params":{"power_switch":1}}
```

## 监控的主题列表

### 系统主题（$SYS）
```
$SYS/broker/clients/connected      # 当前连接客户端数
$SYS/broker/clients/disconnected   # 断开连接客户端数
$SYS/broker/clients/total          # 总客户端数
$SYS/broker/clients/maximum        # 最大连接数
$SYS/broker/messages/received      # 接收消息数
$SYS/broker/messages/sent          # 发送消息数
$SYS/broker/messages/dropped       # 丢弃消息数
$SYS/broker/version                # broker 版本
$SYS/broker/uptime                 # 运行时间（秒）
$SYS/broker/system/memory          # 内存使用（字节）
```

### 业务主题
```
thing/up/+/+/+     # 所有设备上行消息
thing/down/+/+/+   # 所有设备下行消息
```

## 注意事项

1. **权限要求**: 确保 MQTT 客户端有权限订阅 `$SYS` 主题
2. **网络连接**: 确保能够访问 MQTT broker 的地址和端口
3. **资源消耗**: 监控程序会订阅大量主题，注意 broker 的性能影响
4. **日志输出**: 程序会持续输出监控信息，建议重定向到文件或使用日志管理

## 停止程序

使用 `Ctrl+C` 停止程序，程序会优雅地断开 MQTT 连接。

## 扩展功能

可以根据需要添加以下功能：
- 📊 数据统计和图表展示
- 💾 数据持久化存储
- 🚨 告警阈值设置
- 📧 邮件/短信通知
- 🌐 Web 界面展示
- 📈 性能指标分析
