
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT 系统监控程序

安装依赖:
pip install paho-mqtt

运行:
python main.py
"""

import paho.mqtt.client as mqtt
import json
from datetime import datetime

# MQTT Broker 配置
MQTT_BROKER = "*************"  # 根据你的配置文件中的地址
MQTT_PORT = 1883
MQTT_KEEPALIVE = 60

# 系统监控主题列表
SYS_TOPICS = [
    # 客户端相关
    "$SYS/broker/clients/connected",     # 当前连接的客户端数量
    "$SYS/broker/clients/disconnected",  # 断开连接的客户端数量
    "$SYS/broker/clients/total",         # 总客户端数量
    "$SYS/broker/clients/maximum",       # 最大连接数

    # 消息统计
    "$SYS/broker/messages/received",     # 接收的消息数
    "$SYS/broker/messages/sent",         # 发送的消息数
    "$SYS/broker/messages/dropped",      # 丢弃的消息数

    # 系统信息
    "$SYS/broker/version",               # broker 版本
    "$SYS/broker/uptime",                # 运行时间
    "$SYS/broker/system/memory",         # 内存使用
]

class MQTTSystemMonitor:
    def __init__(self, broker, port=1883, keepalive=60):
        self.broker = broker
        self.port = port
        self.keepalive = keepalive
        self.client = mqtt.Client(client_id="mqtt_system_monitor", clean_session=True)

        # 设置回调函数
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        self.client.on_subscribe = self.on_subscribe

    def on_connect(self, client, userdata, flags, rc):
        """连接回调函数"""
        if rc == 0:
            print(f"✅ 成功连接到 MQTT Broker: {self.broker}:{self.port}")
            print(f"📅 连接时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 60)

            # 订阅所有系统主题
            for topic in SYS_TOPICS:
                result, mid = client.subscribe(topic, qos=0)
                if result == mqtt.MQTT_ERR_SUCCESS:
                    print(f"📡 订阅主题: {topic}")
                else:
                    print(f"❌ 订阅失败: {topic}, 错误码: {result}")
            print("-" * 60)
        else:
            print(f"❌ 连接失败，错误码: {rc}")

    def on_message(self, client, userdata, msg):
        """消息接收回调函数"""
        try:
            topic = msg.topic
            payload = msg.payload.decode('utf-8')
            timestamp = datetime.now().strftime('%H:%M:%S')

            # 格式化输出
            print(f"[{timestamp}] 📊 {topic}")
            print(f"           💾 数据: {payload}")

            # 特殊处理某些主题的数据格式
            if "memory" in topic and payload.isdigit():
                memory_mb = int(payload) / (1024 * 1024)
                print(f"           📈 内存: {memory_mb:.2f} MB")
            elif "uptime" in topic and payload.isdigit():
                uptime_hours = int(payload) / 3600
                print(f"           ⏰ 运行时间: {uptime_hours:.2f} 小时")

            print()

        except Exception as e:
            print(f"❌ 处理消息时出错: {e}")

    def on_disconnect(self, client, userdata, rc):
        """断开连接回调函数"""
        if rc != 0:
            print(f"⚠️  意外断开连接，错误码: {rc}")
        else:
            print("👋 正常断开连接")

    def on_subscribe(self, client, userdata, mid, granted_qos):
        """订阅确认回调函数"""
        pass  # 可以在这里添加订阅确认的处理逻辑

    def start_monitoring(self):
        """开始监控"""
        try:
            print("🚀 启动 MQTT 系统监控程序")
            print(f"🔗 连接地址: {self.broker}:{self.port}")
            print(f"⏱️  心跳间隔: {self.keepalive} 秒")
            print("=" * 60)

            # 连接到 broker
            self.client.connect(self.broker, self.port, self.keepalive)

            # 开始循环处理
            self.client.loop_forever()

        except KeyboardInterrupt:
            print("\n🛑 收到中断信号，正在停止监控...")
            self.stop_monitoring()
        except Exception as e:
            print(f"❌ 启动监控时出错: {e}")

    def stop_monitoring(self):
        """停止监控"""
        try:
            self.client.disconnect()
            print("✅ 监控程序已停止")
        except Exception as e:
            print(f"❌ 停止监控时出错: {e}")

def main():
    """主函数"""
    print("🔍 MQTT 系统监控程序")
    print("=" * 60)

    # 创建监控实例
    monitor = MQTTSystemMonitor(
        broker=MQTT_BROKER,
        port=MQTT_PORT,
        keepalive=MQTT_KEEPALIVE
    )

    # 开始监控
    monitor.start_monitoring()

if __name__ == "__main__":
    main()