## 关于序列 seq

在 PostgreSQL 中，序列通常用于为 SERIAL 或 BIGSERIAL 类型的列生成唯一的自动增量值。

SERIAL 和 BIGSERIAL是自动递增类型，基于 INTEGER 和 BIGINT。PostgreSQL 会自动创建并管理一个序列，确保插入时主键值的唯一性和递增性。


## 相关命令

1. 检查现有序列
可以查询系统目录表 pg_sequences 或 pg_class 和 pg_depend 来查看数据库中存在的序列。

```sql
-- 查看所有序列
SELECT * FROM pg_sequences;

-- 或者查看特定表的序列
SELECT 
    c.relname AS sequence_name
FROM 
    pg_class c
    JOIN pg_depend d ON d.objid = c.oid
    JOIN pg_class t ON d.refobjid = t.oid
WHERE 
    t.relname = 'your_table_name' AND
    c.relkind = 'S';
```

2. 创建新的序列
如果表缺少对应的序列，你可以手动创建一个新的序列，并将其与表的列关联。

```sql
-- 创建序列
CREATE SEQUENCE your_table_name_your_column_name_seq;

-- 设置列的默认值为新创建的序列
ALTER TABLE your_table_name ALTER COLUMN your_column_name SET DEFAULT nextval('your_table_name_your_column_name_seq');
```


3. 将序列与列关联
如果你已经有一个序列并希望将其与某个列关联，可以设置列的默认值为序列的 nextval：


```sql
-- 将现有序列设置为列的默认值
ALTER TABLE your_table_name ALTER COLUMN your_column_name SET DEFAULT nextval('your_existing_sequence_name');
```

4. 检查列的当前序列

可以使用 pg_get_serial_sequence 函数来检查列是否已经设置了序列，并获取序列名称：


```sql
SELECT pg_get_serial_sequence('your_table_name', 'your_column_name');
```

如果返回 NULL，表示该列没有设置序列。

示例
假设你有一个表 employees，列 employee_id 应该使用序列进行自动增量，但你发现没有对应的序列。

检查序列:


```sql
SELECT * FROM pg_sequences WHERE schemaname = 'public';
```

创建新的序列:

```sql
CREATE SEQUENCE employees_employee_id_seq;
```

将序列与列关联:

```sql
ALTER TABLE employees ALTER COLUMN employee_id SET DEFAULT nextval('employees_employee_id_seq');
```

通过这些步骤，你可以确保表的列与相应的序列正确关联，从而保证自动增量值的生成。



只是更新 _id_seq

```sql
SELECT setval('datapoint_id_seq', (SELECT MAX(id) FROM datapoint));
```

## pgdump 


1、查看建表过程


```
pg_dump -U your_username -d your_database -t your_table --schema-only
```

postgres dsn : *******************************************/ebox_online4?sslmode=disable&TimeZone=UTC" service=gw


pg_dump -U root -d ebox_online4 -t device --schema-only

## 索引和约束

```sql
SELECT conname AS constraint_name,  contype AS constraint_type,   condeferrable,   condeferred,    pg_get_constraintdef(oid) AS definition FROM pg_constraint WHERE conrelid = 'device'::regclass;


SELECT conname AS constraint_name,  contype AS constraint_type,   condeferrable,   condeferred,    pg_get_constraintdef(oid) AS definition FROM pg_constraint WHERE conrelid = 'tag'::regclass;

SELECT conname FROM pg_constraint WHERE conrelid = '__table_name'::regclass;
```