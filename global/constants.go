package global

/*

查询产品和相关软件category

+-----+--------------+--------+-----+----------+
| pid | key          | model  | cid | name     |
+-----+--------------+--------+-----+----------+
|   2 | civea0cfe9rh | NC1800 |   2 | firmware |
|   3 | civeasecxhwl | NC2800 |   3 | kernel   |
|   3 | civeasecxhwl | NC2800 |   4 | rootfs   |
|   3 | civeasecxhwl | NC2800 |   5 | app_iot  |
|   4 | civebe716ola | NC1801 |   6 | firmware |
|   3 | civeasecxhwl | NC2800 |   7 | system   |
+-----+--------------+--------+-----+----------+
*/

const (
	PKEY_NC1800  = "amck1z7dw9a8" //一代 RTU
	PKEY_NC2800  = "ct4e8172nf00" //二代 RTU
	PKEY_RTUV2M8 = "crvonfe434m6" //二代 RTU 带8 路振弦
	PKEY_RTUV3   = "d3ac4axsj7gq" //三代 RTU
)

const (
	PID_NC1800 = 1  //一代 RTU
	PID_NC2800 = 11 //二代 RTU
	// PID_RTUV2M8 = 10 //二代 RTU 带8 路振弦
)

const (
	COMPANY_ID_BEITHING = 1  // 北盛公司的 ID，特殊处理时需要
	COMPANY_ID_NANCHANG = 21 // 南昶公司的 ID，特殊处理时需要
)

const (
	P_SL651  string = "SL651"  //水文监测数据通信规约
	P_SL2022 string = "SL2022" //水文监测数据通信规约
	P_SL427  string = "SL427"  //水资源监测数据传输规约，新版
	P_SZY206 string = "SZY206" //水资源监测数据传输规约，老版
	P_HJ212  string = "HJ212"  //环境协议
	P_GNSS   string = "GNSS"   //华测 GNSS 协议
	P_PUXUN  string = "PUXUN"  //普讯设备 UDP 协议
)
