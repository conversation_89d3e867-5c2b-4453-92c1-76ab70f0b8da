package global

import (
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/docker/docker/client"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/mem"
	"gorm.io/gorm"

	"bs.com/app/config"
	"bs.com/app/pkg/eventbus"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xminio"
	"bs.com/app/pkg/xnats"
)

// 通过 -ldflags 注入的全局变量，使用编译时自动替代
var (
	Version   string // 版本号，格式 yy.mmdd.xx
	CommitID  string // 提交 ID
	CommitLog string // 提交日志
	BuildTime string // 编译时间
)

// 全局上下文
type Global struct {
	Container sync.Map //其他全局依赖注入容器

	Cfg *config.Config //系统配置

	Ebus *eventbus.Client //进程内消息总线

	Cache *Redis   //redis 缓存
	DB    *gorm.DB //gorm 数据库实例

	NatsClient *xnats.NatsClient // nats client

	// not used
	MinioOtherClient   *xminio.XMinio // minio的buket_other的连接实例
	MinioProjectClient *xminio.XMinio // minio的buket_project的连接实例

	HostInfo //宿主机信息
}

type HostInfo struct {
	HostID    string `json:"host_id"` //主机 docker ID
	HostName  string `json:"host_name"`
	PublicIP  string `json:"public_ip"`
	OS        string `json:"os"`
	Arch      string `json:"arch"`
	MemTotal  int64  `json:"mem_total"`
	MemUse    int64  `json:"mem_use"`
	DiskTotal uint64 `json:"disk_total"`
	DiskFree  uint64 `json:"disk_free"`
	DiskUse   uint64 `json:"disk_use"`
}

var G *Global

func InitGlobal() {
	fmt.Println(">>>>>>>>>>>>")
	fmt.Printf("Version: %s\n", Version)
	fmt.Printf("CommitID: %s\n", CommitID)
	fmt.Printf("CommitLog: %s\n", CommitLog)
	fmt.Println(">>>>>>>>>>>>")

	G = &Global{}
	//G.bootEtcd()

	// xlog.Info("boot eventbus")
	G.bootEventbus()

	err := G.InitNats()
	if err != nil {
		xlog.Error("init nats failed", "err", err)
		os.Exit(1)
	}
	xlog.Info("init global done")

	// 通过 docker socket ，获取宿主机的信息
	// G.GetHostInfo()
}

// 使用chan 实现的进程内event - handler，简单易用
/*
	//添加event handler
	g.Ebus.Subscribe("test", func(value interface{}) {
		xlog.WithKV("value", value).Info("test handler by event bus")
	})

	//发送event
	g.Ebus.Publish("test", "hello world")
*/
func (g *Global) bootEventbus() {
	g.Ebus = eventbus.NewClient()
}

func (g *Global) GetHostInfo() {
	xlog.Info("get host info")

	// 使用 docker.sock 连接到 Docker API
	cli, err := client.NewClientWithOpts(client.WithHost("unix:///var/run/docker.sock"))
	if err != nil {
		log.Fatal(err)
	}

	// 获取 Docker 引擎的系统信息
	info, err := cli.Info(context.Background())
	if err != nil {
		log.Fatal(err)
	}

	// 打印宿主机的硬件信息
	fmt.Println("Docker Engine Info:")
	fmt.Println("ID: ", info.ID)
	fmt.Println("Operating System: ", info.OperatingSystem)
	fmt.Println("Kernel Version: ", info.KernelVersion)
	fmt.Println("Architecture: ", info.Architecture)
	fmt.Println("CPU Count: ", info.NCPU)
	fmt.Println("Memory (bytes): ", info.MemTotal)

	d, _ := disk.Usage("/")

	g.HostInfo = HostInfo{
		HostID:    info.ID,
		HostName:  info.Name,
		OS:        info.OperatingSystem,
		Arch:      info.Architecture,
		MemTotal:  info.MemTotal,
		MemUse:    info.MemTotal,
		DiskTotal: d.Total,
		DiskFree:  d.Free,
		DiskUse:   d.Used,
	}

	g.GetPublicIP()

	// 获取宿主机的内存信息
	v, _ := mem.VirtualMemory()
	fmt.Printf("Memory Usage: %.2f%%\n", v.UsedPercent)

	// 获取宿主机的磁盘信息
	fmt.Printf("Disk Total: %v GB\n", d.Total/1024/1024/1024)
	fmt.Printf("Disk Free: %v GB\n", d.Free/1024/1024/1024)
	fmt.Printf("Disk Used: %.2f%%\n", d.UsedPercent)
}

func (g *Global) GetPublicIP() error {
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get("http://ifconfig.me")
	if err != nil {
		return fmt.Errorf("failed to get public IP: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	g.HostInfo.PublicIP = string(body)
	return nil
}

func (g *Global) InitMinio() {
	mConf := config.Get().Minio
	conf := xminio.Config{
		EndPoint:        mConf.Endpoint,
		Domain:          mConf.Domain,
		AccessKeyID:     mConf.AccessKey,
		SecretAccessKey: mConf.SecretKey,
	}
	conf.BucketName = mConf.BucketOther
	G.MinioOtherClient = xminio.NewClient(&conf)

	//
	conf.BucketName = mConf.BucketProject
	g.MinioProjectClient = xminio.NewClient(&conf)

}

// =======================================================================
func (g *Global) Set(name string, val interface{}) {
	g.Container.Store(name, val)
}

func (g *Global) Get(key string) (interface{}, error) {
	if val, ok := g.Container.Load(key); ok {
		return val, nil
	}
	return nil, fmt.Errorf("Not found %s from container! ", key)
}
