package global

import (
	"context"
	"fmt"
	"os"
	"time"

	"bs.com/app/config"
	"bs.com/app/pkg/xlog"
	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/spf13/viper"
)

func InitInflux() error {
	const (
		flushInterval   uint = 20 // second
		retryInterval   uint = 3  // second
		batchSize       uint = 10 // 批量写入的 size
		retryQueueLimit uint = 1000
	)

	cfg := config.Get().Influxdb2

	xlog.Info("influxdb  auth token: " + cfg.Token)

	hostUrl := fmt.Sprintf("http://%s", cfg.Address)

	opts := influxdb2.DefaultOptions().SetUseGZip(true)
	opts = opts.SetPrecision(time.Second)

	opts = opts.SetFlushInterval(flushInterval * 1000)
	opts = opts.SetRetryInterval(retryInterval * 1000)
	opts = opts.SetBatchSize(batchSize)
	opts = opts.SetRetryBufferLimit(retryQueueLimit * batchSize)

	influx2Token := viper.GetString("influxdb2.token")
	client := influxdb2.NewClientWithOptions(hostUrl, influx2Token, opts)
	ok, err := client.Ping(context.Background())
	if !ok {
		xlog.Info("influx ping failed", "err", err, "token", influx2Token)

		os.Exit(0)
		//return err
	}

	xlog.Info("influx Ping OK")

	return nil
}
