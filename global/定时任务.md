## 简单异步任务

使用eventbus即可

## 复杂异步任务

使用 asynq： async task queue

## 定时任务

使用 cron

- 有一些耗时比较久的数据，比如数据分析、数据汇总、召测、整编等，可以建一个异步 job，然后在系统空闲的时候，慢慢执行。
- 定时执行删除一些无效自动创建的测站。
- 定时远程重启设备，避免RTC无法工作的问题。
- 定时删除设备运维传感器的旧数据。—— 参考 h_iot.go

## cron spec

cron表达式是一个字符串，字符串以5或6个字段来表示时间信息。

默认是5个字段，cron可以通过扩展，支持秒级的时间。

特殊字符的含义

（1）*：表示匹配该域的任意值，假如在Minutes域中使用*，即表示每分钟都会触发事件。

（2）？：只能用在DayofMonth 和 DayofWeek两个域中，表示未说明的值，即不关心它为何值，用于解决DayofMonth 和 DayofWeek之间的冲突。

因为DayofMonth 和 DayofWeek会相互影响。例如cron表达式为“ * * * 20 * ?
”，则表示任务设置在每月的20日触发调度，不管20日到底是星期几都会触发，而如果把？换成 *，则表示不管星期几都会触发调度,
而DayofMonth又设置为20，表示只能在每个月的20日触发调度，这样就会引起冲突，所以必须要对其中一个设置? 来表示并不关心它为何值。

（3）-：指定范围，例如：在Minutes域中使用5-20，表示从5分到20分钟每分钟触发一次。

（4）/：符号前的数字表示开始时间，符号后的数字表示每次递增的值。例如：在Minutes域中使用5/20，表示从5分分钟开始执行，每隔20分钟触发一次。

（5）,：表示列出枚举值。例如：在Minutes域中使用5,20，则表示在5分钟 和 20分钟各触发一次。

（6）L：表示last，只能出现在DayofMonth 和 DayofWeek域，例如：

    在DayofMonth域中使用L，则表示在当前月的最后一天触发一次。

    在DayofWeek域中使用L，则表示在当前月每周的星期六触发一次。 如果在DayofWeek域中和数字联合使用，则表示当前月的最后一个星期几触发一次，如：6L表示当前月的最后一个星期五触发一次

（7）W：只能用在月份中，表示最接近指定天的工作日 (周一到周五)。

例如：在DayofMonth域中使用5W 指 "最接近当前月第5天的工作日"
，如果当前月的第5天是星期六，则将在最近的工作日：星期五，即当前月的第4天触发。如果当前月的第5天是星期天，则在当前月的第6天(
周一)触发；如果当前月的第5天在星期一 到星期五之间，则就在当前月的第5天触发。另外一点，W在寻找最近工作日是不会跨过月份。

（8）LW：这两个字符可以连用，表示在当前月的最后一个工作日，即当前月的最后一个星期五。

（9）#：用于确定每个月第几个星期几，只能出现在DayofMonth域。例如在4#2，表示当前月的第二个星期三。

## example

```
0 0 10,14,16 * * ? 每天上午10点，下午2点，4点

0 0/30 9-17 * * ? 朝九晚五工作时间内每半小时

0 0 12 ? * WED 表示每个星期三中午12点

"0 0 12 * * ?" 每天中午12点触发

"0 15 10 ? * *" 每天上午10:15触发

"0 15 10 * * ?" 每天上午10:15触发

"0 15 10 * * ? *" 每天上午10:15触发

"0 15 10 * * ? 2005" 2005年的每天上午10:15触发

"0 * 14 * * ?" 在每天下午2点到下午2:59期间的每1分钟触发

"0 0/5 14 * * ?" 在每天下午2点到下午2:55期间的每5分钟触发

"0 0/5 14,18 * * ?" 在每天下午2点到2:55期间和下午6点到6:55期间的每5分钟触发

"0 0-5 14 * * ?" 在每天下午2点到下午2:05期间的每1分钟触发

"0 10,44 14 ? 3 WED" 每年三月的星期三的下午2:10和2:44触发

"0 15 10 ? * MON-FRI" 周一至周五的上午10:15触发

"0 15 10 15 * ?" 每月15日上午10:15触发

"0 15 10 L * ?" 每月最后一日的上午10:15触发

"0 15 10 ? * 6L" 每月的最后一个星期五上午10:15触发

"0 15 10 ? * 6L 2002-2005" 2002年至2005年的每月的最后一个星期五上午10:15触发

"0 15 10 ? * 6#3" 每月的第三个星期五上午10:15触发
```

```
//前6个字段分别表示：
//       秒钟：0-59
//       分钟：0-59
//       小时：1-23
//       日期：1-31
//       月份：1-12
//       星期：0-6（0 表示周日）

//还可以用一些特殊符号：
//       *： 表示任何时刻
//       ,： 表示分割，如第三段里：2,4，表示 2 点和 4 点执行
//       －：表示一个段，如第三端里： 1-5，就表示 1 到 5 点
//       /n : 表示每个n的单位执行一次，如第三段里，*/1, 就表示每隔 1 个小时执行一次命令。也可以写成1-23/1.
/////////////////////////////////////////////////////////
//  0/30 * * * * *                        每 30 秒 执行
//  0 43 21 * * *                         21:43 执行
//  0 15 05 * * *                         05:15 执行
//  0 0 17 * * *                          17:00 执行
//  0 0 17 * * 1                          每周一的 17:00 执行
//  0 0,10 17 * * 0,2,3                   每周日,周二,周三的 17:00和 17:10 执行
//  0 0-10 17 1 * *                       毎月1日从 17:00 到 7:10 毎隔 1 分钟 执行
//  0 0 0 1,15 * 1                        毎月1日和 15 日和 一日的 0:00 执行
//  0 42 4 1 * *                          毎月1日的 4:42 分 执行
//  0 0 21 * * 1-6                        周一到周六 21:00 执行
//  0 0,10,20,30,40,50 * * * *            每隔 10 分 执行
//  0 */10 * * * *                        每隔 10 分 执行
//  0 * 1 * * *                           从 1:0 到 1:59 每隔 1 分钟 执行
//  0 0 1 * * *                           1:00 执行
//  0 0 */1 * * *                         毎时 0 分 每隔 1 小时 执行
//  0 0 * * * *                           毎时 0 分 每隔 1 小时 执行
//  0 2 8-20/3 * * *                      8:02,11:02,14:02,17:02,20:02 执行
//  0 30 5 1,15 * *                       1 日 和 15 日的 5:30 执行
```