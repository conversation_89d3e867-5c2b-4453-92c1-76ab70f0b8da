package global

/*
// etcd 注入到 bootstrap Container
func (g *Global) bootEtcd() {
	xlog.Info("boot etcd")

	addrs := []string{config.Get().Etcd.Address}
	etcd, err := clientV3.New(clientV3.Config{
		Endpoints:   addrs,
		DialTimeout: 3 * time.Second,
	})
	if err != nil {
		os.Exit(1)
	}

	timeoutCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	for _, addr := range addrs {
		_, err = etcd.Status(timeoutCtx, addr)
		if err != nil {
			os.Exit(1)
			return
		}
	}

	g.Set("etcd", etcd) //注入到 bootstrap Container
}

// GetEtcd instance to performing etcd operations
func (g *Global) GetEtcd() (*clientV3.Client, error) {
	res, err := g.Get("etcd")
	if err != nil {
		return nil, err
	}
	return res.(*clientV3.Client), nil
}
*/
