package global

import (
	"context"
	"os"

	"bs.com/app/pkg/orm"
	"bs.com/app/pkg/xlog"

	"bs.com/app/config"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

// WithContext 实际是调用 db.Session(&Session{Context: ctx})，每次创建新 Session，各 db 操作之间互不影响：
func DB() *gorm.DB {
	if G.DB == nil {
		xlog.Error("need init db")
		os.Exit(0)
	}
	return G.DB.WithContext(context.Background())
}

func InitAppMysql(dbname string) {
	pwd, addr := config.GetMysqlConfig()
	level := viper.GetString("service_gw.db_log_level")
	db := orm.InitGormMysql(dbname, pwd, addr, level)

	G.DB = db
}

func InitPostgres(dbname string) {
	cfg := config.GetPostgresConfig()
	level := viper.GetString("service_gw.db_log_level")
	db := orm.InitPG(dbname, cfg.Username, cfg.Password, cfg.Address, level)

	G.DB = db
}

func SetPGSchema(schema_name string) {
	G.DB = orm.SetSchema(G.DB, schema_name)
}

func PgCheckOrCreate(dbname string) bool {
	cfg := config.GetPostgresConfig()
	return orm.PgCheckOrCreateDatabase(dbname, cfg.Username, cfg.Password, cfg.Address)
}
