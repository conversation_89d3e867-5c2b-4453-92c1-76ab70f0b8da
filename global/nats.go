package global

import (
	"context"
	"os"

	"bs.com/app/config"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xnats"
)

func (g *Global) InitNats() error {
	natsUrl := config.Get().Nats.Url
	ctx := context.Background()

	// 初始化三个 stream
	xlog.InfoMsg("new nats client, nats server : ", natsUrl)
	if natsUrl == "" {
		xlog.Errorf("nats url is: %s", natsUrl)
		os.Exit(-1)
	}

	var err error
	g.NatsClient, err = xnats.NewJetStreamClient(natsUrl)
	if err != nil {
		return err
	}
	// 添加三个流
	err = g.NatsClient.CreateStream(ctx, xnats.StreamConfig{
		Name:     xnats.STREAM_BUSINESS,
		Subjects: []string{"business.>"},
	})
	if err != nil {
		return err
	}

	err = g.NatsClient.CreateStream(ctx, xnats.StreamConfig{
		Name:     xnats.STREAM_THING,
		Subjects: []string{"thing.>"},
	})
	if err != nil {
		return err
	}
	err = g.NatsClient.CreateStream(ctx, xnats.StreamConfig{
		Name:     xnats.STREAM_TEMP,
		Subjects: []string{"temp.>"},
	})
	if err != nil {
		return err
	}

	return nil
}
