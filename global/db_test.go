package global

import (
	"fmt"
	"testing"
	"time"
)

func TestRedis(t *testing.T) {
	address := "127.0.0.1:6379"
	password := ""
	k := "key0182765456"
	r := newRedis(address, password, 0)

	var n int64
	var err error

	err = r.Set(k, 1, 3)
	if err != nil {
		return
	}

	n, err = r.TTL(k)
	fmt.Println("ttl : ", n, err)

	//err = r.Set(k, 1, 100)

	time.Sleep(2 * time.Second)
	n, err = r.TTL(k)
	fmt.Println("ttl : ", n, err)

	//_= r.Expire(k, 10)

	time.Sleep(2 * time.Second)
	n, err = r.TTL(k)
	fmt.Println("ttl : ", n, err)

	//var ret int
	//ret, err = r.GetInt(k)
	//fmt.Println("get : ", ret, err)

	err= r.Expire(k, 10)
	fmt.Println("expire err = ", err)

	time.Sleep(2 * time.Second)
	n, err = r.TTL(k)
	fmt.Println("ttl : ", n, err)
}
