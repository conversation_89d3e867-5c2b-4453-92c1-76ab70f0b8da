package global

import (
	"context"
	"os"
	"time"

	"bs.com/app/config"
	"bs.com/app/pkg/xlog"
	"github.com/redis/go-redis/v9"
)

// Redis cache implement
type Redis struct {
	Client *redis.Client
}

func Cache() *Redis {
	return G.Cache
}
func InitRedis(num int) *Redis {
	addr, passwd := config.GetRedisConfig()
	xlog.Infof("redis addr: %s, password : %s", addr, passwd)

	G.Cache = newRedis(addr, passwd, num)
	return G.Cache
}

func newRedis(addr, passwd string, num int) *Redis {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: passwd,
		DB:       num,
	})
	one := &Redis{
		Client: client,
	}

	_, err := client.Ping(context.Background()).Result()
	if err != nil {
		// fmt.Println("init redis failed")
		xlog.Error("init redis failed", err)
		os.Exit(1)
	}

	return one
}

func (*Redis) String() string {
	return "redis"
}

// 全局ctx
var ctx = context.Background()

// Get from key
func (r *Redis) GetString(key string) (string, error) {
	return r.Client.Get(ctx, key).Result()
}
func (r *Redis) GetFloat64(key string) (float64, error) {
	return r.Client.Get(ctx, key).Float64()
}
func (r *Redis) GetInt(key string) (int, error) {
	return r.Client.Get(ctx, key).Int()
}
func (r *Redis) GetInt64(key string) (int64, error) {
	return r.Client.Get(ctx, key).Int64()
}

// Set value with key and expire time
func (r *Redis) Set(key string, val interface{}, expire int) error {
	return r.Client.Set(ctx, key, val, time.Duration(expire)*time.Second).Err()
}

// Del delete key in redis
func (r *Redis) Del(key string) error {
	return r.Client.Del(ctx, key).Err()
}

// 使用通配符
func (r *Redis) DelAll(prefix string) error {
	iter := r.Client.Scan(ctx, 0, prefix, 0).Iterator()
	for iter.Next(ctx) {
		err := r.Client.Del(ctx, iter.Val()).Err()
		if err != nil {
			return err
		}
	}
	return iter.Err()
}

// hashSet
func (r *Redis) HashSet(hk string, values map[string]interface{}) error {
	return r.Client.HSet(ctx, hk, values).Err()
}

// HashGet from key
func (r *Redis) HashGet(hk, key string) (string, error) {
	return r.Client.HGet(ctx, hk, key).Result()
}
func (r *Redis) HashExist(hk, key string) bool {
	return r.Client.HExists(ctx, hk, key).Val()
}

// HashDel delete key in specify redis's hashtable
func (r *Redis) HashDel(hk, key string) error {
	return r.Client.HDel(ctx, hk, key).Err()
}

// Increase
func (r *Redis) Increase(key string) error {
	return r.Client.Incr(ctx, key).Err()
}

func (r *Redis) Decrease(key string) error {
	return r.Client.Decr(ctx, key).Err()
}

// Set ttl:second
func (r *Redis) Expire(key string, expire int64) error {
	dur := time.Duration(expire) * time.Second
	return r.Client.Expire(ctx, key, dur).Err()
}

func (r *Redis) IncrBy(ctx context.Context, key string, val int64) (uint64, error) {
	return r.Client.IncrBy(ctx, key, val).Uint64()
}

func (r *Redis) TTL(key string) (int64, error) {
	result := r.Client.TTL(ctx, key)
	return int64(result.Val().Seconds()), result.Err()
}
