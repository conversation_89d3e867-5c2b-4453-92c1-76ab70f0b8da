package global

import (
	"fmt"
	"os"
	"time"

	"bs.com/app/config"
	"bs.com/app/pkg/xjwt"
	"bs.com/app/pkg/xlog"
)

// 检查 license
func CheckLicense2() {
	// TODO：通过和宿主机程序通讯，实现程序运行合法性检查
}

func CheckLicense() {
	cfg := config.Get()
	id, license := cfg.DeployID, cfg.DeployLicense
	ret := xjwt.ParseLicense(id, license)
	if ret {
		xlog.Info(">>>>>>>>>>>> check license OK")
		fmt.Println(">>>>>>>>>>>>")
		fmt.Println(">>>>>>>>>>>> check license OK")
		fmt.Println(">>>>>>>>>>>>")
	} else {
		xlog.Info(">>>>>>>>>>>> check license failed")
		fmt.Println(">>>>>>>>>>>>")
		fmt.Println(">>>>>>>>>>>> id : ", id)
		fmt.Println(">>>>>>>>>>>> license : ", license)
		fmt.Println(">>>>>>>>>>>> check license failed")
		fmt.Println(">>>>>>>>>>>>")
		time.Sleep(60 * time.Second) //方便查看容器日志，避免容器频繁重启
		os.Exit(1)
	}
}
