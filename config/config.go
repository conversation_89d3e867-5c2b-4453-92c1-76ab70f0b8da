package config

import (
	"fmt"
	"os"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"

	"bs.com/app/pkg/xlog"
)

var cfg *Config

func ParseConfig(filename string) {
	xlog.Info("parse config file : " + filename)
	viper.SetConfigFile(filename)
	viper.AddConfigPath(".")
	err := viper.ReadInConfig()
	if err != nil {
		fmt.Println("readin failed:", err)
		return
	}

	cfg = &Config{}
	err = viper.Unmarshal(cfg, func(config *mapstructure.DecoderConfig) {
		config.TagName = "yaml"
	})
	if err != nil {
		xlog.Error("yaml unmarshal failed: ", err)
		os.Exit(1)
	}

	cfg.Misc.CDNDir = "cdn"
	cfg.Misc.PathExport = "cdn/export"
	cfg.Misc.PathFirmware = "cdn/firmware"
	cfg.Misc.PathImage = "cdn/images"
	cfg.Misc.PathTmp = "cdn/tmp"
	cfg.Misc.PathLog = "cdn/logs"
	xlog.Debug("config misc", "misc", cfg.Misc)

	//在不同的机器上运行的时候，请保证有一些字符串，必须不可以重复。包括：

	xlog.Debug("parse config")
}

func Get() *Config {
	return cfg
}
