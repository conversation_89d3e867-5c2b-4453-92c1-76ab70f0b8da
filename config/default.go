package config

const (
	// mqtt
	DefaultMqttBrokerHost       = "127.0.0.1:1883"
	DefaultMqttHeartbeatTimeout = 60

	// nats
	DefaultNatsUrl = "nats://127.0.0.1:4222"

	// redis
	DefaultRedisAddress  = "127.0.0.1:6379"
	DefaultRedisPassword = "123456"

	// postgresql
	DefaultPostgresAddress  = "127.0.0.1:5432"
	DefaultPostgresUsername = "root"
	DefaultPostgresPassword = "root1024"
	DefaultPostgresDBName   = "app"

	// minio
	DefaultMinioEndpoint      = "127.0.0.1:9000"
	DefaultMinioDomain        = "https://nuc.beithing.com:59000"
	DefaultMinioAccessKey     = "beithing"
	DefaultMinioSecretKey     = "Root@1024Minio"
	DefaultMinioBucketImage   = "ebox-image"
	DefaultMinioBucketOther   = "ebox-other"
	DefaultMinioBucketProject = "ebox-project"
)
