package config

import (
	"os"
)

//数据库相关配置，一般没有必要放在配置文件里面
/*
1、优先读取环境变量
2、如果环境变量没有设置，则读取程序里面的默认值

#mysql
export DEFAULT_MYSQL_ADDRESS="127.0.0.1:3306"
export DEFAULT_MYSQL_PASSWORD="123456"

#redis
export DEFAULT_REDIS_ADDRESS="127.0.0.1:6379"
export DEFAULT_REDIS_PASSWORD="123456"
*/

// 获取环境变量信息
func getEnvWithDefault(key, defVal string) string {
	val, ex := os.LookupEnv(key)
	if !ex {
		return defVal
	}
	return val
}

func GetMysqlConfig() (password, address string) {
	//如果定义了环境变量则使用环境变量，否则使用配置文件
	password = getEnvWithDefault("DEFAULT_MYSQL_PASSWORD", cfg.Mysql.Password)
	address = getEnvWithDefault("DEFAULT_MYSQL_ADDRESS", cfg.Mysql.Address)
	return
}

func GetPostgresConfig() PostgresConfig {
	//如果定义了环境变量则使用环境变量，否则使用配置文件
	pgcfg := PostgresConfig{}
	pgcfg.Username = getEnvWithDefault("DEFAULT_POSTGRES_USERNAME", cfg.Postgres.Username)
	pgcfg.Password = getEnvWithDefault("DEFAULT_POSTGRES_PASSWORD", cfg.Postgres.Password)
	pgcfg.Address = getEnvWithDefault("DEFAULT_POSTGRES_ADDRESS", cfg.Postgres.Address)
	pgcfg.AppDBName = getEnvWithDefault("DEFAULT_POSTGRES_DBNAME", cfg.Postgres.AppDBName)
	return pgcfg
}

func GetRedisConfig() (addr, password string) {
	addr = getEnvWithDefault("DEFAULT_REDIS_ADDRESS", cfg.Redis.Address)
	password = getEnvWithDefault("DEFAULT_REDIS_PASSWORD", cfg.Redis.Password)
	return
}
