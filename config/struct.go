package config

type Config struct {
	DeployID      string `yaml:"deploy_id"          json:"deploy_id"`
	DeployLicense string `yaml:"deploy_license"     json:"deploy_license"`

	//==============================================================================
	ServiceApp *configServiceApp `yaml:"service_app" json:"service_app"`

	// // http 子服务
	ServiceBuca *configHttpService `yaml:"service_buca" json:"service_buca"`

	// jt808 service
	ServiceJt808 *configJt808Server `yaml:"service_jt808" json:"service_jt808"`

	EboxConfig *EboxConfig `yaml:"ebox"   json:"ebox"`

	//其他公共配置
	Misc *Misc `yaml:"misc"  json:"misc"` //其他配置

	//第三方服务相关配置
	// <PERSON><PERSON>yun      `yaml:"aliyun"  json:"aliyun"` //阿里云服务
	// AMap   AMap        `yaml:"amap"  json:"amap"`     //高德地图服务
	Minio *MinioConfig `yaml:"minio" json:"minio"` // 文件服务器
	Nats  *NatsConfig  `yaml:"nats" json:"nats"`   // nats服务
	Mqtt  *MqttConfig  `yaml:"mqtt" json:"mqtt"`   // nats服务

	Influxdb2 *Influxdb2Config `yaml:"influxdb2" json:"influxdb2"`
	Redis     *RedisConfig     `yaml:"redis" json:"redis"` // redis

	// mysql
	Mysql *MysqlConfig `yaml:"mysql" json:"mysql"` // mysql
	// psql
	Postgres *PostgresConfig `yaml:"postgres" json:"postgres"` // postgres

	// ms sql
	MSSql *MSSqlConfig `yaml:"mssql"     json:"mssql"`
}

// ===========================================================================================================
type configServiceApp struct {
	Name       string `yaml:"name"            json:"name"`         //server name
	HttpPort   uint16 `yaml:"http_port"       json:"http_port"`    //http port
	DBLogLevel string `yaml:"db_log_level"    json:"db_log_level"` //默认gorm日志等级，当设置为 debug，开启  gorm debug 模式
}

//===========================================================================================================

type configHttpService struct {
	Name string `yaml:"name" json:"name"` //server name
	Host string `yaml:"host" json:"host"` //server host,ip+port or domain+port or container_name+port
}

// sl651 server, use sl651
type ConfigTcpService struct {
	Name             string `yaml:"name" json:"name"`                           //server name
	Port             uint16 `yaml:"port" json:"port"`                           //port
	HeartbeatTimeout int    `yaml:"heartbeat_timeout" json:"heartbeat_timeout"` //心跳间隔
	HeartbeatPacket  bool   `yaml:"heartbeat_packet" json:"heartbeat_packet"`   //是否记录心跳包原始报文，调试用，默认 false
	ForwardHost      string `yaml:"forward_host"  json:"forward_host"`          // 如有数据转发需求，设置这里
}

type ConfigUdpService struct {
	Name             string `yaml:"name" json:"name"`                           //server name
	Port             uint16 `yaml:"port" json:"port"`                           //port
	HeartbeatTimeout int    `yaml:"heartbeat_timeout" json:"heartbeat_timeout"` //心跳间隔
	HeartbeatPacket  bool   `yaml:"heartbeat_packet" json:"heartbeat_packet"`   //是否记录心跳包原始报文，调试用，默认 false
}

type configJt808Server struct {
	Name             string `yaml:"name" json:"name"`                           //server name
	Port             uint16 `yaml:"port" json:"port"`                           //port
	HeartbeatTimeout int    `yaml:"heartbeat_timeout" json:"heartbeat_timeout"` //心跳间隔
	// 鉴权信息
	AccessPointID   string `yaml:"access_point_id" json:"access_point_id"`     //接入点ID
	AccessPointCode string `yaml:"access_point_code" json:"access_point_code"` //接入点编码
	DeviceTypeID    string `yaml:"device_type_id" json:"device_type_id"`       //设备类型ID
	DeviceTypeCode  string `yaml:"device_type_code" json:"device_type_code"`   //设备类型编码
}

// Only For NC1800
type configServiceProxy struct {
	Name      string `yaml:"name" json:"name"`             //server name
	Port      uint16 `yaml:"port" json:"port"`             //port
	ProductID string `yaml:"product_id" json:"product_id"` //虚拟设备产品ID
	HostSl651 string `yaml:"host_sl651" json:"host_sl651"` //默认此接入服务器分发 sl651 数据的目的主机 IP 地址

	//cloud mqtt clientID,分发 iot 数据
	MqttClientID string `yaml:"mqtt_client_id" json:"mqtt_client_id"`
}

// =================================================================================================
type Misc struct {
	CDNDir       string `yaml:"cdn_dir" json:"cdn_dir"`             //静态文件服务的根目录
	PathFirmware string `yaml:"path_firmware" json:"path_firmware"` //软件文件
	PathImage    string `yaml:"path_image" json:"path_image"`       //图片文件
	PathExport   string `yaml:"path_export" json:"path_export"`     //整编文件地址
	PathTmp      string `yaml:"path_tmp" json:"path_tmp"`           //临时文件地址
	PathLog      string `yaml:"path_log" json:"path_log"`           //日志文件
	LogLevel     string `yaml:"log_level" json:"log_level"`         //默认日志等级
	Format       string `yaml:"format" json:"format"`               //默认日志格式
}

// 阿里云：短信、文件存储
type Aliyun struct {
	OssBucketImage string `yaml:"oss_bucket_image" json:"oss_bucket_image"` //存放设备图像
	OssBucketOther string `yaml:"oss_bucket_other" json:"oss_bucket_other"` //存放其他文件
	//Endpoint        string `yaml:"endpoint" json:"endpoint"`
	//AccessKeyId     string `yaml:"access_key_id" json:"access_key_id"`
	//AccessKeySecret string `yaml:"access_key_secret" json:"access_key_secret"`
}

// 高德地图
type AMap struct {
	AmapKey string `yaml:"amap_key" json:"amap_key"`
}

// minio
type MinioConfig struct {
	Endpoint      string `yaml:"endpoint" json:"endpoint"`
	Domain        string `yaml:"domain" json:"domain"` // 文件外部访问的url域名前缀
	AccessKey     string `yaml:"access_key" json:"access_key"`
	SecretKey     string `yaml:"secret_key" json:"secret_key"`
	BucketImage   string `yaml:"bucket_image" json:"bucket_image"`      //存放设备图像
	BucketOther   string `yaml:"bucket_other" json:"bucket_other"`      //存放其他文件
	BucketProject string `yaml:"bucket_project"  json:"bucket_project"` // 存放project相关文件(用户可读写)
}

// nats
type NatsConfig struct {
	Url string `yaml:"url" json:"url"`
}

type MqttConfig struct {
	MqttBrokerHost       string `yaml:"mqtt_broker_host" json:"mqtt_broker_host"`             //地址
	MqttHeartbeatTimeout int    `yaml:"mqtt_heartbeat_timeout" json:"mqtt_heartbeat_timeout"` //心跳间隔
}

// mysql
type MysqlConfig struct {
	Address   string `yaml:"address"      json:"address"`
	Password  string `yaml:"password"     json:"password"`
	AppDBName string `yaml:"app_db_name"  json:"app_db_name"` // 有可能有多个dbname
}

type PostgresConfig struct {
	Address   string `yaml:"address"      json:"address"`
	Username  string `yaml:"username"     json:"username"`
	Password  string `yaml:"password"     json:"password"`
	AppDBName string `yaml:"app_db_name"  json:"app_db_name"` // 有可能有多个dbname
}

// ms sql
type MSSqlConfig struct {
	Host      string `json:"host"`      //*************
	Port      uint16 `json:"port"`      //31433
	Username  string `json:"username"`  //sa
	Password  string `json:"password"`  //zhdgps_123456
	AppDBName string `json:"AppDBName"` //adapter
}

type Influxdb2Config struct {
	Address string `yaml:"address"      json:"address"`
	Token   string `yaml:"token"        json:"token"`
	Org     string `yaml:"org"          json:"org"`
	Bucket  string `yaml:"bucket"       json:"bucket"`
}

// redis
type RedisConfig struct {
	Address  string `yaml:"address" json:"address"`
	Password string `yaml:"password" json:"password"`
}

// gb28181的信令服务器配置
type sipServerConfig struct {
	//   server_port: "60060"
	//   password: "987666"
	//   server_domain: "3402000000"
	//   server_id: "34020000002000000001"
	ServerPort   string `yaml:"server_port"     json:"server_port"`
	ServerDomain string `yaml:"server_domain"   json:"server_domain"`
	ServerID     string `yaml:"server_id"       json:"server_id"`
	Password     string `yaml:"password"        json:"password"`
}

// zlmedia的配置
type ZLMediaKitConfig struct {
	// port: 55080
	// host: zlmedia
	Port string `yaml:"port"   json:"port"`
	Host string `yaml:"host"   json:"host"`
}

// ebox 的 key 和 secrect
type EboxConfig struct {
	AppKey     string `yaml:"app_key"       json:"app_key"`
	AppSecrect string `yaml:"app_secrect"   json:"app_secrect"`
}
