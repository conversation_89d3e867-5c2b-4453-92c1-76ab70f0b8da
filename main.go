/**
 *
 *    ____          __
 *   / __/__ ____ _/ /__
 *  / _// _ `/ _ `/ / -_)
 * /___/\_,_/\_, /_/\__/
 *         /___/
 *
 *
 * generate by http://patorjk.com/software/taag/#p=display&f=Small%20Slant&t=Eagle
 */
package main

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/cobra"

	"bs.com/app/cmd"
	"bs.com/app/config"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
)

// 使用编译时自动替代
// var Version string
// var CommitID string
// var BuildTime string
var rootCmd = &cobra.Command{
	Use:   "gw",
	Short: "iot app gw",
	Long:  "service : gateway ",
	Args: func(cmd *cobra.Command, args []string) error {
		if len(args) < 1 {
			_ = cmd.Usage()
			return errors.New("show usage")
		}
		return nil
	},
}

func checkDir() {
	cfg := config.Get()

	//检查文件目录，创建必须的目录
	if err := xutils.MkDirIfNotExist(cfg.Misc.CDNDir); err != nil {
		xlog.Error("checkDir .....failed")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathLog); err != nil {
		xlog.Error("checkDir .....failed")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathFirmware); err != nil {
		xlog.Error("checkDir .....failed")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathImage); err != nil {
		xlog.Error("checkDir .....failed")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathExport); err != nil {
		xlog.Error("checkDir .....failed")
		os.Exit(1)
	}

	if err := xutils.MkDirIfNotExist(cfg.Misc.PathTmp); err != nil {
		xlog.Error("checkDir .....failed")
		os.Exit(1)
	}

}

var configFile string

func initConfig() {
	fmt.Println("we use config file : ", configFile)

	//检查配置文件
	if _, err := os.Stat(configFile); err != nil {
		configFile = filepath.Join(xutils.GetExecPath(), configFile)
		if _, err = os.Stat(configFile); err != nil {
			fmt.Println("config file not found: ", configFile)
			os.Exit(1)
		}
	}

	//解析配置文件
	config.ParseConfig(configFile)

	//初始检查
	checkDir()

}

func main() {
	// 使用方法： ./build/ebox_linux.bin gw --config config.yaml
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "config.yaml", "select configuration file")

	// 设置配置初始化函数，在命令执行前调用
	rootCmd.PersistentPreRun = func(cmd *cobra.Command, args []string) {
		initConfig()
	}

	//添加命令行入口
	rootCmd.AddCommand(cmd.GW)
	rootCmd.AddCommand(cmd.VDmqtt)
	rootCmd.AddCommand(cmd.Jt808)

	_ = rootCmd.Execute()
}
