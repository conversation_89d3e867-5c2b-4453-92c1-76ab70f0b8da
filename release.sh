#!/bin/bash

# 检查是否提供 commit log
if [ -z "$1" ]; then
  echo "Usage: $0 \"commit log\""
  exit 1
fi

COMMIT_LOG=$1

# 获取当前日期生成版本号前缀 yy.mmdd
DATE_PREFIX=$(date +"%y.%m%d")

# 获取当天已有的版本号后缀 xx
EXISTING_TAGS=$(git tag --list "${DATE_PREFIX}.*")
NEXT_NUMBER=1
if [ -n "$EXISTING_TAGS" ]; then
  for TAG in $EXISTING_TAGS; do
    NUMBER=$(echo "$TAG" | awk -F '.' '{print $3}')
    if [ "$NUMBER" -ge "$NEXT_NUMBER" ]; then
      NEXT_NUMBER=$((NUMBER + 1))
    fi
  done
fi

# 生成版本号
VERSION="${DATE_PREFIX}.$(printf "%02d" "$NEXT_NUMBER")"


# 编译测试
echo "--------------------- 开始编译测试"
make

# Check if the build was successful
if [ $? -eq 0 ]; then
  # Remove the build artifacts
  rm -rf build/*
else
  echo "编译失败"
  exit 1
fi

echo "--------------------- 编译测试成功"


# 提交代码到仓库
echo "--------------------- 提交代码到仓库，并发布版本 ${VERSION}"
git add .
git commit -m "$COMMIT_LOG"
git push

# 创建并推送标签
git tag "$VERSION"
git push origin "$VERSION"

echo "--------------------- 开始编译"

# 1、获取最新 commit id
COMMIT_ID=$(git rev-parse HEAD)
BUILD_LDFLAGS="-X 'bs.com/app/global.Version=${VERSION}' -X 'bs.com/app/global.CommitID=${COMMIT_ID}' -X 'bs.com/app/global.CommitLog=${COMMIT_LOG}' -s -w"


# 2、获取构建时间
BUILD_TIME=$(date +"%Y-%m-%d %H:%M:%S")

# 更新构建标志以包含构建时间
BUILD_LDFLAGS="$BUILD_LDFLAGS -X 'test.com/app/version.BuildTime=${BUILD_TIME}'"


# 3、获取主机名
HOSTNAME=$(hostname)
# 获取系统版本号
OS_VERSION=$(uname -a)
# 更新构建标志以包含主机名和系统版本号
BUILD_LDFLAGS="$BUILD_LDFLAGS -X 'test.com/app/version.Hostname=${HOSTNAME}' -X 'test.com/app/version.OSVersion=${OS_VERSION}'"



# Go parameters
BINARY_NAME=ebox
# DATE= `date +%Y%m%d%H%M%S`
goos=$(uname -s | tr '[:upper:]' '[:lower:]')
if [ -z "$goos" ]; then
  echo "Failed to determine OS type."
  exit 1
else
  echo "OS type: $goos"
fi


goarch=amd64

# 编译 Go 项目
mkdir -p build
CGO_ENABLED=0 GOOS=${goos} GOARCH=${goarch} go build -ldflags "$BUILD_LDFLAGS" -o build/${BINARY_NAME}_${goos}.bin
CGO_ENABLED=0 GOOS=${goos} GOARCH=${goarch} go build -ldflags "$BUILD_LDFLAGS" -o build/vdtcp_${goos}.bin  ./cmd/vdtcp/*.go
CGO_ENABLED=0 GOOS=${goos} GOARCH=${goarch} go build -ldflags "$BUILD_LDFLAGS" -o build/vd1800_${goos}.bin   ./cmd/vd1800/*.go
CGO_ENABLED=0 GOOS=${goos} GOARCH=${goarch} go build -ldflags "$BUILD_LDFLAGS" -o build/gw_${goos}.bin    ./cmd/gw/*.go
CGO_ENABLED=0 GOOS=${goos} GOARCH=${goarch} go build -ldflags "$BUILD_LDFLAGS" -o build/pg_${goos}.bin   ./cmd/pg/*.go

if [ $? -ne 0 ]; then
  echo "Go build failed!"
  exit 1
fi

echo "--------------------- 编译成功"
echo