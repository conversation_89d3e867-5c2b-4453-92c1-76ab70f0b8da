package dm

import "strings"

// 提为变量，鉴权用

type mqttConfig struct {
	ClientID string
	Username string
	Password string
}

const (
	PreClientID = "dm.root-device-manger"
	PreUsername = "dm.admin"
)

// 内部服务，判断前缀即可
func IsMqttWhitelist(clientID string, username string, topic string) bool {
	if strings.HasPrefix(clientID, PreClientID) {
		return true
	}
	// 文件相关操作都白名单
	if strings.HasPrefix(topic, "file/") {
		return true
	}

	return false
}
