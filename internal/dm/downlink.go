package dm

import (
	"errors"
	"strings"
	"time"

	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xnats"
	"bs.com/app/pkg/xutils"
	"github.com/nats-io/nats.go/jetstream"
)

// 注册一个 nats 消费者，从 nats 收到下发给设备的消息，并转换成 mqtt 下发
// 下发给设备的消息，参考 bean/mqtt_topic.go 中的定义
func (d *DeviceMan) messageDownOfThing() error {
	defer xutils.Recovery("nats consumer: nats message to mqtt device")

	// 创建消费者
	consumer, err := d.natsClient.CreateConsumer(d.ctx, xnats.STREAM_THING, xnats.ConsumerConfig{
		Name:          "mqtt_thing_down",
		FilterSubject: "thing.down.>",
		AckWait:       30 * time.Second,
		MaxDeliver:    1,
		ReplayPolicy:  jetstream.ReplayInstantPolicy,
	})
	if err != nil {
		return err
	}

	// 通过 nats 下发的 things.down 消息，直接转发给 mqtt
	var consumerContext jetstream.ConsumeContext
	consumerContext, err = d.natsClient.ConsumeMessages(d.ctx, consumer, func(msg jetstream.Msg) error {
		xlog.Info("on nats message: send to mqtt_device", "subject", msg.Subject, "data", string(msg.Data()))
		msg.Ack()

		topic := bean.TopicFromSubject(msg.Subject())
		d.mqttPubMessage(topic, msg.Data())
		return nil
	})
	if err != nil {
		return err
	}

	<-d.ctx.Done() //等待退出
	consumerContext.Stop()
	return nil
}

func (d *DeviceMan) natsDownSync(subject, method string, data map[string]any, timeout int) (*bean.MqttMessage, error) {

	xlog.Debug("callSync", "subject", subject, "method", method, "data", data, "dm", d.Name)

	// topic 检查，必须是 thing.down 开头
	if !strings.HasPrefix(subject, "thing.down.action") {
		return nil, errors.New("topic must be thing.down.action")
	}
	if d.natsClient == nil {
		return nil, errors.New("nats client is nil")
	}

	// 主动请求
	sn := xutils.UUIDSnowFlake()
	msgSend := bean.MqttMessage{
		Version: bean.MqttStdV1,
		Method:  method,
		MsgID:   sn,
		TS:      time.Now().UnixMilli() / 1000,
		Data:    data,
	}
	err := d.natsPubMessage(subject, msgSend)
	if err != nil {
		xlog.Error("callSync: send failed", "err", err)
		return nil, err
	}

	call := &SyncCall{
		SN:    sn,
		Reply: make(chan *bean.MqttMessage),
	}

	d.pengdingCall.Store(sn, call)

	xlog.Debug("wait for reply", "sn", sn)
	// 等待超时
	select {
	case result := <-call.Reply:
		xlog.Debug("callSync: reply", "sn", sn, "result", result)
		call.Close()
		d.pengdingCall.Delete(sn)
		return result, nil
	case <-time.After(time.Second * time.Duration(timeout)):
		// 默认 10s 超时
		xlog.Warn("callSync: request timeout", "sn", sn)
		call.Close()
		d.pengdingCall.Delete(sn)
		return nil, errors.New("request timeout")
	}
}

// 通过消息队列，下发消息到设备，异步操作，可以是设置属性，也可以是查询属性。非同步，不等待回复
// mqttType: attri | event | action | iot
func (d *DeviceMan) MessageDownToDeviceAsync(mqttType string, deviceID string, method string, data map[string]any) error {
	var topic string
	// 查询设备的 deviceTypeID 和 deviceID
	if mqttType == "iot" {
		// iot 消息，使用 productID 和 HardwareID
		hw, err := d.hardwareRepo.FindOne(deviceID)
		if err != nil {
			return err
		}
		xlog.Debug("find hardware", "device_id", deviceID, "product_id", hw.ProductID, "hardware_id", hw.HardwareID)
		topic = bean.GetTopicIotDown(hw.ProductID, hw.HardwareID)
	} else {
		// 设备消息，使用 deviceTypeID 和 deviceID
		dev, err := d.deviceRepo.FindOne(deviceID)
		if err != nil {
			return err
		}
		xlog.Debug("find device", "device_type_id", dev.DeviceTypeID, "device_id", deviceID)
		switch mqttType {
		case "attri":
			topic = bean.GetTopicAttriDown(dev.DeviceTypeID, dev.DeviceID)
		case "event":
			topic = bean.GetTopicEventDown(dev.DeviceTypeID, dev.DeviceID)
		case "action":
			topic = bean.GetTopicActionDown(dev.DeviceTypeID, dev.DeviceID)
			go d.saveCommandSendAndReplyToTsDB(deviceID, method, data, nil)
		default:
			return errors.New("invalid message type")
		}
	}

	// 要使用 nats 的 subject，需要转换
	subject := bean.TopicToSubject(topic)
	msgSend := bean.MqttMessage{
		Version: bean.MqttStdV1,
		Method:  method,
		MsgID:   xutils.UUIDSnowFlake(),
		TS:      time.Now().UnixMilli() / 1000,
		Data:    data,
	}
	err := d.natsPubMessage(subject, msgSend)
	if err != nil {
		xlog.Error("downAsync: send failed", "err", err)
		return err
	}
	return nil
}

// 通过 nats 下发同步消息到设备
// mqttType: attri | event | action | iot
func (d *DeviceMan) MessageDownToDeviceSync(mqttType string, deviceID string, method string, data map[string]any, timeout int) (*bean.MqttMessage, error) {
	var topic string
	// 查询设备的 deviceTypeID 和 deviceID
	if mqttType == "iot" {
		// iot 消息，使用 productID 和 HardwareID
		hw, err := d.hardwareRepo.FindOne(deviceID)
		if err != nil {
			return nil, err
		}
		topic = bean.GetTopicIotDown(hw.ProductID, hw.HardwareID)
	} else {
		// 设备消息，使用 deviceTypeID 和 deviceID
		dev, err := d.deviceRepo.FindOne(deviceID)
		if err != nil {
			return nil, err
		}
		switch mqttType {
		case "attri":
			topic = bean.GetTopicAttriDown(dev.DeviceTypeID, dev.DeviceID)
		case "event":
			topic = bean.GetTopicEventDown(dev.DeviceTypeID, dev.DeviceID)
		case "action":
			topic = bean.GetTopicActionDown(dev.DeviceTypeID, dev.DeviceID)
		}
	}

	// 要使用 nats 的 subject，需要转换
	subject := bean.TopicToSubject(topic)
	resp, err := d.natsDownSync(subject, method, data, timeout)
	if mqttType == "action" {
		go d.saveCommandSendAndReplyToTsDB(deviceID, method, data, resp)
	}
	return resp, err
}

// 保存命令发送和回复到 tsdb
func (d *DeviceMan) saveCommandSendAndReplyToTsDB(deviceID, method string, sendData map[string]any, reply *bean.MqttMessage) {
	xlog.Debug("saveSendAndReplyToTsDB", "device_id", deviceID, "send", sendData, "reply", reply)
	mVal := make(map[string]any)
	mVal[dto.CommandSend] = string(xutils.InterfaceToByte(sendData))
	mVal[dto.SendTs] = time.Now().UnixMilli()
	if reply == nil {
		// 超时，没有返回的情况
		mVal[dto.CommandReply] = map[string]any{}
		err := d.tsRepo.InsertDataCommand(deviceID, method, mVal, time.Now().UnixMilli())
		if err != nil {
			xlog.Error("saveSendAndReplyToTsDB: insert data failed", "err", err)
		} else {
			xlog.Debug("saveSendAndReplyToTsDB: insert data success")
		}
	} else {
		mVal[dto.CommandReply] = string(xutils.InterfaceToByte(reply.Data)) //  reply.Data
		mVal[dto.ReplyTs] = reply.TS
		// 保存到 tsdb
		err := d.tsRepo.InsertDataCommand(deviceID, method, mVal, time.Now().UnixMilli())
		if err != nil {
			xlog.Error("saveSendAndReplyToTsDB: insert data failed", "err", err)
		} else {
			xlog.Debug("saveSendAndReplyToTsDB: insert data success")
		}
	}

}
