package dm

import (
	"context"
	"encoding/json"
	"os"
	"sync"
	"time"

	"bs.com/app/config"
	"bs.com/app/internal/gw/model"
	"bs.com/app/internal/tsdb"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xmqtt"
	"bs.com/app/pkg/xnats"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// 管理 mqtt 消息的上行、下行
// mqtt topic 和 nats subject 按照规则转换
// mqtt 文件下载操作不通过 nats，直接处理了
type DeviceMan struct {
	Name string
	mqttConfig

	ctx    context.Context
	cancel context.CancelFunc

	mqttClient mqtt.Client
	natsClient *xnats.NatsClient

	//用于同步调用,key是消息的 sn，value是 SyncCall，只有当下行消息的时候，才记录到这里
	pengdingCall sync.Map

	qos byte // qos

	// 文件处理器
	fileHandler  *FileHandler
	deviceRepo   *model.DeviceRepo
	hardwareRepo *model.HardwareRepo
	tsRepo       *tsdb.InfluxdbRepo
}

type SyncCall struct {
	SN    string `json:"sn"`
	Reply chan *bean.MqttMessage
}

func (call *SyncCall) Close() {
	close(call.Reply)
}

// 设备管理器
func newDeviceMan(ctx context.Context, mqttCfg mqttConfig) *DeviceMan {
	d := &DeviceMan{
		Name:         "device-manger",
		mqttConfig:   mqttCfg,
		deviceRepo:   model.NewDeviceRepo(),
		hardwareRepo: model.NewHardwareRepo(),
		tsRepo:       tsdb.NewInfluxdbRepo(),
	}

	d.qos = 0 // TCP 本身能保证可靠性
	d.ctx, d.cancel = context.WithCancel(ctx)

	d.fileHandler = NewFileHandler(d)
	cfg := config.Get()

	// mqtt client
	d.mqttClient = xmqtt.NewClientSimple(cfg.Mqtt.MqttBrokerHost, d.ClientID, d.Username, d.Password, cfg.Mqtt.MqttHeartbeatTimeout)

	// nats client
	var err error

	natsUrl := cfg.Nats.Url
	d.natsClient, err = xnats.NewJetStreamClient(natsUrl)
	if err != nil {
		xlog.Error("new nats client failed", "err", err, "natsurl", natsUrl)
		os.Exit(1)
	}

	return d
}

// 与 mqtt broker 连接，处理与设备之间的消息交互
func (d *DeviceMan) Start() {
	xlog.Infof("================== run mqtt proxy")

	// mqtt 订阅：mqtt 分发上行消息到 nats
	go d.messageUp()

	// nats 订阅：nats 转发下行消息到 MQTT 消息
	go d.messageDownOfThing()

	// 初始化文件处理器
	if d.fileHandler == nil {
		d.fileHandler = NewFileHandler(d)
	}

	xlog.Info("mqtt -- nats client connected", "clientID", d.ClientID)
}

// mqtt pub message to device
func (d *DeviceMan) mqttPubMessage(topic string, data any) error {
	xlog.Info("mqtt publish", "topic", topic, "data", data)

	// 根据数据类型进行不同处理
	var payload []byte
	var err error

	switch v := data.(type) {
	case []byte:
		// 如果数据已经是[]byte类型，直接使用
		payload = v
	case string:
		// 如果是字符串，直接转换为字节数组
		payload = []byte(v)
	default:
		// 其他类型需要序列化为JSON
		payload, err = json.Marshal(data)
		if err != nil {
			xlog.Error("marshal data failed", "err", err)
			return err
		}
	}

	// 发布消息
	token := d.mqttClient.Publish(topic, d.qos, false, payload)
	token.WaitTimeout(5 * time.Second)
	return token.Error()
}

// nats pub message
func (d *DeviceMan) natsPubMessage(subject string, data any) error {
	xlog.Info("nats publish", "subject", subject, "data", data)

	// 根据数据类型进行不同处理
	var payload []byte
	var err error

	switch v := data.(type) {
	case []byte:
		// 如果数据已经是[]byte类型，直接使用
		payload = v
	case string:
		// 如果是字符串，直接转换为字节数组
		payload = []byte(v)
	default:
		// 其他类型需要序列化为JSON
		payload, err = json.Marshal(data)
		if err != nil {
			xlog.Error("marshal data failed", "err", err)
			return err
		}
	}
	_, err = d.natsClient.Publish(d.ctx, subject, payload)
	return err
}
