package dm

import (
	"context"
	"time"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"bs.com/app/pkg/xwg"
)

type DeviceManger struct {
}

func NewDeviceManger() xwg.IService {
	return &DeviceManger{}
}

var OneDM *DeviceMan //全局、单例

func (dm *DeviceManger) Run(ctx context.Context) error {

	// 方便开发调试，避免clientID 相同导致的冲突
	OneDM = newDeviceMan(ctx, mqttConfig{
		ClientID: PreClientID + "." + xutils.RandString(5),
		Username: PreUsername + "." + xutils.RandString(5),
		Password: "123456",
	})

	// 需要等待 http server 启动，因为 mqtt 鉴权需要 http server 鉴权支持。
	time.Sleep(3 * time.Second) // 等待 HTTP 服务器启动完成
	go OneDM.Start()

	//正常启动，则阻塞等待被关闭
	<-ctx.Done()
	xlog.Info("vdmqtt Stoped")

	return nil
}

func (dm *DeviceManger) Name() string {
	return "device-manger"
}
