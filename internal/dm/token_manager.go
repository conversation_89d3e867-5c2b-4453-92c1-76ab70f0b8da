package dm

import (
	"bs.com/app/global"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
)

// 令牌管理器
type TokenManager struct {
	secretKey string // 用于签名的密钥
	cache     *global.Redis
}

// 创建令牌管理器
func NewTokenManager() *TokenManager {
	// 使用默认密钥
	secretKey := "default_file_token_secret_key" // 实际应用中应该使用更安全的密钥

	xlog.Info("初始化文件令牌管理器", "secretKey", "[使用默认密钥]")

	return &TokenManager{
		secretKey: secretKey,
		cache:     global.Cache(),
	}
}

// 生成文件访问令牌
func (tm *TokenManager) GenerateToken(fileID string) string {
	return xutils.ComputeHmacMd5(fileID, tm.secretKey)
}

// 验证文件访问令牌
func (tm *TokenManager) VerifyToken(tokenStr, fileID string) bool {
	return tokenStr == tm.GenerateToken(fileID)
}
