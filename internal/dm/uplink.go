package dm

import (
	"encoding/json"
	"os"
	"strings"
	"time"

	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// mqtt 上行消息，转发到 nats，nats consumer 消费和处理

func (d *DeviceMan) messageUp() {
	defer xutils.Recovery("mqtt sub: mqtt message to nats")

	// 启动连接
	xlog.Info("starting mqtt connection", "clientID", d.ClientID)

	// 连接MQTT Broker
	token := d.mqttClient.Connect()
	if !token.WaitTimeout(10 * time.Second) {
		xlog.Error("mqtt connect timeout after 5 seconds", "clientID", d.ClientID)
		os.Exit(1)
	}

	// 检查连接是否有错误
	if err := token.Error(); err != nil {
		xlog.Error("mqtt connect failed", "err", err, "clientID", d.ClientID)
		os.Exit(1)
	}

	// 双重检查连接状态
	if !d.mqttClient.IsConnected() {
		xlog.Error("mqtt client not connected after connect call", "clientID", d.ClientID)
		os.Exit(1) //关键服务，没启动就退出程序
	}

	defer func() {
		d.mqttClient.Disconnect(250)
	}()

	xlog.Debug("mqtt client connected ........... OK", "clientID", d.ClientID)

	// 订阅：所以来自设备的 MQTT 的上行消息，转发到 nats 来分发处理
	token = d.mqttClient.Subscribe("thing/up/#", byte(d.qos), d.thingHandler)
	if !token.WaitTimeout(5 * time.Second) {
		xlog.Errorf("subscribe thing error:%s", token.Error())
		os.Exit(1)
	}

	// 注册文件处理函数
	if d.fileHandler != nil {
		d.fileHandler.RegisterHandlers()
	}

	// 订阅来自 mqtt 的系统消息：设备上线、设备离线
	if token = d.mqttClient.Subscribe("$SYS/brokers/+/clients/+/+", byte(d.qos), d.sysHandler); !token.WaitTimeout(5 * time.Second) {
		xlog.Errorf("subscribe sys error:%s", token.Error())
		os.Exit(1)
	}

	<-d.ctx.Done()
}

// 蒋接收到的 thing 消息转换到 nats 上的
func (d *DeviceMan) thingHandler(client mqtt.Client, msg mqtt.Message) {
	t := msg.Topic()
	xlog.Info("on thingHandler", "topic", t, "payload", string(msg.Payload()))

	// 当做 messageReply 来解析(可以通过 mqtt topic 来专门监听 action reply)
	msgUp := &bean.MqttMessage{}
	err := json.Unmarshal(msg.Payload(), msgUp)
	if err != nil {
		xlog.Error("thingHandler: unmarshal failed", "err", err)
		return
	}

	// 通过 topic 解析出消息类型
	ti, err := bean.GetTopicInfo(msg.Topic())
	if err != nil {
		xlog.Error("thingHandler: get topic info failed", "err", err)
		return
	}

	// 检查是否同步请求的响应
	if ti.Type == bean.MqttTypeAction {
		// code 不等于 0，说明是请求的响应消息
		val, ok := d.pengdingCall.Load(msgUp.MsgID)
		if ok {
			callInfo := val.(*SyncCall)
			callInfo.Reply <- msgUp
		}
	}

	// mqtt 上行消息转换为 nats 消息，发送到对应 stream
	subject := bean.TopicToSubject(t)
	d.natsClient.Publish(d.ctx, subject, msg.Payload())
}

// 系统消息，设备上线、设备离线
type sysStatusMessage struct {
	ClientID        string `json:"client_id"`
	RemoteAddr      string `json:"remote_addr"`
	Username        string `json:"username"`
	Timestamp       int64  `json:"timestamp"`
	Event           string `json:"event"`
	ProtocolVersion int    `json:"protocol_version"`
	CleanSession    bool   `json:"clean_session"`
	Keepalive       int    `json:"keepalive"`
}

// 就不转换到 nats 了，直接处理
func (d *DeviceMan) sysHandler(client mqtt.Client, msg mqtt.Message) {
	t := msg.Topic()
	xlog.Info("on sysHandler", "topic", t)
	data := &sysStatusMessage{}
	err := json.Unmarshal(msg.Payload(), data)
	if err != nil {
		xlog.Error("sysHandler: unmarshal failed", "err", err)
		return
	}
	var status int32
	if data.Event == "connected" {
		xlog.Info("device connected", "clientID", data.ClientID, "remoteAddr", data.RemoteAddr, "username", data.Username)
		status = model.Online
	} else if data.Event == "disconnected" {
		xlog.Info("device disconnected", "clientID", data.ClientID, "remoteAddr", data.RemoteAddr, "username", data.Username)
		status = model.Offline
	}

	// 解析设备ID
	ci, err := bean.GetClientIDInfo(data.ClientID)
	if err != nil {
		xlog.Error("sysHandler: get client id info failed", "err", err)
		return
	}

	// 接入点离线，要把所有接入点的设备都修改为离线。
	// 接入点在线，不修改设备状态。设备状态是在设备接入注册的时候更新的。
	if strings.HasPrefix(data.Username, "ap.") {
		// 接入点
		if data.Event == "disconnected" {
			// 离线
			err = d.deviceRepo.SetOfflineAll(ci.DeviceTypeID)
			if err != nil {
				xlog.Error("sysHandler: set offline failed", "err", err)
			}
		}
		return
	}

	// 是直连设备
	// 查询设备是否存在
	onedev, err := d.deviceRepo.FindOne(ci.DeviceID)
	if err != nil {
		xlog.Warn("sysHandler: device not exist", "deviceID", data.ClientID)
		return
	}

	// 判断是否状态改变
	if onedev.ActiveOnline == status {
		return
	}

	// 更新设备在线状态
	err = d.deviceRepo.Update(&model.Device{
		DeviceID:     data.ClientID,
		ActiveOnline: status,
	})
	if err != nil {
		xlog.Error("natsMessageHandler: update device failed", "err", err)
	}
	return
}
