## mqtt-nats 消息桥接

┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MQTT Broker   │◄──►│  Bridge Service  │◄──►│   NATS Server   │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │MQTT Clients │ │    │ │ MQTT Client  │ │    │ │NATS Clients │ │
│ │             │ │    │ │              │ │    │ │             │ │
│ │ Device A    │ │    │ │ Subscribe    │ │    │ │ Service A   │ │
│ │ Device B    │ │    │ │ Publish      │ │    │ │ Service B   │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    │ ┌──────────────┐ │    └─────────────────┘
                       │ │ NATS Client  │ │
                       │ │              │ │
                       │ │ Subscribe    │ │
                       │ │ Publish      │ │
                       │ └──────────────┘ │
                       └──────────────────┘

## device manage 

- 订阅 mqtt的消息，并分发到 nats。
- 订阅下发到设备的消息，通过 mqtt 发送。
- 支持同步请求，调用设备的方法（action）
- mqtt service client 接入到 broker的鉴权。和设备鉴权不同。


设备有两个关键信息：deviceTypeID 和 deviceID


## nats作为mq的使用规范

- 广播消息
- 负载均衡试的消费消息


内部需要订阅消息的模块：

- 消息预处理  字段过滤、字段重命名、合规转换
- 时序数据库写入 存入 InfluxDB、TDengine、TimescaleDB
- 预警管理  实时判断是否超过阈值、触发规则
- 规则引擎  灵活表达式匹配、联动控制
- Web 推送服务  将设备数据实时推送到前端页面
- 日志/审计记录  记录所有事件流用于后期审计
- 指令响应服务  处理来自用户控制页面或云端下发的指令


MQ 的设计初衷就是 解耦 与 并发消费，你应该做到：一条消息，可以被多个服务“并发”消费，不互相等待、互不依赖。广播 or 队列消费，按场景选择：

- 广播消费（所有服务都收到） ， 数据预处理、规则引擎、Web 推送、报警模块， 多模块并行各自处理
- 竞争消费（任一实例处理），时序入库、批处理、数据聚合，多副本服务均衡负载处理


- 每个服务只专注做一件事，各自加载物模型/规则；
- 做好日志、traceid，方便调试追踪；

## subject 规则

- nats 的 subject 是 由英文句点 . 分隔的多层字符串；
- 通配符； * 匹配单层; > 匹配当前及所有子层

## consumer 的 durable

- durable 不为空的时候，离线消息不丢失
- 每个服务实例使用不同的 durable name，这样每个实例都会收到所有消息，用于广播消息
- 多个服务实例使用相同的 durable name，这样消息会在实例间分发，用于队列消费（负载均衡）

## 确认策略

- AckNonePolicy ：不需要 ack，消息发出后立即被认为已处理（可能丢消息，性能最高）
- AckAllPolicy ：一次 ack 会自动确认该消费者在当前会话中之前的所有未确认消息
- AckExplicitPolicy ：每条消息都必须明确调用 msg.Ack() 来确认


## nats的特点

- Stream 是“消息存储容器”，而 Subject 是消息的“路由名”或“标签”。
- Stream 通过 Subjects 匹配 Subject 来收消息
- Consumer 是“从该 Stream 拉取或接收消息的逻辑处理器”。Consumer 是 Stream 的附属资源。不可以跨 Stream。

划分 stream 的逻辑：

- 性能隔离：单个 Stream 的写入/消费都是串行化存储（磁盘或内存），高并发场景下可能成为瓶颈。
- 生命周期不同：比如设备数据保留 7 天，报警保留 90 天，混在一起不便管理。
- 可靠性不同：有些消息你希望持久化、有的只是临时的，策略不同不宜混用。
- 权限管理：不同业务（如告警、指令）可能需要不同权限分组访问。
- 订阅复杂度：所有 Consumer 都在一个 Stream 下，管理、排查变复杂。

使用需注意

- 流控：每个 Consumer 设置 MaxAckPending 限制堆积
- 消费模式：预警、入库、日志可以是 Pull 模式，WS 推送用 Push
- 权限控制：每个模块的 NATS 账户设置可访问的 Subject 范围
- 持久化策略：不同 Stream 设置 MaxAge、Storage 类型
- 多实例消费负载：每个 Consumer 配置为 Durable，多实例共享名称即可
- 选择广播模式：模块解耦、功能分流
- 选择负载均衡：多实例消费消息、提升处理性能。需要多个 consumer 使用相同的 Durable 名称。

示例：

```
[Stream: STREAM_THING ] ← thing.attr.>
   ├── Consumer: thing_storage → TSDB
   ├── Consumer: thing_rule_engine → 实时计算/流转
   └── Consumer: thing_alert_checker → 判断阈值、日志记录 → alert 发布

[Stream: DEVICE_DEBUG] ← debug/log.>
   ├── frontend_func: 设备调试
   └── frontend_log → 设备日志

<!-- 其他异步操作也可以走 MQ，如推送短信，如生成差分固件等 -->
[Stream: ALERTS] ← alerts.*
   └── alert_logger → 推送短信/微信等
```

## 定义多个stream

- 设备消息： device ，使用 Jetstream 
- 服务之间消息：service ，使用 Jetstream 
- 临时消息：temp，使用 nats client

- 对于需要 ack 的消息，mqtt 订阅到就立即 ack


## ithings分析

nats 把 mqtt 的消息封装并发送到 nats：

subject 是在 mqtt 的基础上做了一些调整。但是我们可以考虑与 mqtt 统一。这样避免不必要的转换。

之所以对 topic 进行调整，是因为实际上内部会做一些封装，比如上下线事件等

消息封装如下（mqtt 和 nats 交换消息的 struct）

```go
	DevPublish struct { //发布消息结构体
		Topic        string    `json:"topic"`  //只用于日志记录
		Handle       MsgHandle `json:"handle"` //对应 mqtt topic的第一个 thing ota config 等等
		Type         string    `json:"type"`   //操作类型 从topic中提取 物模型下就是   property属性 event事件 action行为
		Payload      []byte    `json:"payload"`
		Timestamp    int64     `json:"timestamp"` //毫秒时间戳
		ProductID    string    `json:"productID"`
		DeviceName   string    `json:"deviceName"`
		Explain      string    `json:"explain"`      //内部使用的拓展字段
		ProtocolCode string    `json:"protocolCode"` //如果有该字段则回复的时候也会带上该字段
	}
```

## 其他broker 主题

- 监听设备上下线
- 监听 broker 运行情况

## 下行消息

通过 nats 可以发送消息给设备，支持同步和异步两种操作。

- 封装接口：方便 gw 服务直接调用。如果 dm 是单独的服务，则应该使用 RPC。
- 统一数据结构，避免数据在 nats 和 mqtt 之间转换。
- 跟踪消息流转。带唯一标识 SN。
- 在 gw 记录消息下发的日志。