package dm

import (
	"encoding/json"
	"os"
	"strconv"
	"strings"
	"time"

	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/filecache"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// 文件传输处理器：就不走 nats 转发了
type FileHandler struct {
	dm *DeviceMan
	// 文件缓冲
	filePool *filecache.FileCache
	// 令牌管理器
	tokenManager *TokenManager

	fileRepo *model.FileRepo
}

// 文件上传状态
type FileUploadState struct {
	FileInfo     bean.FileInfo // 文件信息
	FilePath     string        // 本地文件路径
	File         *os.File      // 文件句柄
	ReceivedSize int64         // 已接收大小
	LastUpdate   time.Time     // 最后更新时间
	Completed    bool          // 是否完成
}

// 文件信息
type FileInfo struct {
	FileID   string         // 文件ID
	Name     string         // 文件名
	Path     string         // 本地文件路径
	Size     int64          // 文件大小
	Checksum string         // 校验和
	Meta     map[string]any // 元数据
}

// 创建文件处理器
func NewFileHandler(dm *DeviceMan) *FileHandler {
	// 初始化令牌管理器
	tokenManager := NewTokenManager()

	return &FileHandler{
		dm:           dm,
		tokenManager: tokenManager,
		filePool:     filecache.NewFileCache(),
		fileRepo:     model.NewFileRepo(),
	}
}

// 注册MQTT处理函数
func (h *FileHandler) RegisterHandlers() {
	// 文件信息请求
	h.dm.mqttClient.Subscribe("file/info/+", h.dm.qos, h.handleFileInfo)

	// 文件下载请求
	h.dm.mqttClient.Subscribe("file/download/+/+/+", h.dm.qos, h.handleFileDownload)
}

// 读取固件分片:使用文件缓存
func (h *FileHandler) getFileBuffer(data *bean.ReqOtaSlice) ([]byte, error) {

	buf, ok := h.filePool.Get(data.FileID)
	if ok {
		return buf, nil
	}

	// 如果缓存里面没有，则读取文件
	fw, err := h.fileRepo.FindOne(data.FileID)
	if err != nil {
		return nil, err
	}
	return h.filePool.CacheFile(data.FileID, fw.LocalPath)
}

// 处理文件信息请求
func (h *FileHandler) handleFileInfo(client mqtt.Client, msg mqtt.Message) {
	topic := msg.Topic()
	parts := strings.Split(topic, "/")
	if len(parts) != 3 {
		xlog.Error("无效的文件信息请求主题", "topic", topic)
		return
	}

	fileID := parts[2]
	xlog.Info("收到文件信息请求", "fileID", fileID)

	// 查找文件信息
	fileInfo, err := h.fileRepo.FindOne(fileID)
	if err != nil {
		xlog.Error("未找到文件信息", "fileID", fileID)
		return
	}

	// 响应文件信息
	replyTopic := bean.GetTopicFileInfoReply(fileID)
	response := bean.FileInfo{
		FileID: fileID,
		Name:   fileInfo.Name,
		Size:   fileInfo.Size,
		Md5:    fileInfo.MD5,
		Crc16:  fileInfo.CRC16,
	}

	payload, _ := json.Marshal(response)
	h.dm.mqttClient.Publish(replyTopic, h.dm.qos, false, payload)
	xlog.Info("已发送文件信息", "fileID", fileID)
}

// 处理文件下载请求
func (h *FileHandler) handleFileDownload(client mqtt.Client, msg mqtt.Message) {
	topic := msg.Topic()
	parts := strings.Split(topic, "/")
	if len(parts) != 5 {
		xlog.Error("无效的文件下载请求主题", "topic", topic)
		return
	}

	fileID := parts[2]
	offset, err := strconv.ParseInt(parts[3], 10, 64)
	if err != nil {
		xlog.Error("解析偏移量失败", "offset", parts[3], "err", err)
		return
	}

	length, err := strconv.ParseInt(parts[4], 10, 64)
	if err != nil {
		xlog.Error("解析长度失败", "length", parts[4], "err", err)
		return
	}

	xlog.Info("收到文件下载请求", "fileID", fileID, "offset", offset, "length", length)

	// 解析消息体中的令牌
	var downloadRequest struct {
		Token string `json:"token"`
	}
	// 验证令牌
	if !h.tokenManager.VerifyToken(downloadRequest.Token, fileID) {
		xlog.Error("文件令牌验证失败", "fileID", fileID)
		return
	}

	// 查找文件信息
	buffer, err := h.getFileBuffer(
		&bean.ReqOtaSlice{
			FileID: fileID,
			Offset: uint32(offset),
			Length: uint16(length)},
	)
	if err != nil {
		xlog.Error("未找到文件信息", "fileID", fileID)
		return
	}

	// 检查偏移量和长度
	if offset < 0 || offset >= int64(len(buffer)) {
		xlog.Error("无效的偏移量", "offset", offset, "fileSize", len(buffer))
		return
	}

	// 调整长度，确保不超出文件边界
	if offset+length > int64(len(buffer)) {
		length = int64(len(buffer)) - offset
	}

	// 限制单次下载大小
	if length > 1024 {
		length = 1024
	}

	// 读取文件数据
	data := buffer[offset : offset+length]

	// 计算校验和 (简单使用数据长度作为校验和)
	checksum := xutils.CRC16Modbus(data)

	// 响应文件数据
	replyTopic := bean.GetTopicFileDownloadReply(fileID, offset, int64(checksum))
	h.dm.mqttClient.Publish(replyTopic, h.dm.qos, false, data)
	xlog.Debug("已发送文件数据", "fileID", fileID, "offset", offset, "length", len(data))
}
