package dm

import (
	"context"
	"encoding/json"
	"time"

	"bs.com/app/config"
	"bs.com/app/internal/gw/model"
	"bs.com/app/internal/tsdb"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xnats"
	"bs.com/app/pkg/xwg"
	"github.com/nats-io/nats.go/jetstream"
)

// ----------------------------------------------------------------
// 一个 nats consumer ，处理上行的 mqtt thing 的消息

type ConsumerThings struct {
	tsRepo     tsdb.ITsDBRepo
	natsClient *xnats.NatsClient

	ctx context.Context

	deviceRepo    *model.DeviceRepo
	attributeRepo *model.AttributeRepo
	eventRepo     *model.EventRepo
}

func NewConsumerThing() xwg.IService {
	return &ConsumerThings{
		tsRepo:        tsdb.NewInfluxdbRepo(),
		deviceRepo:    model.NewDeviceRepo(),
		attributeRepo: model.NewAttributeRepo(),
		eventRepo:     model.NewEventRepo(),
	}
}

func (one *ConsumerThings) Run(ctx context.Context) error {
	cfg := config.Get()

	var err error
	one.ctx = ctx
	one.natsClient, err = xnats.NewJetStreamClient(cfg.Nats.Url)
	if err != nil {
		return err
	}
	// 创建消费者，消费上行消息
	consumer, err := one.natsClient.CreateConsumer(ctx, xnats.STREAM_THING, xnats.ConsumerConfig{
		Name:          "mqtt_thing_consumer_dump",
		FilterSubject: "thing.up.>",
		AckWait:       30 * time.Second,
		MaxDeliver:    1,
		AckPolicy:     jetstream.AckExplicitPolicy,
		ReplayPolicy:  jetstream.ReplayInstantPolicy,
	})
	if err != nil {
		return err
	}

	// 消费消息
	var consumerContext jetstream.ConsumeContext
	consumerContext, err = one.natsClient.ConsumeMessages(ctx, consumer, one.natsMessageHandlerOfThing)
	if err != nil {
		return err
	}

	<-ctx.Done() //等待退出

	consumerContext.Stop()
	return nil
}

func (one *ConsumerThings) Name() string {
	return "nats-consumer-dump"
}

func (one *ConsumerThings) natsMessageHandlerOfThing(msg jetstream.Msg) error {
	subject := msg.Subject()
	xlog.Info("on nats message: consumer dump", "subject", subject, "data", string(msg.Data()))
	msg.Ack()

	// 解析 topic
	topic := bean.TopicFromSubject(subject)
	info, err := bean.GetTopicInfo(topic)
	if err != nil {
		xlog.Error("natsMessageHandler: get topic info failed", "err", err)
		return err
	}

	// 收到任何消息，都去更新设备在线状态
	err = one.deviceRepo.SetOnline(info.DeviceID)
	if err != nil {
		xlog.Error("natsMessageHandler: update device online failed", "err", err)
	}

	// 解析消息
	data := bean.MqttMessage{}
	err = json.Unmarshal(msg.Data(), &data)
	if err != nil {
		xlog.Error("natsMessageHandler: unmarshal failed", "err", err)
		return err
	}

	switch info.Type {
	case bean.MqttTypeAttri:
		if data.Method == bean.AttriReport || data.Method == bean.AttriGetReply {
			return one.natsMessageHandlerOfThingAttribute(info, &data)
		}

	case bean.MqttTypeEvent:
		// 处理事件消息
		return one.natsMessageHandlerOfThingEvent(info, &data)

	case bean.MqttTypeAction:
		// 处理动作消息,  这里在下发命令的时候，已经把消息存入数据库了，这里只需要回复即可
		return one.natsMessageHandlerOfThingCommand(info, &data)

	case bean.MqttTypeIot:
		return one.natsMessageHandlerOfThingIot(info, &data)

	default:
		return nil
	}

	return nil
}

// 这里在下发命令的时候，已经把消息存入数据库了，这里只需要回复即可
func (one *ConsumerThings) natsMessageHandlerOfThingCommand(info *bean.TopicInfo, data *bean.MqttMessage) error {
	// 响应消息
	if data.Method == bean.AttriReport {
		reply := bean.MqttMessage{
			Version: data.Version,
			Method:  data.Method,
			MsgID:   data.MsgID,
			TS:      data.TS,
			Code:    200,
			Data:    nil,
		}
		// 发送响应消息
		buf, _ := json.Marshal(reply)
		one.natsClient.Publish(one.ctx, info.Topic, buf)
	}
	return nil
}

// 事件上报
// 解析物模型，存入时序数据库
func (one *ConsumerThings) natsMessageHandlerOfThingEvent(info *bean.TopicInfo, data *bean.MqttMessage) error {
	//
	eventList, err := one.eventRepo.FindByFilter(model.EventFilter{DeviceTypeID: info.DeviceTypeID}, nil)
	if err != nil {
		xlog.Error("natsMessageHandler: find event list failed", "err", err)
		return err
	}
	// 转为 map 方便使用，key 是属性标识符
	var eventMap = make(map[string]*model.Event)
	for _, v := range eventList {
		xlog.Debug("get event", "device_type_id", info.DeviceTypeID, "identifier", v.Identifier)
		eventMap[v.Identifier] = v
	}
	// k：属性标识符，v：属性值
	for k, v := range data.Data {
		eventModel, ok := eventMap[k]
		if !ok {
			xlog.Warn("natsMessageHandler: event not exist", "device_id", info.DeviceID, "event_identifier", k)
			continue
		}
		mVal := make(map[string]any)
		mData, ok := v.(map[string]any)
		if !ok {
			xlog.Error("invalid data type")
			continue
		}
		// 遍历参数
		for _, param := range eventModel.Params {
			switch param.DataType {
			case bean.DataTypeNumber, bean.DataTypeSwitch, bean.DataTypeText, bean.DataTypeEnum: //基础类型，直接存储
				mVal[param.Identifier] = mData[param.Identifier]
			default:
				xlog.Error("invalid data type", "data_type", param.DataType)
				continue
			}
		}
		err = one.tsRepo.InsertDataEvent(info.DeviceID, k, mVal, data.TS)
		if err != nil {
			xlog.Error("natsMessageHandler: insert event data failed", "err", err)
		} else {
			xlog.Debug("insert event data success!", "data", mVal, "ts", data.TS)
		}
	}

	// 响应消息
	if data.Method == bean.AttriReport {
		reply := bean.MqttMessage{
			Version: data.Version,
			Method:  data.Method,
			MsgID:   data.MsgID,
			TS:      data.TS,
			Code:    200,
			Data:    nil,
		}
		// 发送响应消息
		buf, _ := json.Marshal(reply)
		one.natsClient.Publish(one.ctx, info.Topic, buf)
	}
	return nil
}

// 属性上报
// 解析物模型，存入时序数据库
// 默认响应
func (one *ConsumerThings) natsMessageHandlerOfThingAttribute(info *bean.TopicInfo, data *bean.MqttMessage) error {
	// 查询设备的属性
	attr, err := one.attributeRepo.FindByFilter(model.AttributeFilter{DeviceTypeID: info.DeviceTypeID}, nil)
	if err != nil {
		xlog.Error("natsMessageHandler: find attribute failed", "err", err)
		return err
	}

	// 转为 map 方便使用，key 是属性标识符
	var attrMap = make(map[string]*model.Attribute)
	for _, v := range attr {
		xlog.Debug("get attribute", "device_type_id", info.DeviceTypeID, "identifier", v.Identifier, "data_type", v.DataType)
		attrMap[v.Identifier] = v
	}

	// k：属性标识符，v：属性值
	for k, v := range data.Data {
		attriModel, ok := attrMap[k]
		if !ok {
			xlog.Warn("natsMessageHandler: attribute not exist", "device_id", info.DeviceID, "attribute_id", k)
			continue
		}
		// 可以根据物模型描述，对属性进行检查、转换

		mVal := make(map[string]any)
		switch attriModel.DataType {
		case bean.DataTypeNumber, bean.DataTypeSwitch, bean.DataTypeText, bean.DataTypeEnum: //基础类型，直接存储
			mVal[k] = v

		case bean.DataTypeList:
			// TODO: 这个不好处理.因为不确定 list 里面的数值和时间是如何对应的。
			// 比如设备如果 5 分钟一次采样，1 小时一次上报数据，则一次性上报 12 条数据，12 条数据的时间戳需要计算

		case bean.DataTypeObject:
			// 转换为 map[string]any
			mData, ok := v.(map[string]any)
			if !ok {
				xlog.Error("invalid data type", "data_type", attrMap[k].DataType)
				continue
			}
			mVal = mData

		default:
			xlog.Error("invalid data type", "data_type", attrMap[k].DataType)
			continue
		}

		err = one.tsRepo.InsertDataAttr(info.DeviceID, k, mVal, data.TS)
		if err != nil {
			xlog.Error("natsMessageHandler: insert data failed", "err", err)
		} else {
			xlog.Debug("insert data success!", "data", mVal, "ts", data.TS)
		}
	}

	// 响应消息
	if data.Method == bean.AttriReport {
		reply := bean.MqttMessage{
			Version: data.Version,
			Method:  data.Method,
			MsgID:   data.MsgID,
			TS:      data.TS,
			Code:    200,
			Data:    nil,
		}
		// 发送响应消息
		buf, _ := json.Marshal(reply)
		one.natsClient.Publish(one.ctx, info.Topic, buf)
	}
	return nil
}

// 处理hardware 运维消息
func (one *ConsumerThings) natsMessageHandlerOfThingIot(info *bean.TopicInfo, data *bean.MqttMessage) error {
	switch data.Method {
	case bean.Register:
		// 注册设备, 这里好像不容易标准化啊，先适配 jt808
		deviceID := info.DeviceID
		onedev, err := one.deviceRepo.FindOne(deviceID)
		if err != nil {
			// 不存在，需用户先添加
			xlog.Warn("natsMessageHandler: device not exist", "deviceID", deviceID)
		}
		devtype, err2 := one.deviceRepo.FindDeviceTypeByDeviceID(onedev.DeviceTypeID)
		if err2 != nil {
			xlog.Error("natsMessageHandler: find device type failed", "err", err)
		}

		if err == nil && err2 == nil {
			// 更新设备扩展信息
			if onedev.ExtendInfo == nil {
				onedev.ExtendInfo = make(map[string]any)
			}
			meta, ok := data.Data["meta"].(map[string]any)
			if ok {
				// 更新设备扩展信息, 设备类型有什么信息，才保存到设备扩展信息
				if devtype.ExtendInfo != nil {
					for _, ext := range devtype.ExtendInfo {
						if meta[ext.Identifier] != nil {
							onedev.ExtendInfo[ext.Identifier] = meta[ext.Identifier]
						}
					}
				}
			}

			// 注册的时候，更新设备扩展信息，设备在线状态
			err = one.deviceRepo.Update(&model.Device{
				DeviceID:     deviceID,
				ExtendInfo:   onedev.ExtendInfo,
				ActiveOnline: model.Online,
			})
			if err != nil {
				xlog.Error("natsMessageHandler: update device failed", "err", err)
			}
		}

		// 发送响应消息
		reply := bean.MqttMessage{
			Version: data.Version,
			Method:  data.Method,
			MsgID:   data.MsgID,
			TS:      data.TS,
			Code:    200,
			Data:    nil,
		}
		buf, _ := json.Marshal(reply)
		one.natsClient.Publish(one.ctx, info.Topic, buf)
	}
	return nil
}
