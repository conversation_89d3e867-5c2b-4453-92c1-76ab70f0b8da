package apjt808

import (
	"context"
	"fmt"
	"os"
	"time"

	"bs.com/app/config"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xmqtt"
	"bs.com/app/pkg/xutils"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

type apMqttClient struct {
	Name         string
	DeviceTypeID string
	ClientID     string
	Username     string
	Password     string

	ctx    context.Context
	cancel context.CancelFunc

	mqttClient mqtt.Client
	qos        byte // qos
}

func newMqttClient(ctx context.Context) *apMqttClient {
	d := &apMqttClient{
		Name: "mqtt client for ap-jt808",
	}

	// 从 config 文件中解析 mqtt config
	cfg := config.Get()
	if cfg.ServiceJt808 == nil || cfg.Mqtt == nil {
		os.Exit(1)
	}

	deviceTypeID := cfg.ServiceJt808.DeviceTypeID
	d.DeviceTypeID = deviceTypeID
	deviceTypeCode := cfg.ServiceJt808.DeviceTypeCode

	accessPointID := cfg.ServiceJt808.AccessPointID
	accessPointCode := cfg.ServiceJt808.AccessPointCode

	// clientID=GCtUBMRQrx.2730783552763 username=ap.2730783552763.1752208509949.Skn6p8lmXo password=429f9e85ca8e10af9e726ef09cf6bb83727d073b87f3ecd0edb7ba86c12da91c
	xlog.Debug("mqtt client for ap-jt808", "deviceTypeID", deviceTypeID, "deviceTypeCode", deviceTypeCode, "accessPointID", accessPointID, "accessPointCode", accessPointCode)

	// mqtt 三元组
	d.ClientID, d.Username, d.Password = bean.GenMqttAuthInfoForAccessPoint(deviceTypeID, deviceTypeCode, accessPointID, accessPointCode)
	mqttBrokerHost := cfg.Mqtt.MqttBrokerHost
	mqttHeartbeatTimeout := cfg.Mqtt.MqttHeartbeatTimeout
	xlog.Debug("mqtt client for ap-jt808", "clientID", d.ClientID, "username", d.Username, "password", d.Password)

	d.mqttClient = xmqtt.NewClientSimple(mqttBrokerHost, d.ClientID, d.Username, d.Password, mqttHeartbeatTimeout)
	d.qos = 0 // TCP 本身能保证可靠性
	d.ctx, d.cancel = context.WithCancel(ctx)

	return d
}

// 蒋接收到的 mqtt 消息，转发成 jt808 下行消息，发给设备
func (d *apMqttClient) onDownMessage(client mqtt.Client, msg mqtt.Message) {
	t := msg.Topic()
	xlog.Info("on message", "topic", t, "payload", string(msg.Payload()))
}

// 与 mqtt broker 连接，处理与设备之间的消息交互
func (d *apMqttClient) Start() {
	xlog.Info("================== run mqtt proxy", "mqtt client", d.ClientID)

	defer xutils.Recovery("mqtt sub: mqtt message to nats")

	// 启动连接
	xlog.Info("starting mqtt connection", "clientID", d.ClientID)
	token := d.mqttClient.Connect()

	// 等待连接完成，最多等待10秒
	token.WaitTimeout(10 * time.Second)
	if err := token.Error(); err != nil {
		xlog.Error("mqtt connect failed", "err", err, "clientID", d.ClientID)
		os.Exit(1)
	}

	// 双重检查连接状态
	if !d.mqttClient.IsConnected() {
		xlog.Error("mqtt client not connected after connect call", "clientID", d.ClientID)
		os.Exit(1) //关键服务，没启动就退出程序
	}

	defer func() {
		d.mqttClient.Disconnect(250)
	}()

	// 订阅：所有发给此设备的下行消息
	topic := fmt.Sprintf("thing/down/+/%s/+", d.DeviceTypeID)
	token = d.mqttClient.Subscribe(topic, byte(d.qos), d.onDownMessage)
	token.WaitTimeout(5 * time.Second)
	err := token.Error()
	if err != nil {
		xlog.Errorf("subscribe error:%s", err.Error())
		os.Exit(1)
	}

	<-d.ctx.Done()
}

// mqtt pub message to dm
func (d *apMqttClient) pubMessage(topic string, data []byte) error {
	xlog.Info("mqtt publish", "topic", topic, "data", data)
	token := d.mqttClient.Publish(topic, d.qos, false, data)
	token.WaitTimeout(5 * time.Second)
	return token.Error()
}
