package apjt808

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"time"

	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"

	"github.com/cuteLittleDevil/go-jt808/protocol/jt808"
	jtmodel "github.com/cuteLittleDevil/go-jt808/protocol/model"
	"github.com/cuteLittleDevil/go-jt808/service"
	"github.com/cuteLittleDevil/go-jt808/shared/consts"
)

type meTerminal struct {
	deviceTypeID string
	mqttClient   IMqttClient

	// repo         tsdb.ITsDBRepo
	// deviceRepo   *gwmodel.DeviceRepo
}

type IMqttClient interface {
	pubMessage(topic string, data []byte) error
}

func newMeTerminal(deviceTypeID string, mqttClient IMqttClient) *meTerminal {
	return &meTerminal{
		deviceTypeID: deviceTypeID,
		mqttClient:   mqttClient,
		// repo:         tsdb.NewInfluxdbRepo(),
		// deviceRepo:   gwmodel.NewDeviceRepo(),
	}
}

func (m *meTerminal) OnJoinEvent(msg *service.Message, key string, err error) {
	xlog.Info("onJoinEvent", "key", key, "version", msg.JTMessage.Header.ProtocolVersion.String(), "err", err)
}

func (m *meTerminal) OnLeaveEvent(key string) {
	xlog.Info("onLeaveEvent", "key", key)
}

func (m *meTerminal) OnNotSupportedEvent(msg *service.Message) {
	fmt.Printf("not supported command %x\n", msg.ExtensionFields.TerminalData)
}

/*
TODO: 解析数据，通过 mqtt 上报
*/
func (m *meTerminal) OnReadExecutionEvent(msg *service.Message) {
	switch msg.Command {
	case consts.T0200LocationReport:
		var tmp Location
		_ = tmp.Parse(msg.JTMessage)
		// fmt.Println(tmp.T0x0200AdditionDetails.String())  //打印扩展信息

		location := tmp.T0x0200
		// 打印位置信息
		xlog.Info("收到位置上报消息",
			slog.String("终端手机号", msg.JTMessage.Header.TerminalPhoneNo),
			slog.String("协议版本", msg.JTMessage.Header.ProtocolVersion.String()),
			slog.Uint64("报警标志", uint64(location.AlarmSign)),
			slog.Uint64("状态标志", uint64(location.StatusSign)),
			slog.String("纬度", fmt.Sprintf("%.6f度", float64(location.Latitude)/1000000.0)),
			slog.String("经度", fmt.Sprintf("%.6f度", float64(location.Longitude)/1000000.0)),
			slog.Uint64("海拔", uint64(location.Altitude)),
			slog.String("速度", fmt.Sprintf("%.1f km/h", float64(location.Speed)/10.0)),
			slog.Uint64("方向", uint64(location.Direction)),
			slog.String("时间", location.DateTime),
		)

		if v, ok := tmp.Additions[consts.A0x01Mile]; ok {
			xlog.Debug("A0x01Mile", "mile", v.Content.Mile, "customMile", tmp.customMile)
		}
		id := consts.JT808LocationAdditionType(0x33)
		if v, ok := tmp.Additions[id]; ok {
			xlog.Debug("other addition", "value", v.Content.CustomValue, "customValue", tmp.customValue)
		}

		// 发送到 dm
		topic := bean.GetTopicAttriUp(m.deviceTypeID, msg.JTMessage.Header.TerminalPhoneNo)
		msg := bean.MqttMessage{
			Version: bean.MqttStdV1,
			Method:  bean.AttriReport,
			MsgID:   xutils.UUIDSnowFlake(),
			TS:      time.Now().UnixMilli(),
			Data: map[string]any{
				"position": map[string]any{
					"lat":       float64(location.Latitude) / 1000000.0,
					"lng":       float64(location.Longitude) / 1000000.0,
					"altitude":  float64(location.Altitude),
					"speed":     float64(location.Speed) / 10.0,
					"direction": float64(location.Direction),
					"gpstime":   location.DateTime,
				},
			},
		}
		buf, _ := json.Marshal(msg)
		m.mqttClient.pubMessage(topic, buf)

	case consts.T0100Register:

		xlog.Info("T0100Register", "header", xutils.CleanString(msg.JTMessage.Header.String()))
		tmp := &jtmodel.T0x0100{}
		if err := tmp.Parse(msg.JTMessage); err != nil {
			xlog.Error("T0100Register", "err", err)
			return
		}
		xlog.Info("T0100Register", "message", xutils.CleanString(tmp.String()))

		// 注册设备到 gw
		topic := bean.GetTopicIotUp(m.deviceTypeID, msg.JTMessage.Header.TerminalPhoneNo)
		msg := bean.MqttMessage{
			Version: bean.MqttStdV1,
			Method:  bean.Register,
			MsgID:   xutils.UUIDSnowFlake(),
			TS:      time.Now().UnixMilli(),
			Data: map[string]any{
				// 属性值（struct）
				"position": map[string]any{
					"province_id":      tmp.ProvinceID,
					"city_id":          tmp.CityID,
					"manufacturer_id":  tmp.ManufacturerID,
					"terminal_model":   tmp.TerminalModel,
					"terminal_id":      tmp.TerminalID,
					"plate_color":      tmp.PlateColor,
					"license_plate_no": tmp.LicensePlateNumber,
					"version":          tmp.Version,
				},
			},
		}
		buf, _ := json.Marshal(msg)
		m.mqttClient.pubMessage(topic, buf)

	default:
		xlog.Info("not support command", "command", msg.Command)
	}
}

func (m *meTerminal) OnWriteExecutionEvent(msg service.Message) {
	extension := msg.ExtensionFields
	xlog.Debug("OnWriteExecutionEvent", "terminalData", xutils.DumpHexRaw(extension.TerminalData), "platformData", xutils.DumpHexRaw(extension.PlatformData))
}

type Location struct {
	jtmodel.T0x0200
	customMile  int
	customValue uint8
}

func (l *Location) Parse(jtMsg *jt808.JTMessage) error {
	l.T0x0200AdditionDetails.CustomAdditionContentFunc = func(id uint8, content []byte) (jtmodel.AdditionContent, bool) {
		if id == uint8(consts.A0x01Mile) {
			l.customMile = 100
		}
		if id == 0x33 {
			value := content[0]
			l.customValue = value
			return jtmodel.AdditionContent{
				Data:        content,
				CustomValue: value,
			}, true
		}
		return jtmodel.AdditionContent{}, false
	}
	return l.T0x0200.Parse(jtMsg)
}

func (l *Location) OnReadExecutionEvent(_ *service.Message) {

}

func (l *Location) OnWriteExecutionEvent(_ service.Message) {

}
