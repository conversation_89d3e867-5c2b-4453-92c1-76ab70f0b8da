package apjt808

import (
	"context"
	"time"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

type ApJt808 struct {
}

func NewJt808App() xwg.IService {
	return &ApJt808{}
}

func (jt *ApJt808) Run(ctx context.Context) error {

	// 等待 http server 启动，因为 mqtt 鉴权需要 http server 鉴权支持。
	time.Sleep(3 * time.Second)
	go NewJt808Server().Start()

	//正常启动，则阻塞等待被关闭
	<-ctx.Done()
	xlog.Info("jt808 server Stoped")

	return nil
}

func (jt *ApJt808) Name() string {
	return "jt808-server"
}
