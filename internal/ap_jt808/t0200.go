package apjt808

import (
	"encoding/hex"
	"fmt"
	"log/slog"
	"strings"

	"github.com/cuteLittleDevil/go-jt808/protocol/model"
	"github.com/cuteLittleDevil/go-jt808/service"
)

// 位置上报消息处理器
type t0200ReportHandler struct {
	model.T0x0200
}

// 读取事件
func (h *t0200ReportHandler) OnReadExecutionEvent(msg *service.Message) {
	// 可以在这里添加额外的处理逻辑
	slog.Info("收到位置上报消息")
	fmt.Println("header:", msg.JTMessage.Header.String())
	fmt.Println("body:", strings.ToUpper(hex.EncodeToString(msg.Body)))
	// 手动调用 Parse 方法解析消息体
	err := h.Parse(msg.JTMessage)
	if err != nil {
		slog.Error("解析位置上报消息失败", slog.String("error", err.Error()))
		return
	}
	location := h.T0x0200
	// 打印位置信息
	slog.Info("收到位置上报消息",
		slog.String("终端手机号", msg.Header.TerminalPhoneNo),
		slog.Uint64("报警标志", uint64(location.AlarmSign)),
		slog.Uint64("状态标志", uint64(location.StatusSign)),
		slog.String("纬度", fmt.Sprintf("%.6f度", float64(location.Latitude)/1000000.0)),
		slog.String("经度", fmt.Sprintf("%.6f度", float64(location.Longitude)/1000000.0)),
		slog.Uint64("海拔", uint64(location.Altitude)),
		slog.String("速度", fmt.Sprintf("%.1f km/h", float64(location.Speed)/10.0)),
		slog.Uint64("方向", uint64(location.Direction)),
		slog.String("时间", location.DateTime),
	)
	// 打印报警和状态详情
	if location.AlarmSign > 0 {
		fmt.Println("报警详情:\n" + location.AlarmSignDetails.String())
	}

	fmt.Println("状态详情:\n" + location.StatusSignDetails.String())

	fmt.Println()
	fmt.Println()

}

// 写入事件
func (h *t0200ReportHandler) OnWriteExecutionEvent(msg service.Message) {
	// 可以在这里添加额外的处理逻辑
	slog.Info("回复位置上报消息")
	fmt.Println("header:", msg.JTMessage.Header.String())
	fmt.Println("body:", strings.ToUpper(hex.EncodeToString(msg.Body)))
	fmt.Println()

}
