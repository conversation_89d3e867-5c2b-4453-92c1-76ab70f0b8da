package apjt808

import (
	"context"
	"fmt"
	"os"

	"bs.com/app/config"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/cuteLittleDevil/go-jt808/service"
)

type jt808Server struct {
	server *service.GoJT808

	deviceTypeID string
	mqttClient   *apMqttClient //用于消息上报
}

func NewJt808Server() *jt808Server {
	cfg := config.Get()
	deviceTypeID := cfg.ServiceJt808.DeviceTypeID
	return &jt808Server{
		deviceTypeID: deviceTypeID,
		mqttClient:   newMqttClient(context.Background()), // TODO ctx 应该用于生命周期管理
	}
}

func (s *jt808Server) Start() {
	defer xutils.Recovery("jt808 server")

	// 启动 mqtt
	go s.mqttClient.Start()

	cfg := config.Get()
	if cfg.ServiceJt808 == nil {
		os.Exit(1)
	}
	addr := fmt.Sprintf("0.0.0.0:%d", cfg.ServiceJt808.Port)

	xlog.Info("start jt808 server", "addr", addr)
	// 创建服务器实例
	s.server = service.New(
		service.WithHostPorts(addr),
		service.WithNetwork("tcp"),
		service.WithCustomTerminalEventer(func() service.TerminalEventer {
			return newMeTerminal(s.deviceTypeID, s.mqttClient) // 自定义开始 结束 报文处理等事件
		}),
	)
	// 阻塞运行服务器
	s.server.Run()
}
