package vdmqtt

import (
	"strconv"
	"time"

	"github.com/abiosoft/ishell/v2"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

func setBroker() *ishell.Cmd {
	return &ishell.Cmd{
		Name: "setBroker",
		Help: "setBroker host",
		Func: func(c *ishell.Context) {
			if len(c.Args) != 1 {
				c.Println("invalid args: host")
				return
			}
			brokerHost := c.Args[0]
			vd.setBroker(brokerHost)
		},
	}
}

func setDevConfig() *ishell.Cmd {
	return &ishell.Cmd{
		Name: "setDevConfig",
		Help: "setDevConfig deviceTypeID deviceID deviceCode",
		Func: func(c *ishell.Context) {
			if len(c.Args) != 3 {
				c.Println("invalid args: deviceTypeID, deviceID, deviceCode")
				return
			}
			// 绝大多数情况下，一型一密，更简单一些，不需要用 deviceCode
			deviceTypeID := c.Args[0]
			deviceID := c.Args[1]
			deviceTypeCode := c.Args[2]
			vd.setConfig(deviceTypeID, deviceID, deviceTypeCode)
		},
	}
}

func runNumDevice() *ishell.Cmd {
	return &ishell.Cmd{
		Name: "runNumDevice",
		Help: "runNumDevice num deviceTypeID token (其中num是数量,deviceTypeID是设备类型ID,token是登陆后台的token)",
		Func: func(c *ishell.Context) {
			if len(c.Args) != 3 {
				c.Println("invalid args: num,deviceTypeID, token")
				return
			}
			numStr := c.Args[0]
			deviceTypeID := c.Args[1]
			token := c.Args[2]
			num, err := strconv.Atoi(numStr)
			if err != nil {
				c.Println("invalid args: num, type must be int", err)
				return
			}
			vd.runNumDevice(num, deviceTypeID, token)
		},
	}
}

func NewVDmqttShell() xwg.IService {
	xlog.Debug("net vdmqtt shell")
	hi := &ishell.Cmd{
		Name: "hi",
		Help: "hi",
		Func: func(c *ishell.Context) {
			c.Println("hello: ", time.Now().String())
		},
	}

	start := &ishell.Cmd{
		Name: "start",
		Help: "start",
		Func: func(c *ishell.Context) {
			deviceTypeID := deviceTypeID
			deviceID := deviceID
			deviceTypeCode := deviceTypeCode //VdMqtt 鉴权使用
			vd.setConfig(deviceTypeID, deviceID, deviceTypeCode)
		},
	}
	stop := &ishell.Cmd{
		Name: "stop",
		Help: "stop",
		Func: func(c *ishell.Context) {
			if vd.device != nil {
				vd.device.Cancel()
			}
		},
	}

	login := &ishell.Cmd{
		Name: "login",
		Help: "login",
		Func: func(c *ishell.Context) {
			token := login()
			c.Println("token: ", token)
		},
	}

	// 生成 mqtt 三元组，用于测试
	genDevice := &ishell.Cmd{
		Name: "genDevice",
		Help: "genDevice deviceTypeID",
		Func: func(c *ishell.Context) {
			if len(c.Args) != 1 {
				c.Println("invalid args: deviceTypeID")
				return
			}
			deviceTypeID := c.Args[0]
			clientID, username, password := genDevice(deviceTypeID)
			c.Println("clientID: ", clientID)
			c.Println("username: ", username)
			c.Println("password: ", password)
		},
	}

	cmdArr := []*ishell.Cmd{
		hi,
		setDevConfig(),
		setBroker(),
		runNumDevice(),
		start,
		stop,
		login,
		genDevice,
	}
	return xwg.NewXshell("vdmqtt", cmdArr...)
}
