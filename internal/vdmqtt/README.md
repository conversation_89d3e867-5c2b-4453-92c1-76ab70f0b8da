## 使用mqtt的虚拟设备

这是一个虚拟设备，功能功能描述如下：

### 属性

- 温度（自动上报，不可设置）  temperature
- 湿度（自动上报，不可设置）  humidity
- 电量（自动上报，不可设置）  battery
- 温度阈值（不上报，可设置）  temp_threshold
- 湿度阈值（不上报，可设置）  humid_threshold

### 事件

- 温度超过阈值   temp_alarm
- 湿度超过阈值   humid_alarm
- 电池电量低     battery_low


### 命令

- 设置温度阈值  set_temp_threshold
- 设置湿度阈值  set_humid_threshold
- 重启设备  reboot


## 物模型描述

```json
{
  "name": "温湿度计",
  "device_type_id": "3022288739199",
  "access_type": 1,
  "desc": "",
  "icon": "&#xe784;",
  "attributes": [
    {
      "id": 8,
      "created_at": "2025-07-12T16:33:34.30854+08:00",
      "updated_at": "2025-07-12T16:33:34.30854+08:00",
      "device_type_id": "3022288739199",
      "attr_id": "7812132053009",
      "name": "湿度阈值",
      "identifier": "humid_threshold",
      "attr_type": "device",
      "data_type": "number",
      "data_options": {},
      "desc": ""
    },
    {
      "id": 7,
      "created_at": "2025-07-12T16:33:15.738204+08:00",
      "updated_at": "2025-07-12T16:33:15.738204+08:00",
      "device_type_id": "3022288739199",
      "attr_id": "5630555671097",
      "name": "温度阈值",
      "identifier": "temp_threshold",
      "attr_type": "device",
      "data_type": "number",
      "data_options": {},
      "desc": ""
    },
    {
      "id": 6,
      "created_at": "2025-07-12T16:32:51.90378+08:00",
      "updated_at": "2025-07-12T16:32:51.90378+08:00",
      "device_type_id": "3022288739199",
      "attr_id": "6791166705780",
      "name": "电池电量",
      "identifier": "battery",
      "attr_type": "device",
      "data_type": "number",
      "data_options": {},
      "desc": ""
    },
    {
      "id": 5,
      "created_at": "2025-07-12T16:32:23.693194+08:00",
      "updated_at": "2025-07-12T16:32:23.693194+08:00",
      "device_type_id": "3022288739199",
      "attr_id": "8373128099662",
      "name": "湿度",
      "identifier": "humidity",
      "attr_type": "device",
      "data_type": "number",
      "data_options": {},
      "desc": ""
    },
    {
      "id": 4,
      "created_at": "2025-07-12T16:32:10.125473+08:00",
      "updated_at": "2025-07-12T16:32:10.125473+08:00",
      "device_type_id": "3022288739199",
      "attr_id": "6306827633856",
      "name": "温度",
      "identifier": "temperature",
      "attr_type": "device",
      "data_type": "number",
      "data_options": {
        "default": "",
        "max": "",
        "min": "",
        "step": "",
        "unit": ""
      },
      "desc": ""
    }
  ],
  "events": [
    {
      "id": 4,
      "created_at": "2025-07-12T16:36:01.292673+08:00",
      "updated_at": "2025-07-12T16:36:01.292673+08:00",
      "device_type_id": "3022288739199",
      "event_id": "9133028868835",
      "name": "电量低",
      "identifier": "battery_low",
      "desc": "",
      "params": [
        {
          "name": "电池电量",
          "data_type": "number",
          "data_options": {
            "default": "",
            "max": "",
            "min": "",
            "step": "",
            "unit": ""
          },
          "identifier": "battery",
          "desc": ""
        }
      ]
    },
    {
      "id": 3,
      "created_at": "2025-07-12T16:35:32.33785+08:00",
      "updated_at": "2025-07-12T16:35:32.33785+08:00",
      "device_type_id": "3022288739199",
      "event_id": "9537002113828",
      "name": "湿度预警",
      "identifier": "humid_alarm",
      "desc": "",
      "params": [
        {
          "name": "湿度",
          "data_type": "number",
          "data_options": {
            "default": "",
            "max": "",
            "min": "",
            "step": "",
            "unit": ""
          },
          "identifier": "humidity",
          "desc": ""
        },
        {
          "name": "湿度阈值",
          "data_type": "number",
          "data_options": {
            "default": "",
            "max": "",
            "min": "",
            "step": "",
            "unit": ""
          },
          "identifier": "threshold",
          "desc": ""
        }
      ]
    },
    {
      "id": 2,
      "created_at": "2025-07-12T16:34:40.486165+08:00",
      "updated_at": "2025-07-12T16:34:40.486165+08:00",
      "device_type_id": "3022288739199",
      "event_id": "5670238158207",
      "name": "温度超限",
      "identifier": "temp_alarm",
      "desc": "",
      "params": [
        {
          "name": "温度值",
          "data_type": "number",
          "data_options": {
            "default": "",
            "max": "",
            "min": "",
            "step": "",
            "unit": ""
          },
          "identifier": "temperature",
          "desc": ""
        },
        {
          "name": "温度阈值",
          "data_type": "number",
          "data_options": {
            "default": "",
            "max": "",
            "min": "",
            "step": "",
            "unit": ""
          },
          "identifier": "threshold",
          "desc": ""
        }
      ]
    }
  ],
  "commands": [
    {
      "id": 3,
      "created_at": "2025-07-12T16:38:12.017276+08:00",
      "updated_at": "2025-07-12T16:38:12.017276+08:00",
      "device_type_id": "3022288739199",
      "command_id": "2110985187621",
      "name": "设置湿度阈值",
      "identifier": "set_humid_threshold",
      "desc": "",
      "send_params": [
        {
          "name": "湿度阈值",
          "data_type": "number",
          "data_options": {
            "default": "",
            "max": "",
            "min": "",
            "step": "",
            "unit": ""
          },
          "identifier": "value",
          "desc": ""
        }
      ],
      "send_params_default": {},
      "reply_params": [
        {
          "name": "结果",
          "data_type": "text",
          "data_options": {
            "default": ""
          },
          "identifier": "result",
          "desc": ""
        },
        {
          "name": "消息",
          "data_type": "text",
          "data_options": {
            "default": ""
          },
          "identifier": "message",
          "desc": ""
        }
      ],
      "reply_params_default": {}
    },
    {
      "id": 2,
      "created_at": "2025-07-12T16:37:20.624072+08:00",
      "updated_at": "2025-07-12T16:37:20.624072+08:00",
      "device_type_id": "3022288739199",
      "command_id": "2052265309280",
      "name": "设置温度阈值",
      "identifier": "set_temp_threshold",
      "desc": "",
      "send_params": [
        {
          "name": "温度值",
          "data_type": "number",
          "data_options": {
            "default": "",
            "max": "",
            "min": "",
            "step": "",
            "unit": ""
          },
          "identifier": "value",
          "desc": ""
        }
      ],
      "send_params_default": {},
      "reply_params": [
        {
          "name": "结果",
          "data_type": "text",
          "data_options": {
            "default": ""
          },
          "identifier": "result",
          "desc": ""
        },
        {
          "name": "消息",
          "data_type": "text",
          "data_options": {
            "default": ""
          },
          "identifier": "message",
          "desc": ""
        }
      ],
      "reply_params_default": {}
    }
  ]
}

```