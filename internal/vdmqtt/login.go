package vdmqtt

import (
	"fmt"
	"os"
	"strings"

	"bs.com/app/pkg/httpclient"
	"bs.com/app/pkg/xlog"
)

func login() string {

	apiUrl := baseUrl + "/api/user/login"

	fmt.Println("api url: ", apiUrl)

	data := map[string]any{
		"username":    "shujun",
		"password":    "shujun123",
		"verify_code": "12345",
		"captcha_id":  "test",
	}

	resp, err := httpclient.ReqPost(apiUrl, "", data)
	if err != nil {
		xlog.Error("req post failed: " + err.Error())
		return ""
	}
	type typeLoginResp struct {
		AccessToken string `json:"accessToken"`
	}
	loginResp, err := httpclient.ParsePayload[typeLoginResp](resp.Payload)
	if err != nil {
		xlog.Error("parse login resp failed: " + err.Error())
		return ""
	}
	// 解析的 token，存储到当前目录下的 token.txt 文件中
	err = os.WriteFile("token.txt", []byte(loginResp.AccessToken), 0644)
	if err != nil {
		xlog.Error("write token failed: " + err.Error())
		return ""
	}

	return loginResp.AccessToken
}

// 从文件中读取 token 并返回
func loadToken() string {
	data, err := os.ReadFile("token.txt")
	if err != nil {
		// 文件不存在或读取失败，返回空字符串
		xlog.Error("read token failed: " + err.Error())
		return ""
	}
	// 去除可能的换行符和空白字符
	return strings.TrimSpace(string(data))
}
