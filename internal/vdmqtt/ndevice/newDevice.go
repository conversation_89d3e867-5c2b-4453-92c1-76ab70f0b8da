package ndevice

import (
	"fmt"

	"bs.com/app/pkg/httpclient"
)

func (nd *nDevice) addDevice(n int) {
	fmt.Println("总共需要创建设备数量:", n)

	for i := 0; i < n; i++ {
		name := fmt.Sprintf("虚拟设备%d", i)

		fmt.Println("开始执行创建设备名:", name)

		data := map[string]any{
			"name":           name,
			"device_type_id": nd.deviceTypeID,
			"tags":           []map[string]any{},
			"icon":           "&#xe784;",
		}
		apiStr := fmt.Sprintf("%s%s", baseApi, newDevUri)
		resp, err := httpclient.ReqPost(apiStr, nd.token, data)
		if err != nil {
			if resp != nil {
				// 业务错误（Code != 0）
				fmt.Printf("业务错误: Code=%d, Message=%s\n", resp.Code, resp.Message)
			} else {
				// 网络/IO/JSON 错误
				fmt.Printf("请求失败: %v\n", err)
			}
			return
		}
	}
}
