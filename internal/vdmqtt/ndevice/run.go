package ndevice

import (
	"context"
	"fmt"

	"bs.com/app/internal/gw/model"
	"bs.com/app/internal/vdmqtt/device"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/export"
)

type nDevice struct {
	token         string
	num           int
	deviceTypeID  string
	defaultBroker string
}

func NewNDevice(num int, deviceTypeID, token, broker string) *nDevice {
	return &nDevice{
		num:           num,
		token:         token,
		deviceTypeID:  deviceTypeID,
		defaultBroker: broker,
	}
}

func (nd *nDevice) Start(ctx context.Context) {
	// 0.先测试着加载本地的
	var err error
	needCreat := nd.num

	deviceList := []model.Device{}
	// err := export.ImportFromExcel(savePath, "", &deviceList, 0)
	// if err != nil {
	// 	fmt.Println("import from local excel error:", err)
	// } else {
	// 	if len(deviceList) >= needCreat {
	// 		needCreat = 0
	// 	} else {
	// 		needCreat = nd.num - len(deviceList)
	// 	}
	// }
	// 1.创建设备
	if needCreat > 0 {
		nd.addDevice(needCreat)
	}
	// 2. 导出设备列表
	savePathFile, err := nd.exportDevice()
	if err != nil {
		fmt.Println("export device list error:", err)
		// 如果导出失败，则无法启动虚拟设备
		return
	}

	// 3. 解析excel设备列表,并启动虚拟设备
	fmt.Println("设备数量: ", len(deviceList))
	err = export.ImportFromExcel(savePathFile, "", &deviceList, 0)
	if err != nil {
		fmt.Println("import from local excel error:", err)
		return
	}
	nd.startDeviceList(ctx, deviceList)
}

func (nd *nDevice) startDeviceList(ctx context.Context, deviceList []model.Device) {
	for _, dev := range deviceList {
		clientID, username, password := bean.GenMqttAuthInfo(dev.DeviceTypeID, dev.DeviceID, dev.DeviceCode)
		mqttConf := device.MqttConfig{
			Host:         nd.defaultBroker, // broker 地址
			DeviceTypeID: dev.DeviceTypeID,
			DeviceID:     dev.DeviceID,

			// mqtt 三元组
			ClientID: clientID,
			Username: username,
			Password: password,
		}
		vdDev := device.NewDevice(ctx, &mqttConf)
		go vdDev.Run()
	}
}
