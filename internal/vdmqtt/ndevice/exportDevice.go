package ndevice

import (
	"fmt"

	"bs.com/app/pkg/httpclient"
)

type ExportResp struct {
	Export   string `json:"export"` // 导出地址
	Filename string `json:"filename"`
}

func (nd *nDevice) exportDevice() (string, error) {

	data := map[string]any{
		"device_type_id": nd.deviceTypeID,
	}
	apiURL := fmt.Sprintf("%s%s", baseApi, exportDevUri)
	fmt.Println("api url:", apiURL)
	resp, err := httpclient.ReqPost(apiURL, nd.token, data)
	if err != nil {
		if resp != nil {
			// 业务错误（Code != 0）
			fmt.Printf("业务错误: Code=%d, Message=%s\n", resp.Code, resp.Message)
		} else {
			// 网络/IO/JSON 错误
			fmt.Printf("请求失败: %v\n", err)
		}
		return "", err
	}
	exportResp, err := httpclient.ParsePayload[ExportResp](resp.Payload)
	savePathFile := fmt.Sprintf("%s/%s", savePath, exportResp.Filename)
	apiURL2 := fmt.Sprintf("%s/%s", baseUrl, exportResp.Export)
	err = httpclient.DownloadFile(apiURL2, nd.token, savePathFile)
	if err != nil {
		fmt.Println("下载文件错误:", err)
		return "", err
	}
	return savePathFile, nil
}
