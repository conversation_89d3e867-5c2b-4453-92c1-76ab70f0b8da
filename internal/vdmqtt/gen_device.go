package vdmqtt

import (
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/httpclient"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
)

const (
	baseUrl = "http://dev.beithing.com:53006"
)

func genDevice(deviceTypeID string) (string, string, string) {
	token := loadToken()
	if token == "" {
		xlog.Error("token is empty")
		return "", "", ""
	}
	apiUrl := baseUrl + "/api/device/type/one"
	data := map[string]interface{}{
		"device_type_id": deviceTypeID,
	}
	resp, err := httpclient.ReqGet(apiUrl, token, data)
	if err != nil {
		xlog.Error("req get failed: " + err.Error())
		return "", "", ""
	}
	respDt, err := httpclient.ParsePayload[model.DeviceType](resp.Payload)
	if err != nil {
		xlog.Error("parse device type failed: " + err.Error())
		return "", "", ""
	}
	deviceID := xutils.UUIDSnowFlake()
	clientID, username, password := bean.GenMqttAuthInfo(deviceTypeID, deviceID, respDt.DeviceTypeCode)

	return clientID, username, password
}
