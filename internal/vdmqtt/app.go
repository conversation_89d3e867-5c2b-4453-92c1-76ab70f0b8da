package vdmqtt

import (
	"context"
	"time"

	"bs.com/app/internal/vdmqtt/device"
	"bs.com/app/internal/vdmqtt/ndevice"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

// 默认的 broker 的地址
// const DefaultMqttHost = "tcp://127.0.0.1:1883"
// const DefaultMqttHost = "tcp://127.0.0.1:52883"

const (
	// dev test
	DefaultMqttHost = "tcp://*************:52883"
	deviceTypeID    = "KF38HDDeXO"
	deviceID        = "9gZUkH9NgV"
	deviceTypeCode  = "8WsH7xMAv8tN1"

	// local test
	// const DefaultMqttHost = "tcp://127.0.0.1:52883"
	// deviceTypeID = "3022288739199"
	// deviceID = "qgby8nYj7B"
	// deviceTypeCode = "9ijhg7yhjkliu7ytgf" //VdMqtt 鉴权使用
)

var vd *VdInfo

type VdInfo struct {
	name         string
	DeviceTypeID string
	DeviceID     string
	DeviceCode   string

	DefaultBroker string
	// DeviceCode   string // 生成mqtt 认证信息需要
	device *device.Device
	ctx    context.Context
}

func NewMqttDevice() xwg.IService {
	vd = &VdInfo{
		DefaultBroker: DefaultMqttHost,
	}
	return vd
}

func (s *VdInfo) newDev(deviceTypeID, deviceID, deviceTypeCode string) *device.Device {
	clientID, username, password := bean.GenMqttAuthInfo(deviceTypeID, deviceID, deviceTypeCode)

	d1 := device.MqttConfig{
		Host:         s.DefaultBroker, // broker 地址
		DeviceTypeID: deviceTypeID,
		DeviceID:     deviceID,

		// mqtt 三元组
		ClientID: clientID, //  "f7pYiv6xdPV8M.7iughjk987yghjk.7VmJy", // fmt.Sprintf("%s.%s.%s", deviceTypeID, deviceID, "00001"), //clientID 需确保唯一性
		Username: username, // "7iughjk987yghjk",
		Password: password, // "863409ad2d490c64efaa198251baf6c881bb5fe0891feb20069fed9ebee04b29",
	}

	return device.NewDevice(s.ctx, &d1)
}

func (s *VdInfo) setConfig(deviceTypeID, deviceID, deviceTypeCode string) {
	// 1. 先停止旧的 device
	if s.device != nil {
		s.device.Cancel()
		// 等待一小段时间让设备停止
		time.Sleep(100 * time.Millisecond)
	}

	// 2. 更新配置
	s.DeviceTypeID = deviceTypeID
	s.DeviceID = deviceID
	s.DeviceCode = deviceTypeCode
	// 3. 创建新的 device 和 context
	s.device = s.newDev(deviceTypeID, deviceID, deviceTypeCode)
	go s.device.Run()
}

// 运行Num个虚拟设备
func (s *VdInfo) runNumDevice(num int, deviceTypeID, token string) {
	nd := ndevice.NewNDevice(num, deviceTypeID, token, s.DefaultBroker)
	nd.Start(s.ctx)
}

func (s *VdInfo) setBroker(host string) {
	s.DefaultBroker = host

	// 1. 先停止旧的 device
	if s.device != nil {
		s.device.Cancel()
		// 等待一小段时间让设备停止
		time.Sleep(100 * time.Millisecond)
	}
	// 3. 创建新的 device 和 context
	s.device = s.newDev(s.DeviceTypeID, s.DeviceID, s.DeviceCode)
	go s.device.Run()

}

func (s *VdInfo) Name() string {
	return s.name
}

func (s *VdInfo) Run(ctx context.Context) (err error) {
	s.ctx = ctx

	//正常启动，则阻塞等待被关闭
	<-ctx.Done()
	xlog.Info("vdmqtt Stoped")

	return nil
}
