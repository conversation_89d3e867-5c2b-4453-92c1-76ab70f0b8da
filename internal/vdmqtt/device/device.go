package device

import (
	"context"
	"encoding/json"
	"os"
	"time"

	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xmqtt"
	"bs.com/app/pkg/xutils"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// mqtt broker

type MqttConfig struct {
	Host string `json:"host"`

	DeviceTypeID string `json:"device_type_id"`
	DeviceID     string `json:"device_id"`

	// mqtt 三元组
	ClientID string `json:"client_id"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type Device struct {
	MqttConfig

	ctx    context.Context
	Cancel context.CancelFunc

	client mqtt.Client // mqtt 客户端
	Qos    int         // qos

	// 设备属性
	temperature    float64 // 温度
	humidity       float64 // 湿度
	batteryLevel   float64 // 电量
	tempThreshold  float64 // 温度阈值
	humidThreshold float64 // 湿度阈值

	// 事件触发状态
	tempAlarmTriggered    bool // 温度报警是否已触发
	humidAlarmTriggered   bool // 湿度报警是否已触发
	batteryAlarmTriggered bool // 电池报警是否已触发
}

func NewDevice(ctx context.Context, config *MqttConfig) *Device {
	d := &Device{
		MqttConfig: *config,
		// 初始化设备属性
		temperature:    25.0,
		humidity:       50.0,
		batteryLevel:   100.0,
		tempThreshold:  25.0, // 默认温度阈值
		humidThreshold: 60.0, // 默认湿度阈值
	}

	d.client = xmqtt.NewClientSimple(d.Host, d.ClientID, d.Username, d.Password, 60)
	d.Qos = 0 // TCP 本身能保证可靠性
	d.ctx, d.Cancel = context.WithCancel(ctx)

	return d
}

func (d *Device) Run() {
	xlog.Info("mqtt device run.... ", "clientID", d.ClientID)

	defer func() {
		xlog.Debug("mqtt device stoped", "clientID", d.ClientID)
		d.Cancel()
	}()

	// 启动连接
	xlog.Info("starting mqtt connection...", "clientID", d.ClientID, "host", d.Host)
	token := d.client.Connect()

	// 等待连接完成，最多等待10秒
	if !token.WaitTimeout(10 * time.Second) {
		xlog.Error("mqtt connect timeout", "clientID", d.ClientID)
		return
	}

	if err := token.Error(); err != nil {
		xlog.Error("mqtt connect failed", "err", err, "clientID", d.ClientID)
		return
	}

	// 双重检查连接状态
	if !d.client.IsConnected() {
		xlog.Error("mqtt client not connected after connect call", "clientID", d.ClientID)
		return
	}

	defer func() {
		d.client.Disconnect(250)
	}()

	// 订阅
	d.initSubscribe()
	xlog.Info("mqtt client connected, starting main loop", "clientID", d.ClientID)

	// 数据上报定时器 - 每5秒上报一次属性
	tc := time.NewTicker(5 * time.Second)
	defer tc.Stop()

	for {
		select {
		case <-d.ctx.Done():
			return
		case <-tc.C:
			// 检查连接状态后再发布消息
			if !d.client.IsConnected() {
				xlog.Warn("mqtt client not connected, skipping publish", "clientID", d.ClientID)
				continue
			}

			// 模拟温度和湿度变化
			d.temperature = 25.0 + xutils.RandFloat64()*10.0 - 5.0 // 20-30度之间波动
			d.humidity = 50.0 + xutils.RandFloat64()*20.0 - 10.0   // 40-60%之间波动

			// 上报设备属性信息
			topic := bean.GetTopicAttriUp(d.DeviceTypeID, d.DeviceID)
			data := map[string]any{
				"temperature": d.temperature,
				"humidity":    d.humidity,
				"battery":     d.batteryLevel,
			}
			d.pubMessage(topic, data)

			// 检查是否需要触发事件
			d.checkAndTriggerEvents()
			// 先发一个事件
			d.triggerHumidityAlarm()
			// 模拟电池电量下降
			d.batteryLevel -= 1.0
		}
	}
}

// 连接成功之后，订阅消息
func (d *Device) initSubscribe() {
	xlog.Info("mqtt do subscribe", "clientID", d.ClientID)

	handler := d.defaultMessageHandler

	// 监听attri下发
	attriDownTopic := bean.GetTopicAttriDown(d.DeviceTypeID, d.DeviceID)
	xlog.Info("on property down", "topic", attriDownTopic)

	token := d.client.Subscribe(attriDownTopic, byte(d.Qos), handler)
	token.WaitTimeout(5 * time.Second)
	err := token.Error()
	if err != nil {
		xlog.Errorf("subscribe error:%s", err.Error())
		os.Exit(1)
	}

	// 监听event下发（eventReply）
	eventDownTopic := bean.GetTopicEventDown(d.DeviceTypeID, d.DeviceID)
	xlog.Info("on event down", "topic", eventDownTopic)

	token = d.client.Subscribe(eventDownTopic, byte(d.Qos), handler)
	token.WaitTimeout(5 * time.Second)
	err = token.Error()
	if err != nil {
		xlog.Errorf("subscribe error:%s", err.Error())
		os.Exit(1)
	}

	// 监听action下发（action call）
	actionDownTopic := bean.GetTopicActionDown(d.DeviceTypeID, d.DeviceID)
	xlog.Info("on action down", "topic", actionDownTopic)

	token = d.client.Subscribe(actionDownTopic, byte(d.Qos), handler)
	token.WaitTimeout(5 * time.Second)
	err = token.Error()
	if err != nil {
		xlog.Errorf("subscribe error:%s", err.Error())
		os.Exit(1)
	}
}

func (d *Device) defaultMessageHandler(client mqtt.Client, msg mqtt.Message) {
	t := msg.Topic()
	xlog.Info("on message", "topic", t, "payload", string(msg.Payload()))

	ti, err := bean.GetTopicInfo(t)
	if err != nil {
		xlog.Error("get topic info failed", "err", err)
		return
	}
	data := &bean.MqttMessage{}
	err = json.Unmarshal(msg.Payload(), &data)
	if err != nil {
		xlog.Error("unmarshal failed", "err", err)
		return
	}

	// 按照 type 分别处理
	switch ti.Type {
	case bean.MqttTypeAction:
		d.onActionHandler(data)
	case bean.MqttTypeEvent:
		d.onEventHandler(data)
	case bean.MqttTypeAttri:
		d.onAttributeHandler(data)
	default:
		xlog.Warn("unknown topic type", "topic", t)
	}
}

// 主动上报属性
func (d *Device) pubMessage(topic string, data map[string]any) error {
	msg := bean.MqttMessage{
		Version: bean.MqttStdV1,
		Method:  string(bean.AttriReport),
		MsgID:   xutils.UUIDSnowFlake(),
		TS:      time.Now().UnixMilli(),
		Data:    data,
	}
	payload, err := json.Marshal(msg)
	if err != nil {
		xlog.Errorf("marshal data to []byte error:%s", err.Error())
		return err
	}

	xlog.Info("mqtt publish", "topic", topic, "data", data, "ts", msg.TS)

	token := d.client.Publish(topic, byte(d.Qos), false, payload)
	token.WaitTimeout(5 * time.Second)
	return token.Error()
}

// 发布事件
func (d *Device) pubEvent(method string, data map[string]any) error {
	topic := bean.GetTopicEventUp(d.DeviceTypeID, d.DeviceID)

	msg := bean.MqttMessage{
		Version: bean.MqttStdV1,
		Method:  method,
		MsgID:   xutils.UUIDSnowFlake(),
		TS:      time.Now().UnixMilli(),
		Data:    data,
	}
	payload, err := json.Marshal(msg)
	if err != nil {
		xlog.Errorf("marshal data to []byte error:%s", err.Error())
		return err
	}

	xlog.Info("mqtt publish", "topic", topic, "data", data, "ts", msg.TS)

	token := d.client.Publish(topic, byte(d.Qos), false, payload)
	token.WaitTimeout(5 * time.Second)
	return token.Error()
}

// 响应消息
func (d *Device) replyMessage(method, msgID string, code int, data map[string]any) error {
	topic := bean.GetTopicEventUp(d.DeviceTypeID, d.DeviceID)

	msg := bean.MqttMessage{
		Version: bean.MqttStdV1,
		Method:  method,
		MsgID:   msgID,
		TS:      time.Now().UnixMilli(),
		Data:    data,
		Code:    code,
	}
	payload, err := json.Marshal(msg)
	if err != nil {
		xlog.Errorf("marshal data to []byte error:%s", err.Error())
		return err
	}

	xlog.Info("mqtt publish", "topic", topic, "data", data, "ts", msg.TS)

	token := d.client.Publish(topic, byte(d.Qos), false, payload)
	token.WaitTimeout(5 * time.Second)
	return token.Error()
}
