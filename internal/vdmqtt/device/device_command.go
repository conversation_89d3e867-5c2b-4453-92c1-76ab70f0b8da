package device

import (
	"fmt"
	"time"

	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
)

// 响应某个 action
func (d *Device) onActionHandler(msgDown *bean.MqttMessage) {
	// 处理不同的命令
	method := msgDown.Method
	params := msgDown.Data
	replyCode := 200

	responseData := map[string]any{}
	switch method {
	case "set_temp_threshold":
		// 设置温度阈值
		if threshold, ok := params["value"].(float64); ok {
			d.tempThreshold = threshold
			responseData["result"] = "success"
			responseData["message"] = fmt.Sprintf("Temperature threshold set to %.1f", threshold)
			xlog.Info("Temperature threshold set", "value", threshold)
		} else {
			responseData["result"] = "failed"
			responseData["message"] = "Invalid threshold value"
		}

	case "set_humid_threshold":
		// 设置湿度阈值
		if threshold, ok := params["value"].(float64); ok {
			d.humidThreshold = threshold
			responseData["result"] = "success"
			responseData["message"] = fmt.Sprintf("Humidity threshold set to %.1f", threshold)
			xlog.Info("Humidity threshold set", "value", threshold)
		} else {
			responseData["result"] = "failed"
			responseData["message"] = "Invalid threshold value"
		}

	case "reboot":
		// 模拟设备重启，没有参数
		xlog.Info("Device rebooting...")
		responseData["result"] = "success"
		responseData["message"] = "Device is rebooting"

		// 模拟重启过程
		go func() {
			// 先回复命令已接收
			d.replyMessage(method, msgDown.MsgID, 200, responseData)

			// 等待3秒模拟重启过程
			time.Sleep(3 * time.Second)

			// 重置设备状态
			d.temperature = 25.0
			d.humidity = 50.0
			d.batteryLevel = 100.0
			d.tempAlarmTriggered = false
			d.humidAlarmTriggered = false
			d.batteryAlarmTriggered = false

			// 发送重启完成事件
			eventData := map[string]any{
				"result": "success",
			}
			d.pubEvent("reboot", eventData)
		}()

		// 由于已经在goroutine中回复，这里直接返回
		return

	default:
		responseData["result"] = "failed"
		responseData["message"] = fmt.Sprintf("Unknown function: %s", method)
		replyCode = 4001 // invalid method
	}

	// 回复命令执行结果
	d.replyMessage(method, msgDown.MsgID, replyCode, responseData)
}
