package device

import (
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
)

// 处理属性下发消息
func (d *Device) onAttributeHandler(msg *bean.MqttMessage) {
	xlog.Info("Handling attribute message", "method", msg.Method, "data", msg.Data)
	// 根据不同的方法处理
	switch msg.Method {
	case bean.AttriSet: // 设置属性
		responseData := map[string]any{
			"result": "success",
		}
		_ = d.replyMessage(msg.Method, msg.MsgID, 200, responseData)

	case bean.AttriGet: // 获取属性
		// 响应当前的所有属性值
		responseData := map[string]any{
			"temperature":     d.temperature,
			"humidity":        d.humidity,
			"battery":         d.batteryLevel,
			"temp_threshold":  d.tempThreshold,
			"humid_threshold": d.humidThreshold,
		}

		_ = d.replyMessage(msg.Method, msg.MsgID, 200, responseData)

	default:
		xlog.Warn("Unknown attribute method", "method", msg.Method)
	}
}
