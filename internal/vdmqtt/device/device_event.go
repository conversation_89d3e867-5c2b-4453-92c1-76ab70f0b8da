package device

import (
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
)

// 处理事件下发消息
func (d *Device) onEventHandler(msg *bean.MqttMessage) {
	xlog.Info("Handling event message", "method", msg.Method, "data", msg.Data)

	// 事件下发通常是对设备上报事件的响应确认
	switch msg.Method {
	case "temp_alarm":
		// 收到温度报警的响应，可以重置报警状态
		d.tempAlarmTriggered = false
		xlog.Info("Temperature alarm acknowledged")

	case "humid_alarm":
		// 收到湿度报警的响应，可以重置报警状态
		d.humidAlarmTriggered = false
		xlog.Info("Humidity alarm acknowledged")

	case "battery_low":
		// 收到电池低电量报警的响应
		// 注意：电池电量低的状态不会因为收到响应而重置，只有电量恢复才会重置
		xlog.Info("Battery low alarm acknowledged")

	default:
		xlog.Warn("Unknown event in reply", "event", msg.Method)
	}
}

// 检查并触发事件
func (d *Device) checkAndTriggerEvents() {
	// 检查温度是否超过阈值
	if d.temperature > d.tempThreshold && !d.tempAlarmTriggered {
		d.triggerTemperatureAlarm()
	} else if d.temperature <= d.tempThreshold && d.tempAlarmTriggered {
		// 温度恢复正常，重置报警状态
		d.tempAlarmTriggered = false
	}

	// 检查湿度是否超过阈值
	if d.humidity > d.humidThreshold && !d.humidAlarmTriggered {
		d.triggerHumidityAlarm()
	} else if d.humidity <= d.humidThreshold && d.humidAlarmTriggered {
		// 湿度恢复正常，重置报警状态
		d.humidAlarmTriggered = false
	}

	// 检查电池电量
	if d.batteryLevel <= 20 && !d.batteryAlarmTriggered {
		d.triggerBatteryLowEvent()
	} else if d.batteryLevel > 20 && d.batteryAlarmTriggered {
		// 电池恢复正常，重置报警状态
		d.batteryAlarmTriggered = false
	}
}

// 触发温度报警事件
func (d *Device) triggerTemperatureAlarm() {
	xlog.Info("Temperature exceeds threshold, triggering alarm", "temperature", d.temperature, "threshold", d.tempThreshold)

	// 标记报警已触发
	d.tempAlarmTriggered = true

	// 发送温度报警事件
	params := map[string]any{
		"temperature": d.temperature,
		"threshold":   d.tempThreshold,
	}
	eventData := map[string]any{
		"temperatureAlarm": params, // 这里应该为{ identifier: { "key": "value","key": "value"}}
	}
	d.pubEvent("temp_alarm", eventData)
}

// 触发湿度报警事件
func (d *Device) triggerHumidityAlarm() {
	xlog.Info("Humidity exceeds threshold, triggering alarm", "humidity", d.humidity, "threshold", d.humidThreshold)

	// 标记报警已触发
	d.humidAlarmTriggered = true

	// 发送湿度报警事件
	params := map[string]any{
		"humidity":  d.humidity,
		"threshold": d.humidThreshold,
	}
	eventData := map[string]any{
		"humidityAlarm": params, // 这里应该为{ identifier: { "key": "value","key": "value"}}
	}
	d.pubEvent("humid_alarm", eventData)
}

// 触发电池电量低事件
func (d *Device) triggerBatteryLowEvent() {
	xlog.Info("Battery level low, triggering alarm", "battery", d.batteryLevel)

	// 标记报警已触发
	d.batteryAlarmTriggered = true

	// 发送电池电量低事件
	eventData := map[string]any{
		"battery": d.batteryLevel,
	}
	d.pubEvent("battery_low", eventData)
}
