## influxdb 核心概念

- Bucket: 数据库的容器，类似于 SQL 中的 Database。数据有保留策略（Retention Policy），例如只保留最近30天的数据。
- Measurement: 测量值的逻辑分组，类似于 SQL 中的 Table。你应该按数据的物理意义来命名，例如 environment_sensors, energy_meter。
- Tags (标签): 用于索引和分组的元数据。把所有你希望用来 WHERE 和 GROUP BY 的字段都作为 Tag。Tags 的值只能是字符串。
- Fields (字段): 实际的测量数值。这些是你想要进行数学计算（如 mean, sum, max）的数据。Fields 可以是浮点数、整数、布尔值、字符串。
- Timestamp: 每条数据的时间戳，是时序数据的灵魂。InfluxDB 支持纳秒级精度。


- measurement 相当于表名，比如 "device_data"
- tags 索引字段（用于过滤），如设备ID、设备类型
- fields 实际数据值（数值/字符串等），如温度、湿度
- timestamp 时间戳，默认精度是纳秒，可设置秒、毫秒等


```
InfluxDB Schema 设计中最重要的一条规则：

用作查询、筛选、分组、聚合条件的，必须是 Tag。因为 InfluxDB 会为 Tags 建立索引，所以基于 Tag 的查询非常快.
需要进行数学运算的，必须是 Field。

示例: 设备ID (device_id), 地理位置 (location), 设备型号 (model), IP地址 (ip_address)。
示例: 温度 (temperature), 湿度 (humidity), 功率 (power), 累计电量 (kwh_total)。
```

## 时序数据的存储


## 时序数据的读取

分为累计量、瞬时量。查询的时候可以提取特征值、可以重采样做均值（瞬时量），重采样做累计（累计量）等。

## 常用查询语句

```json
every
(Required) Duration of time between windows.

period
Duration of windows. Default is the every value.

period can be negative, indicating the start and stop boundaries are reversed.

offset
Duration to shift the window boundaries by. Default is 0s.

offset can be negative, indicating that the offset goes backwards in time.

fn
(Required) Aggregate or selector function to apply to each time window.

location
Location used to determine timezone. Default is the location option.

column
Column to operate on.

timeSrc
Column to use as the source of the new time value for aggregate values. Default is _stop.

timeDst
Column to store time values for aggregate values in. Default is _time.

createEmpty
Create empty tables for empty window. Default is true.

Note: When using createEmpty: true, aggregate functions return empty tables, but selector functions do not. By design, selectors drop empty tables.

tables
Input data. Default is piped-forward data (<-).
```

### 查询过去一天平均温度

```flux
from(bucket: "your-bucket")
  |> range(start: -1d)
  |> filter(fn: (r) => r._measurement == "environment" and r._field == "temperature")
  |> aggregateWindow(every: 10m, fn: mean, createEmpty: false)
  |> yield(name: "10min_average_temperature")
```