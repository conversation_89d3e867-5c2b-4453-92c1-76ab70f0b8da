package tsdb

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/bean"
)

// 注意：tags 里面需要 device_id 和 model_type
type ITsDBRepo interface {
	// identifier 返回值的 key 值，一般为某个 tag
	QueryData(measure, identifier string, tags map[string]string, tsStart, tsEnd int64, fieldKeys []string) ([]map[string]any, error)
	QueryDataPage(measure, identifier string, tags map[string]string, tsStart, tsEnd int64, fieldKeys []string, pgInfo *dto.PageInfo) ([]map[string]any, error)

	QueryDataStats(measure string, tags map[string]string, tsStart, tsEnd int64) (map[string]*bean.FieldStats, error)
	QueryMeasureStatWithOp(measure string, start, end int64, tags map[string]string, fields []string, operation string) (map[string]*bean.FieldStats, error)
	InsertData(measure string, tags map[string]string, fields map[string]any, ts int64) error
	DeleteData(measure string, tags map[string]string, tsStart, tsEnd int64) error

	// 写入物模型
	InsertDataAttr(deviceID, identifier string, fields map[string]any, ts int64) error
	InsertDataEvent(deviceID, identifier string, fields map[string]any, ts int64) error
	InsertDataCommand(deviceID, identifier string, fields map[string]any, ts int64) error

	// 查询物模型
	QueryDataAttr(deviceID, identifier string, tsStart, tsEnd int64, fieldKeys []string, pgInfo *dto.PageInfo) ([]map[string]any, error)
	QueryDataEvent(deviceID, identifier string, tsStart, tsEnd int64, fieldKeys []string, pgInfo *dto.PageInfo) ([]map[string]any, error)
	QueryDataCommand(deviceID, identifier string, tsStart, tsEnd int64, fieldKeys []string, pgInfo *dto.PageInfo) ([]map[string]any, error)
}
