package tsdb

import (
	"context"
	"fmt"
	"strings"
	"time"

	"bs.com/app/config"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	influxdb2Api "github.com/influxdata/influxdb-client-go/v2/api"
	influxdb2Http "github.com/influxdata/influxdb-client-go/v2/api/http"
	"github.com/influxdata/influxdb-client-go/v2/api/write"
)

type InfluxdbRepo struct {
	Org    string `yaml:"org"          json:"org"`
	Bucket string `yaml:"bucket"       json:"bucket"`

	client      influxdb2.Client
	writeApi    influxdb2Api.WriteAPI
	deleteApi   influxdb2Api.DeleteAPI
	blkWriteApi influxdb2Api.WriteAPIBlocking
}

const (
	flushInterval   uint = 20  // second
	retryInterval   uint = 3   // second
	batchSize       uint = 100 // 批量写入的 size
	retryQueueLimit uint = 1000
)

var _ ITsDBRepo = &InfluxdbRepo{} // 时序数据操作接口

func NewInfluxdbRepo() *InfluxdbRepo {

	cfg := config.Get().Influxdb2
	dao := &InfluxdbRepo{
		Org:    cfg.Org,
		Bucket: cfg.Bucket,
	}

	hostUrl := fmt.Sprintf("http://%s", cfg.Address)

	opts := influxdb2.DefaultOptions().SetUseGZip(true)
	opts = opts.SetPrecision(time.Second)

	opts = opts.SetFlushInterval(flushInterval * 1000)
	opts = opts.SetRetryInterval(retryInterval * 1000)
	opts = opts.SetBatchSize(batchSize)
	opts = opts.SetRetryBufferLimit(retryQueueLimit * batchSize)
	dao.client = influxdb2.NewClientWithOptions(hostUrl, cfg.Token, opts)

	// create asynchronous, auto-retry write api instance
	dao.writeApi = dao.client.WriteAPI(dao.Org, dao.Bucket)
	dao.writeApi.SetWriteFailedCallback(func(batch string, error influxdb2Http.Error, retryAttempts uint) bool {
		xlog.Errorf("influx write failed: %s, retry: %d", error.Error(), retryAttempts)
		return retryAttempts < 100
	})

	dao.deleteApi = dao.client.DeleteAPI()

	// create synchronous api to write the backlog
	dao.blkWriteApi = dao.client.WriteAPIBlocking(dao.Org, dao.Bucket)

	if dao.Ping() == nil {
		xlog.Debug("influxdb ping success")
	}

	return dao
}

// 添加时区，参考： https://www.influxdata.com/blog/time-zones-in-flux/
// 也可以单独在某个窗口函数里面设置 location:   |> aggregateWindow(every: 1mo, fn: sum, createEmpty: true,timeSrc:"_stop",location: timezone.location(name:"Asia/Shanghai"))
// import "timezone"
// option location = timezone.fixed(offset: -8h)
// fname = caller function name
func (d *InfluxdbRepo) doQuery(measure, query string) (*influxdb2Api.QueryTableResult, error) {
	start := time.Now()
	defer func() {
		elapse := time.Until(start)
		if elapse > time.Second {
			xlog.Warnf("slow influx query. query :%s , elapse: %s", query, elapse.String())
		}
	}()

	xlog.Debug(measure + ": flux query ===== " + xutils.CleanString(query))

	queryAPI := d.client.QueryAPI(d.Org)
	// 本地时区
	tz := `import "timezone" option location = timezone.location(name:"Asia/Shanghai")`

	return queryAPI.Query(context.Background(), tz+" "+query)
}

// check health
func (d *InfluxdbRepo) Ping() error {
	ok, err := d.client.Ping(context.Background())
	if !ok {
		xlog.Info("influx ping failed:", err)
	}
	return err
}

// 注意，fields 字段首次类型不可变
// InfluxDB 会在 首次写入某个 measurement.field 组合时记录它的类型，之后不允许更改该字段类型（会忽略或写入失败）。
// 时间用 ms
func (d *InfluxdbRepo) InsertData(measure string, tags map[string]string, fields map[string]any, ts int64) error {
	timestamp := time.UnixMilli(ts)
	p := write.NewPoint(
		measure,
		tags,
		fields,
		timestamp,
	)

	// 插入数据
	d.writeApi.WritePoint(p)
	return nil
}

func (d *InfluxdbRepo) DeleteData(measure string, tags map[string]string, tsStart, tsEnd int64) error {
	// 设置删除时间范围 (删除所有数据)
	start := time.UnixMilli(tsStart)
	stop := time.UnixMilli(tsEnd)
	org, bucket := d.Org, d.Bucket

	// 设置删除的条件（predicate）
	var predicate string

	// 添加 measurement
	predicate = fmt.Sprintf(`_measurement="%s"`, measure)
	// 添加 tags
	for k, v := range tags {
		predicate += fmt.Sprintf(` and %s="%s"`, k, v)
	}

	// 执行删除
	return d.deleteApi.DeleteWithName(context.Background(), org, bucket, start, stop, predicate)
}

// 查询数据，通用查询语句，返回 []map[string]any
// start 和 end 单位 (Unix timestamp in milliseconds).
func (d *InfluxdbRepo) QueryData(measure, identifier string, tags map[string]string, tsStart, tsEnd int64, fieldKeys []string) ([]map[string]any, error) {
	var tagFilters []string
	for k, v := range tags {
		// 注意 Flux 中字符串要加引号
		tagFilters = append(tagFilters, fmt.Sprintf(`r.%s == "%s"`, k, v))
	}
	tagFilterClause := strings.Join(tagFilters, " and ")

	// 生成 drop 列表
	var dropCols []string
	for k := range tags {
		dropCols = append(dropCols, fmt.Sprintf(`"%s"`, k))
	}
	// 添加系统字段
	dropCols = append(dropCols,
		`"_start"`, `"_stop"`, `"_measurement"`, `"result"`, `"table"`)

	dropColsStr := strings.Join(dropCols, ", ")
	var query string
	if len(fieldKeys) > 0 {
		// 查询指定 filed，不需生成 drop 列表
		// 构建字段过滤条件
		var fieldFilters []string
		for _, field := range fieldKeys {
			fieldFilters = append(fieldFilters, fmt.Sprintf(`r["_field"] == "%s"`, field))
		}
		fieldFilterClause := strings.Join(fieldFilters, " or ")

		// 构建查询语句，只查询指定的字段
		temp := `from(bucket:"%s")
		|> range(start: %d,stop: %d)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => %s)
		|> filter(fn: (r) => %s)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
		|> drop(columns: [%s])`
		query = fmt.Sprintf(temp, d.Bucket, tsStart/1000, tsEnd/1000, measure, tagFilterClause, fieldFilterClause, dropColsStr)
	} else {
		temp := `from(bucket:"%s")
		|> range(start: %d,stop: %d)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => %s)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
		|> drop(columns: [%s])`
		query = fmt.Sprintf(temp, d.Bucket, tsStart/1000, tsEnd/1000, measure, tagFilterClause, dropColsStr)
	}
	result, err := d.doQuery("Query Data", query)
	if err != nil {
		xlog.Error("tsdb query faild", "err", err, "query", query)
		return nil, err
	}

	var dataArray []map[string]any
	for result.Next() {
		dataMap := make(map[string]any)
		filedMap := make(map[string]any) // 每个 measurement 的字段值

		record := result.Record()
		dataMap["ts"] = record.Time().Unix()
		recordsMap := record.Values()

		for key, value := range recordsMap {
			if key == "_time" || key == "table" || key == "result" {
				continue
			}
			filedMap[key] = value
		}
		if len(fieldKeys) > 0 {
			continue
		}
		if len(filedMap) > 1 {
			dataMap[identifier] = filedMap
		} else {
			dataMap[identifier] = filedMap[identifier] //非 object 类型
		}

		dataArray = append(dataArray, dataMap)
	}

	return dataArray, nil
}

// 查询数据，支持分页查询，返回 []map[string]any
// start 和 end 单位 (Unix timestamp in milliseconds).
// page 从1开始，pageSize 每页大小
func (d *InfluxdbRepo) QueryDataPage(measure, identifier string, tags map[string]string, tsStart, tsEnd int64, fieldKeys []string, pgInfo *dto.PageInfo) ([]map[string]any, error) {
	var tagFilters []string
	for k, v := range tags {
		// 注意 Flux 中字符串要加引号
		tagFilters = append(tagFilters, fmt.Sprintf(`r.%s == "%s"`, k, v))
	}
	tagFilterClause := strings.Join(tagFilters, " and ")

	// 生成 drop 列表
	var dropCols []string
	for k := range tags {
		dropCols = append(dropCols, fmt.Sprintf(`"%s"`, k))
	}
	// 添加系统字段
	dropCols = append(dropCols,
		`"_start"`, `"_stop"`, `"_measurement"`, `"result"`, `"table"`)

	dropColsStr := strings.Join(dropCols, ", ")

	// 计算分页参数
	offset := (pgInfo.Page - 1) * pgInfo.PageSize
	limit := pgInfo.PageSize

	var query string
	if len(fieldKeys) > 0 {
		// 查询指定 filed，不需生成 drop 列表
		// 构建字段过滤条件
		var fieldFilters []string
		for _, field := range fieldKeys {
			fieldFilters = append(fieldFilters, fmt.Sprintf(`r["_field"] == "%s"`, field))
		}
		fieldFilterClause := strings.Join(fieldFilters, " or ")

		// 构建查询语句，只查询指定的字段
		temp := `from(bucket:"%s")
		|> range(start: %d,stop: %d)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => %s)
		|> filter(fn: (r) => %s)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
		|> drop(columns: [%s])
		|> limit(n: %d, offset: %d)`
		query = fmt.Sprintf(temp, d.Bucket, tsStart/1000, tsEnd/1000, measure, tagFilterClause, fieldFilterClause, dropColsStr, limit, offset)
	} else {
		temp := `from(bucket:"%s")
		|> range(start: %d,stop: %d)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => %s)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
		|> drop(columns: [%s])
		|> limit(n: %d, offset: %d)`
		query = fmt.Sprintf(temp, d.Bucket, tsStart/1000, tsEnd/1000, measure, tagFilterClause, dropColsStr, limit, offset)
	}

	result, err := d.doQuery("Query Data Page", query)
	if err != nil {
		xlog.Error("tsdb query faild", "err", err, "query", query)
		return nil, err
	}

	var dataArray []map[string]any
	for result.Next() {
		dataMap := make(map[string]any)
		filedMap := make(map[string]any) // 每个 measurement 的字段值

		record := result.Record()
		dataMap["ts"] = record.Time().Unix()
		recordsMap := record.Values()

		for key, value := range recordsMap {
			if key == "_time" || key == "table" || key == "result" {
				continue
			}
			filedMap[key] = value
		}
		if len(fieldKeys) > 0 {
			continue
		}

		if len(filedMap) > 1 {
			if identifier == "" && filedMap["identifier"] != nil {
				identifier = filedMap["identifier"].(string)
			}
			dataMap[identifier] = filedMap
		} else {
			if identifier == "" && filedMap["identifier"] != nil {
				// 非 object 类型
				identifier = filedMap["identifier"].(string)
			}
			dataMap[identifier] = filedMap[identifier] //非 object 类型
		}
		dataArray = append(dataArray, dataMap)
	}
	// xlog.Debug("----------------- tsdb query page", "dataArray", dataArray)
	// 获取总记录数
	total, err := d.CountData(measure, tags, tsStart, tsEnd)
	if err != nil {
		xlog.Error("tsdb count query faild", "err", err)
	} else {
		pgInfo.Total = total
	}

	return dataArray, nil
}

// 获取满足条件的总记录数
func (d *InfluxdbRepo) CountData(measure string, tags map[string]string, tsStart, tsEnd int64) (int64, error) {

	var tagFilters []string
	for k, v := range tags {
		tagFilters = append(tagFilters, fmt.Sprintf(`r.%s == "%s"`, k, v))
	}
	tagFilterClause := strings.Join(tagFilters, " and ")

	temp := `from(bucket:"%s")
		|> range(start: %d,stop: %d)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => %s)
		|> count()`
	query := fmt.Sprintf(temp, d.Bucket, tsStart/1000, tsEnd/1000, measure, tagFilterClause)

	result, err := d.doQuery("Count Data", query)
	if err != nil {
		xlog.Error("tsdb count query faild", "err", err, "query", query)
		return 0, err
	}

	if result.Next() {
		return result.Record().Value().(int64), nil
	}

	return 0, nil
}

// 查询数据，并使用聚合函数和聚合方法
/*
from(bucket: "new_ebox")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r.ssn == "0075602395")
  |> filter(fn: (r) => r["_measurement"] == "vwp_osm_water_P19")
  |> aggregateWindow(every: 1h, fn: mean, createEmpty: false)
  |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
  |> drop(columns: ["_start", "_stop", "_measurement", "result", "table", "ssn"])
  |> yield(name: "mean")
*/

// 查询数据，通用查询语句，返回 []map[string]any
// start 和 end 单位 (Unix timestamp in milliseconds).
// every: 聚合时间间隔，可取值：1m, 1h, 1d, 1w, 1mo, 1y
// fn: 聚合函数，可取值：mean, sum, min, max, count, stddev, varp, first, last
func (d *InfluxdbRepo) QueryDataAggregate(measure string, tags map[string]string, tsStart, tsEnd int64, every time.Duration, fn string) ([]map[string]any, error) {
	var tagFilters []string
	for k, v := range tags {
		// 注意 Flux 中字符串要加引号
		tagFilters = append(tagFilters, fmt.Sprintf(`r.%s == "%s"`, k, v))
	}
	tagFilterClause := strings.Join(tagFilters, " and ")

	// 生成 drop 列表，包含所有 tag key
	var dropCols []string
	for k := range tags {
		dropCols = append(dropCols, fmt.Sprintf(`"%s"`, k))
	}
	// 添加系统字段
	dropCols = append(dropCols,
		`"_start"`, `"_stop"`, `"_measurement"`, `"result"`, `"table"`)

	fluxQuery := `from(bucket: "%s")
		|> range(start: %d, stop: %d)
		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => %s)
		|> aggregateWindow(every: %s, fn: %s, createEmpty: false)
		|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
		|> drop(columns: [%s])
		|> yield(name: "%s")`
	query := fmt.Sprintf(fluxQuery, d.Bucket, tsStart/1000, tsEnd/1000, measure, tagFilterClause, every, fn, strings.Join(dropCols, ", "), fn)

	result, err := d.doQuery("Query Data Aggregate", query)
	if err != nil {
		xlog.Error("tsdb query faild", "err", err, "query", query)
		return nil, err
	}

	var dataArray []map[string]any
	for result.Next() {
		dataMap := make(map[string]any)
		record := result.Record()
		dataMap["ts"] = record.Time().Unix()
		recordsMap := record.Values()

		for key, value := range recordsMap {
			if key == "_time" || key == "table" {
				continue
			}
			dataMap[key] = value
		}
		dataArray = append(dataArray, dataMap)
	}

	return dataArray, nil
}

// 查询特征值
// 不需要返回时序数据，仅做特征值提取
// 只适用于瞬时量，累计量使用 QueryDataAggregate sum 即可
// tsStart 和 tsEnd 单位 (Unix timestamp in milliseconds).
func (d *InfluxdbRepo) QueryDataStats(measure string, tags map[string]string, tsStart, tsEnd int64) (map[string]*bean.FieldStats, error) {
	bucket := d.Bucket

	var tagFilters []string
	for k, v := range tags {
		// 注意 Flux 中字符串要加引号
		tagFilters = append(tagFilters, fmt.Sprintf(`r.%s == "%s"`, k, v))
	}
	tagFilterClause := strings.Join(tagFilters, " and ")

	var fluxQuery string

	queryStr := `
            data = from(bucket:"%s")
                |> range(start: %d, stop: %d)
                |> filter(fn: (r) => r._measurement == "%s")
                |> filter(fn: (r) => %s)

            max_data = data
                |> max()
                |> keep(columns: ["_field", "_value", "_time"])
                |> yield(name: "max")

            min_data = data
                |> min()
                |> keep(columns: ["_field", "_value", "_time"])
                |> yield(name: "min")

            mean_data = data
                |> mean()
                |> keep(columns: ["_field", "_value"])
                |> yield(name: "mean")

            // 计算方差：使用标准差的平方
            variance_data = data
                |> stddev()
                |> map(fn: (r) => ({ r with _value: r._value * r._value }))
                |> keep(columns: ["_field", "_value"])
                |> yield(name: "variance")

			// 均方根值（RMS, Root Mean Square）
            rms_data = data
                |> map(fn: (r) => ({ r with _value: r._value * r._value }))
                |> mean()
                |> map(fn: (r) => ({ r with _value: float(v: float(v: r._value) ^ 0.5) }))
                |> keep(columns: ["_field", "_value"])
                |> yield(name: "rms")

            count_data = data
                |> count()
                |> keep(columns: ["_field", "_value"])
                |> yield(name: "count")`

	queryStr22 := `
			import "math"
			
			data = from(bucket:"%s")
				|> range(start: %d, stop: %d)
				|> filter(fn: (r) => r._measurement == "%s")
				|> filter(fn: (r) => %s)
	
			max_data = data
				|> max()
				|> keep(columns: ["_field", "_value", "_time"])
				|> yield(name: "max")
	
			min_data = data
				|> min()
				|> keep(columns: ["_field", "_value", "_time"])
				|> yield(name: "min")
	
			mean_data = data
				|> mean()
				|> keep(columns: ["_field", "_value"])
				|> yield(name: "mean")
	
			// 计算方差：使用标准差的平方
			variance_data = data
				|> stddev()
				|> map(fn: (r) => ({ r with _value: r._value * r._value }))
				|> keep(columns: ["_field", "_value"])
				|> yield(name: "variance")
	
			// 均方根值（RMS, Root Mean Square）
			rms_data = data
				|> map(fn: (r) => ({ r with _value: r._value * r._value }))
				|> mean()
				|> map(fn: (r) => ({ r with _value: math.sqrt(x: r._value )}))
				|> keep(columns: ["_field", "_value"])
				|> yield(name: "rms")
	
			count_data = data
				|> count()
				|> keep(columns: ["_field", "_value"])
				|> yield(name: "count")`

	_ = queryStr22 // 使用了 import math。但是报错 invalid statement: import

	fluxQuery = fmt.Sprintf(queryStr, bucket, tsStart/1000, tsEnd/1000, measure, tagFilterClause)

	result, err := d.doQuery("Query Stats", fluxQuery)
	if err != nil {
		xlog.Error("query error: ", err)
		return nil, err
	}

	// 用于存储每个字段的统计信息
	statsMap := make(map[string]*bean.FieldStats)

	// 处理查询结果
	for result.Next() {
		record := result.Record()
		field := record.ValueByKey("_field").(string)
		value := record.ValueByKey("_value")

		// 确保该字段的统计结构存在
		if _, exists := statsMap[field]; !exists {
			statsMap[field] = &bean.FieldStats{}
		}

		// 根据结果类型更新相应的统计值
		var ok bool
		switch record.Result() {
		case "max":
			statsMap[field].Max, ok = value.(float64)
			statsMap[field].MaxTime = record.Time().Unix()
		case "min":
			statsMap[field].Min, ok = value.(float64)
			statsMap[field].MinTime = record.Time().Unix()
		case "mean":
			statsMap[field].Mean, ok = value.(float64)
		case "variance":
			statsMap[field].Variance, ok = value.(float64)
		case "rms":
			statsMap[field].Rms, ok = value.(float64)
		case "count":
			statsMap[field].Count, ok = value.(int64)
		}

		if !ok {
			xlog.Error("station query stats: type convert failed", "field", field)
			return nil, fmt.Errorf("type convert failed")
		}
	}

	// 格式化所有统计值
	for _, fieldStats := range statsMap {
		fieldStats.Max = xutils.FloatFormat(fieldStats.Max)
		fieldStats.Min = xutils.FloatFormat(fieldStats.Min)
		fieldStats.Mean = xutils.FloatFormat(fieldStats.Mean)
		fieldStats.Variance = xutils.FloatFormat(fieldStats.Variance)
		fieldStats.Rms = xutils.FloatFormat(fieldStats.Rms)
	}

	return statsMap, nil
}

// 对某个 measure 的某些监测项，查询某个特征值
// performs a specified statistical operation on the data.
// tsStart 和 tsEnd 单位 (Unix timestamp in milliseconds).
func (d *InfluxdbRepo) QueryMeasureStatWithOp(measure string, start, end int64, tags map[string]string, fields []string, operation string) (map[string]*bean.FieldStats, error) {
	bucket := d.Bucket

	var tagFilters []string
	for k, v := range tags {
		// 注意 Flux 中字符串要加引号
		tagFilters = append(tagFilters, fmt.Sprintf(`r.%s == "%s"`, k, v))
	}
	tagFilterClause := strings.Join(tagFilters, " and ")

	var fluxQuery string
	var operationFunc string
	switch operation {
	case "min", "max", "mean", "sum":
		operationFunc = fmt.Sprintf("%s()", operation)
	case "variance":
		operationFunc = `stddev() |> map(fn: (r) => ({ r with _value: r._value * r._value }))`
	case "rms":
		operationFunc = `map(fn: (r) => ({ r with _value: r._value * r._value }))
        |> mean()
        |> map(fn: (r) => ({ r with _value: float(v: float(v: r._value) ^ 0.5) }))`
	default:
		return nil, fmt.Errorf("unsupported operation: %s", operation)
	}

	fluxQuery = fmt.Sprintf(`from(bucket:"%s")
        |> range(start: %d, stop: %d)
        |> filter(fn: (r) => r._measurement == "%s")
        |> filter(fn: (r) => %s)
        |> %s
        |> keep(columns: ["_field", "_value", "_time"])
        |> yield(name: "%s")`, bucket, start/1000, end/1000, measure, tagFilterClause, operationFunc, operation)

	result, err := d.doQuery("Query Station Stat With Operation", fluxQuery)
	if err != nil {
		xlog.Error("query error: ", err)
		return nil, err
	}

	// Process the results
	statsMap := make(map[string]*bean.FieldStats)
	for result.Next() {
		record := result.Record()
		field := record.ValueByKey("_field").(string)
		value := record.ValueByKey("_value")

		if _, exists := statsMap[field]; !exists {
			statsMap[field] = &bean.FieldStats{}
		}

		switch operation {
		case "max":
			statsMap[field].Max = xutils.FloatFormat(value.(float64))
			statsMap[field].MaxTime = record.Time().Unix()
		case "min":
			statsMap[field].Min = xutils.FloatFormat(value.(float64))
			statsMap[field].MinTime = record.Time().Unix()
		case "mean":
			statsMap[field].Mean = xutils.FloatFormat(value.(float64))
		case "variance":
			statsMap[field].Variance = xutils.FloatFormat(value.(float64))
		case "rms":
			statsMap[field].Rms = xutils.FloatFormat(value.(float64))
		case "count":
			statsMap[field].Count = value.(int64)
		}
	}

	return statsMap, nil
}

// 查询某个 measure，某个时间范围内,最后一条数据
// tsStart 和 tsEnd 单位 (Unix timestamp in milliseconds).
func (d *InfluxdbRepo) QueryDataLastOne(measure string, start, end int64, tags map[string]string) (map[string]any, error) {
	var tagFilters []string
	for k, v := range tags {
		// 注意 Flux 中字符串要加引号
		tagFilters = append(tagFilters, fmt.Sprintf(`r.%s == "%s"`, k, v))
	}
	tagFilterClause := strings.Join(tagFilters, " and ")

	tmp := `from(bucket:"%s")
		|> range(start: %d, stop: %d) 
  		|> filter(fn: (r) => r["_measurement"] == "%s")
		|> filter(fn: (r) => %s)
		|> group(columns: ["_measurement"])
		|> last()`
	query := fmt.Sprintf(tmp, d.Bucket, start/1000, end/1000, measure, tagFilterClause)
	result, err := d.doQuery("QueryStationLastOne", query)
	if err != nil {
		xlog.Error("query error", "err", err, "query", query)
		return nil, err
	}

	data := make(map[string]any)
	fieldMap := make(map[string]any)
	var timestamp int64

	for result.Next() {
		record := result.Record()
		timestamp = record.Time().Unix()

		// 获取字段名和值
		field := record.ValueByKey("_field").(string)
		value := record.ValueByKey("_value")

		// 将字段值添加到 fieldMap
		fieldMap[field] = value
	}

	// 设置时间戳和字段值
	data["ts"] = timestamp
	data[measure] = fieldMap
	return data, nil
}
