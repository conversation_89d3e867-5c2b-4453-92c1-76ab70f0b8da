package tsdb

import "bs.com/app/internal/gw/dto"

const (
	attriMeasure = "attribute"
)

func (d *InfluxdbRepo) InsertDataAttr(deviceID, identifier string, fields map[string]any, ts int64) error {
	return d.InsertData(attriMeasure, map[string]string{
		"deviceID":   deviceID,
		"identifier": identifier,
	}, fields, ts)
}

func (d *InfluxdbRepo) QueryDataAttr(deviceID, identifier string, tsStart, tsEnd int64, fieldKeys []string, pginfo *dto.PageInfo) ([]map[string]any, error) {
	if pginfo == nil {
		return d.QueryData(attriMeasure, identifier, map[string]string{
			"deviceID":   deviceID,
			"identifier": identifier,
		}, tsStart, tsEnd, fieldKeys)
	} else {
		return d.QueryDataPage(attriMeasure, identifier, map[string]string{
			"deviceID":   deviceID,
			"identifier": identifier,
		}, tsStart, tsEnd, fieldKeys, pginfo)
	}
}
