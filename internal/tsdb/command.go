package tsdb

import "bs.com/app/internal/gw/dto"

const (
	commandMeasure = "command"
)

func (d *InfluxdbRepo) InsertDataCommand(deviceID, identifier string, fields map[string]any, ts int64) error {
	return d.InsertData(commandMeasure, map[string]string{
		"device_id":  deviceID,
		"identifier": identifier,
	}, fields, ts)
}

func (d *InfluxdbRepo) QueryDataCommand(deviceID, identifier string, tsStart, tsEnd int64, fieldKeys []string, pginfo *dto.PageInfo) ([]map[string]any, error) {
	tags := map[string]string{"device_id": deviceID}
	if identifier != "" {
		tags["identifier"] = identifier
	}
	if pginfo == nil {
		return d.QueryData(commandMeasure, identifier, tags, tsStart, tsEnd, fieldKeys)
	} else {
		return d.QueryDataPage(commandMeasure, identifier, tags, tsStart, tsEnd, fieldKeys, pginfo)
	}
}
