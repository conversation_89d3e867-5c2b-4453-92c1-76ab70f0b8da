package tsdb

import "bs.com/app/internal/gw/dto"

const (
	eventMeasure = "event"
)

func (d *InfluxdbRepo) InsertDataEvent(deviceID, identifier string, fields map[string]any, ts int64) error {
	return d.InsertData(eventMeasure, map[string]string{
		"device_id":  deviceID,
		"identifier": identifier,
	}, fields, ts)
}

func (d *InfluxdbRepo) QueryDataEvent(deviceID, identifier string, tsStart, tsEnd int64, fieldKeys []string, pgInfo *dto.PageInfo) ([]map[string]any, error) {
	tags := map[string]string{"device_id": deviceID}
	if identifier != "" {
		tags["identifier"] = identifier
	}
	if pgInfo == nil {
		return d.QueryData(eventMeasure, identifier, tags, tsStart, tsEnd, fieldKeys)
	} else {
		return d.QueryDataPage(eventMeasure, identifier, tags, tsStart, tsEnd, fieldKeys, pgInfo)
	}
}
