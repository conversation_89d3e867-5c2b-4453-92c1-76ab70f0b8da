package dto

import "bs.com/app/pkg/bean"

type AddDeviceTypeReq struct {
	TemplateKind   int32            `json:"template_kind"`   // 类型:1:自定义,2.模板库,3.产品库导入
	TemplateLibID  string           `json:"template_lib_id"` // 模板库ID
	Name           string           `json:"name" binding:"required"`
	Icon           string           `json:"icon"`
	AuthType       int32            `json:"auth_type"   binding:"required"` // 1:一型一密,2:一机一密
	AccessType     int32            `json:"access_type" binding:"required"`
	ConnType       string           `json:"conn_type"`
	Desc           string           `json:"desc"`
	Tags           []map[string]any `json:"tags"`
	AccessPointIDs map[string]any   `json:"access_point_ids"  binding:"required"`
}

// 设备类型扩展信息描述
type ExtendInfo struct {
	Name        string         `json:"name"`
	Identifier  string         `json:"identifier"`
	DataType    string         `json:"data_type"`
	Enabled     bool           `json:"enabled"`
	DataOptions map[string]any `json:"data_options"`
	Desc        string         `json:"desc"`
	CreateTime  int64          `json:"create_time"` // 时间戳
	IsStandard  bool           `json:"is_standard"`
}

type UpdateDeviceTypeReq struct {
	DeviceTypeID   string           `json:"device_type_id"`
	TemplateKind   int32            `json:"template_kind"`   // 类型:1:自定义,2.模板库,3.产品库导入
	TemplateLibID  string           `json:"template_lib_id"` // 模板库ID
	Name           string           `json:"name"      binding:"required"`
	Icon           string           `json:"icon"`
	AuthType       int32            `json:"auth_type"   binding:"required"`
	AccessType     int32            `json:"access_type" binding:"required"`
	ConnType       string           `json:"conn_type"`
	Desc           string           `json:"desc"`
	Tags           []map[string]any `json:"tags"`
	ExtendInfo     []ExtendInfo     `json:"extend_info"`
	AccessPointIDs map[string]any   `json:"access_point_ids"  binding:"required"`
	Status         int32            `json:"status"`
}

type AuthInfoResp struct {
	ClientID string `json:"client_id"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type UpdateAccessPointReq struct {
	AccessPointID string         `json:"access_point_id"  binding:"required"` //接入点ID
	Name          string         `json:"name"            binding:"required"`
	ConnProtocol  string         `json:"conn_protocol"  binding:"required"` // 接入协议.mqtt, tcp, http, 其他等
	MsgProtocol   string         `json:"msg_protocol"  binding:"required"`  // 消息上传或下发使用何种协议
	Host          string         `json:"host"`
	Port          uint16         `json:"port"`
	Status        int32          `json:"status"`
	ExtendInfo    map[string]any `json:"extend_info"` //其他配置，如用户名和密码。
}

type AddAccessPointReq struct {
	Name         string         `json:"name"           binding:"required"`
	ConnProtocol string         `json:"conn_protocol"  binding:"required"` // 接入协议.mqtt, tcp, http, 其他等
	MsgProtocol  string         `json:"msg_protocol"   binding:"required"` // 消息上传或下发使用何种协议
	Host         string         `json:"host"`
	Port         uint16         `json:"port"`
	ExtendInfo   map[string]any `json:"extend_info"` //其他配置，如用户名和密码。
}

type AddAttributeReq struct {
	DeviceTypeID string         ` json:"device_type_id"  binding:"required"` // 设备类型ID
	Name         string         ` json:"name"            binding:"required"`
	Identifier   string         ` json:"identifier"      binding:"required"`
	AttrType     string         ` json:"attr_type"       binding:"required"` // 属性类型,上报，下发等
	DataType     bean.DataType  ` json:"data_type"       binding:"required"` // 数据类型
	DataOptions  map[string]any ` json:"data_options"`                       // 属性选项
	Desc         string         ` json:"desc"`
}

type UpdateAttributeReq struct {
	DeviceTypeID string         ` json:"device_type_id"  binding:"required"` // 设备类型ID
	AttrID       string         ` json:"attr_id"         binding:"required"` // 属性id, 使用字符串而非自增。方便数据迁移
	Name         string         ` json:"name"            binding:"required"`
	Identifier   string         ` json:"identifier"      binding:"required"`
	AttrType     string         ` json:"attr_type"       binding:"required"` // 属性类型,上报，下发等
	DataType     bean.DataType  ` json:"data_type"       binding:"required"` // 数据类型
	DataOptions  map[string]any ` json:"data_options"`                       // 属性选项
	Desc         string         ` json:"desc"`
}

type Param struct {
	Name        string         ` json:"name"`
	DataType    bean.DataType  ` json:"data_type"`
	DataOptions map[string]any ` json:"data_options"`
	Identifier  string         ` json:"identifier"`
	Desc        string         ` json:"desc"`
}

type AddEventReq struct {
	DeviceTypeID string  ` json:"device_type_id"  binding:"required"` // 设备类型ID
	Name         string  ` json:"name"            binding:"required"`
	Identifier   string  ` json:"identifier"      binding:"required"`
	Desc         string  ` json:"desc"`
	Params       []Param ` json:"params"`
}

type UpdateEventReq struct {
	DeviceTypeID string  ` json:"device_type_id"   binding:"required"` // 设备类型ID
	EventID      string  ` json:"event_id"         binding:"required"`
	Name         string  ` json:"name"             binding:"required"`
	Identifier   string  ` json:"identifier"       binding:"required"`
	Desc         string  ` json:"desc"`
	Params       []Param ` json:"params"`
}

type AddCommandReq struct {
	DeviceTypeID       string         ` json:"device_type_id"  binding:"required"` // 设备类型ID
	Name               string         ` json:"name"            binding:"required"`
	Identifier         string         ` json:"identifier"      binding:"required"`
	Desc               string         ` json:"desc"`
	SendParams         []Param        ` json:"send_params"  `
	SendParamsDefault  map[string]any ` json:"send_params_default"`
	ReplyParams        []Param        ` json:"reply_params"  `
	ReplyParamsDefault map[string]any ` json:"reply_params_default"`
}

type UpdateCommandReq struct {
	DeviceTypeID       string         ` json:"device_type_id"  binding:"required"` // 设备类型ID
	CommandID          string         ` json:"command_id"      binding:"required"`
	Name               string         ` json:"name"            binding:"required"`
	Identifier         string         ` json:"identifier"      binding:"required"`
	Desc               string         ` json:"desc"`
	SendParams         []Param        ` json:"send_params"  `
	SendParamsDefault  map[string]any ` json:"send_params_default"`
	ReplyParams        []Param        ` json:"reply_params"  `
	ReplyParamsDefault map[string]any ` json:"reply_params_default"`
}

type CommandResp struct {
	Name        string         ` json:"name"`
	Identifier  string         ` json:"identifier"`
	Desc        string         ` json:"desc"`
	SendParams  map[string]any ` json:"send_params"  `
	ReplyParams map[string]any ` json:"reply_params"  `
	SendTs      int64          `json:"send_ts"`
	ReplyTs     int64          `json:"reply_ts"`
}

type EventResp struct {
	Name       string         ` json:"name"`
	Identifier string         ` json:"identifier"`
	Desc       string         ` json:"desc"`
	ParamsData map[string]any ` json:"params_data"  `
	Ts         int64          `json:"ts"`
}

// /
type AddDeviceReq struct {
	Name         string           `json:"name"              binding:"required"`
	DeviceID     string           `json:"device_id"` // 选填
	DeviceTypeID string           `json:"device_type_id"    binding:"required"`
	Desc         string           `json:"desc"`
	Tags         []map[string]any `json:"tags"`
}

type UpdateDeviceReq struct {
	Name         string           `json:"name"      binding:"required"`
	DeviceID     string           `json:"device_id" binding:"required"` // 更新必填
	DeviceTypeID string           `json:"device_type_id"`
	Status       int32            `json:"status"`
	ExtendInfo   map[string]any   `json:"extend_info"`
	Desc         string           `json:"desc"`
	Tags         []map[string]any `json:"tags"`
	Alarm        map[string]any   `json:"alarm"`
}

type MQTTConnectInfo struct {
	Broker   string `json:"broker"`
	ClientID string `json:"client_id"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type ConnectInfoResp struct {
	ConnProtocol string `json:"conn_protocol"`
	MsgProtocol  string `json:"msg_protocol"`
	Host         string `json:"host"`
	Port         uint16 `json:"port"`
	ExtendInfo   any    `json:"extend_info"` // 如果ConnProtocol为mqtt，则为MQTTConnectInfo类型
}
