package dto

import (
	"time"
)

// 查询 endpoint
type EndPointFilter struct {
	CompanyID      int64  // 后台填充参数
	ProjectCode    string `json:"project_code"  form:"project_code"    binding:"required"` // 项目code
	PointType      string `json:"point_type"    form:"point_type"`                         // 测点类型
	ProvinceID     int64  `json:"province_id"   form:"province_id"`                        // 省ID
	CityID         int64  `json:"city_id"       form:"city_id"`                            // 市ID
	DistrictID     int64  `json:"district_id"   form:"district_id"`                        // 区ID
	QueryDatapoint bool   `json:"query_datapoint"  form:"query_datapoint"`                 // 是否查询测站的datapoint, 默认是false
}

// 把项目涉及到到硬件，抽象一个 endpoint
// 视频监控、gnss测站、遥测终端、MCU
const (
	EP_RTU  = "RTU"
	EP_GNSS = "GNSS"
	EP_IPC  = "IPC"
)

type EndpointInfo struct {
	Name        string    `json:"name"`         // 名称
	StationType string    `json:"station_type"` // 类型  RTU  GNSS  IPC
	UUID        string    `json:"uuid"`         // 唯一编码，对于 station，是 station_sn。对于 gnss station是 deviceID
	Status      string    `json:"status"`       // online,offline
	Address     string    `json:"address"`      // 地址
	CreatedAt   time.Time `json:"created_at"`   // 添加时间
}

// 创建station Sensor map
type DataPointCreateReq struct {
	Name        string                 `json:"name"        binding:"required"` // 测点名字
	StationSN   string                 `json:"station_sn"  binding:"required"` //测站编码
	PointType   string                 `json:"point_type"  binding:"required"`
	Desc        string                 `json:"desc"`       // 描述
	PointCode   string                 `json:"point_code"` // 测点编号
	Lng         string                 `json:"lng"`
	Lat         string                 `json:"lat"`
	SectionCode string                 `json:"section_code"` // 所属断面编码
	LocationID  int64                  `json:"location_id"`
	Meta        map[string]interface{} `json:"meta"`
	DeviceInfo  string                 `json:"device_info"`
}

// 创建station Sensor map

// 创建station Sensor map
type DataPointDeleteReq struct {
	ID int64 `json:"id"         binding:"required"`
}

// 断面
type DamSectionFilter struct {
	ProjectCode string // 所属公司id
	Code        string // 断面编号
	Chainage    string // 桩号
	Name        string // 名称
	ID          int64  // 断面 ID
}
type DamSectionListReq struct {
	ProjectCode string `form:"project_code"     json:"project_code"  binding:"required"` // 项目编码
	Code        string `form:"code"             json:"code"`                             // 断面编号
	Chainage    string `form:"chainage"         json:"chainage"`                         // 桩号
	Name        string `form:"name"             json:"name"`                             // 名称
	ID          int64  `form:"id"               json:"id"`                               // 断面 ID
}

type DamSectionCreateReq struct {
	ProjectCode string ` json:"project_code"  binding:"required"` // 所属项目code
	Name        string `  json:"name"         binding:"required"` // 位置名字
	WallType    string `  json:"wall_type"`                       //  防渗墙类型
	Code        string `  json:"code"      binding:"required"`    // 断面编号
	Chainage    string `  json:"chainage"  binding:"required"`    // 桩号

	InnerWidth    string ` json:"inner_width"`    // 内坡面宽度
	ExteriorWidth string ` json:"exterior_width"` // 外坡宽度
	TopWidth      string `json:"top_width"`       // 顶部宽度
	SectionHeight string `json:"section_height"`  // 断面高度
	Desc          string ` json:"desc"`           // 描述

	Meta map[string]interface{} ` json:"meta" ` // 断面其他信息
}

type DamSectionUpdateReq struct {
	ID            int64  `json:"id"  binding:"required"`
	Name          string `json:"name"`           // 位置名字
	WallType      string `json:"wall_type"`      //  防渗墙类型
	Chainage      string `json:"chainage"`       // 桩号
	Code          string `json:"code"`           // 断面编号
	InnerWidth    string `json:"inner_width"`    // 内坡宽度
	ExteriorWidth string `json:"exterior_width"` // 外坡宽度
	TopWidth      string `json:"top_width"`      // 坝顶宽度
	SectionHeight string `json:"section_height"` // 坝顶高程

	Desc string                 ` json:"desc"`  // 描述
	Meta map[string]interface{} ` json:"meta" ` // 断面其他信息
}

type DamSectionDeleteReq struct {
	ID int64 `json:"id"  binding:"required"`
}

type ReservoirCapacityFilter struct {
	ProjectCode string `json:"project_code"  form:"project_code"` // 项目id (水库id)
}
