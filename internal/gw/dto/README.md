## 介绍

dto 是用于handler 和 dao之间传递参数的数据结构定义。

## validator

常用tag介绍：

ne：不等于参数值，例如 ne=5；
gt：大于参数值，例如 gt=5；
gte：大于等于参数值，例如 gte=50；
lt：小于参数值，例如 lt=50；
lte：小于等于参数值，例如 lte=50；
oneof：只能是列举出的值其中一个，这些值必须是数值或字符串，以空格分隔，如果字符串中有空格，将字符串用单引号包围，例如 oneof=male female。
eq：等于参数值，注意与 len不同。对于字符串， eq约束字符串本身的值，而 len约束字符串长度。例如 eq=10；
len：等于参数值，例如 len=10；
max：小于等于参数值，例如 max=10；
min：大于等于参数值，例如 min=10

常用约束
unique：指定唯一性约束，不同类型处理不同：

对于map，unique约束没有重复的值
对于数组和切片，unique没有重复的值
对于元素类型为结构体的碎片，unique约束结构体对象的某个字段不重复，使用 unique=field指定字段名
email：使用email来限制字段必须是邮件形式，直接写eamil即可，无需加任何指定。

omitempty：字段未设置，则忽略

-：跳过该字段，不检验；

|：使用多个约束，只需要满足其中一个，例如rgb|rgba；

required：字段必须设置，不能为默认值；



```go
    
type User struct {
    Name  string `validate:"required"`       //非空
    Age   uint8  `validate:"gte=0,lte=120"`  //  0<=Age<=120
    Email string `validate:"required,email"` //非空，email格式
    //dive关键字代表 进入到嵌套结构体进行判断
    Address []*Address `validate:"dive"` //  可以拥有多个地址
}

type Address struct {
    Province string `validate:"required"`       //非空
    City     string `validate:"required"`       //非空
    Phone    string `validate:"numeric,len=11"` //数字类型，长度为11
}
    
type Info struct {
    CreateTime time.Time `form:"create_time" binding:"required,timing" time_format:"2006-01-02"`
    UpdateTime time.Time `form:"update_time" binding:"required,timing" time_format:"2006-01-02"`
}
```