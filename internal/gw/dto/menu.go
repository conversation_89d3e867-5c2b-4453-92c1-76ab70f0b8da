package dto

import "time"

type AuthBtn struct {
	MenuID   int64  `json:"menu_id"`
	Name     string `json:"name"`
	Title    string `json:"title"`
	AuthMark string `json:"auth_mark"`
	Sort     int    `json:"sort"`
	Icon     string `json:"icon"`
}

type Meta struct {
	LangTag           string     `json:"lang_tag"`
	Title             string     `json:"title"`
	Sort              int        `json:"sort"`
	Icon              string     `json:"icon"`
	IsHide            bool       `json:"is_hide"`
	KeepAlive         bool       `json:"keep_alive"`
	IsHideTab         bool       `json:"is_hide_tab"`
	IsEnable          bool       `json:"is_enable"`
	IsMenu            bool       `json:"is_menu"`
	IsIframe          bool       `json:"is_iframe"`
	ShowBadge         bool       `json:"show_badge"`
	ShowTextBadge     bool       `json:"show_text_badge"`
	IsInMainContainer bool       `json:"is_in_main_container"`
	Link              string     `json:"link"`
	ActivePath        string     `json:"active_path"`
	AuthList          []*AuthBtn `json:"auth_list"`
}

type MenuResp struct {
	MenuID    int64       `json:"menu_id"`
	Path      string      `json:"path"`
	Name      string      `json:"name"`
	Component string      `json:"component"`
	Meta      *Meta       `json:"meta"`
	UpdatedAt time.Time   `json:"updated_at"`
	Children  []*MenuResp `json:"children,omitempty"` // Optional children
}

type AddUpdateMenuReq struct {
	Icon       string  `form:"icon"            json:"icon"       `
	IsEnable   *bool   `form:"is_enable"        json:"is_enable"       `
	IsHide     *bool   `form:"isHide"          json:"is_hide"       `
	KeepAlive  *bool   `form:"keep_alive"       json:"keep_alive"       `
	Link       string  `form:"link"            json:"link"       `
	MenuID     int64   `form:"menuID"          json:"menu_id"       `
	Sort       int     `form:"sort"            json:"sort"       `
	LangTag    string  `form:"lang_tag"         json:"lang_tag"    `
	ActivePath string  `form:"active_path"     json:"active_path"`
	IsMenu     *bool   `form:"is_menu"          json:"is_menu"     binding:"required"`
	Path       *string `form:"path"            json:"path"       binding:"required"`
	Name       string  `form:"name"            json:"name"       binding:"required"`
	Title      string  `form:"title"           json:"title"      binding:"required"`
	Component  *string `form:"component"       json:"component"  binding:"required"`
}
