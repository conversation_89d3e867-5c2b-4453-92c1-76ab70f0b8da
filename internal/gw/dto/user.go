package dto

// 创建或者更新 company
type ReqCompany struct {
	ID       int64  `form:"id"          json:"id,omitempty"`               //更新 company 的时候需要
	Name     string `form:"name"        json:"name"    binding:"required"` //项目名字
	FullName string `form:"fullname"    json:"fullname"`                   //公司全称
	Roles    string `form:"roles"       json:"roles"   binding:"required"` //公司可用角色ID，公司管理员可具有的最大权限，格式："1，2，3"
	Desc     string `form:"desc"        json:"desc"`                       //项目描述
	IsDevMan bool   `form:"is_dev_man" json:"is_dev_man"`                  //是否设备管理员
	//区域信息
	ProvinceID int64 `form:"province_id" json:"province_id"` //省ID, 必须要有
	CityID     int64 `form:"city_id"     json:"city_id"`     //市ID
	DistrictID int64 `form:"district_id" json:"district_id"` //区县ID

	//公司 logo
	Logo         string `form:"logo"           json:"logo"`          //logo 文件链接
	LogoFilename string `form:"logo_filename"  json:"logo_filename"` //logo 文件名
}

// 查询
type ReqSubUserQuery struct {
	PageInfo
	ID       int64  `form:"id"          json:"id"`
	Username string `form:"username"    json:"username"`
	Phone    string `form:"phone"       json:"phone"`
	Sex      int    `form:"sex"         json:"sex"`
	Status   int    `form:"status"      json:"status"`
}

// 添加、更新子用户信息
type ReqSubUser struct {
	ID       int64   `form:"id"          json:"id,omitempty"`                //更新 company 的时候需要
	Avatar   string  `form:"avatar"      json:"avatar"`                      //  头像
	Phone    string  `form:"phone"       json:"phone"`                       //手机号码
	UserName string  `form:"username"    json:"username" binding:"required"` //用户名字
	TelPhone string  `form:"tel_phone"   json:"tel_phone"`                   //座机号码
	Sex      int     `form:"sex"         json:"sex"`                         // 1:male  2：female
	Desc     string  `form:"desc"        json:"desc"`                        //备注
	Position string  `form:"position"    json:"position,omitempty"`          //职位
	Groups   []int64 `form:"groups"      json:"groups"`                      //分组ID，可多个，可为空
	Roles    []int64 `form:"roles"       json:"roles"    binding:"required"` //角色ID，可多个

	//区域
	ProvinceID int64 `form:"province_id" json:"province_id"` //省ID, 必须要有
	CityID     int64 `form:"city_id"     json:"city_id"`     //市ID
	DistrictID int64 `form:"district_id" json:"district_id"` //区县ID
}

// 用户自己更新自己的信息
type ReqUserUpdate struct {
	//如果更改手机号码，需要提供验证码。
	//如果不需要更改手机号码，可以为空。
	Phone       string `form:"phone"            json:"phone,omitempty"`        //手机号码，前端需要验证手机号码合法性
	PhoneCode   string `form:"phone_code"       json:"phone_code,omitempty"`   //短信验证码
	OldPassword string `form:"old_password"     json:"old_password,omitempty"` //旧密码
	NewPassword string `form:"new_password"     json:"new_password,omitempty"` //新密码
	UserName    string `form:"username"         json:"username,omitempty"`     //用户名
	TelPhone    string `form:"tel_phone"        json:"tel_phone,omitempty"`    //座机号码
	Sex         int    `form:"sex"              json:"sex,omitempty"`          // 1:man  2：woman
	Digit       string `form:"digit"            json:"digit,omitempty"`        // 存储用过的一次性密码

	//头像文件名，非数据库表字段。仅用于获取文件后缀。
	Avatar         string `form:"avatar"           json:"avatar,omitempty"`          //头像链接，收到文件之后填充，不需要 web 端提供
	AvatarFilename string `form:"avatar_filename"  json:"avatar_filename,omitempty"` //web 端如果上传了文件，就需要提供文件名
}

// 角色 ： 添加、更新
type ReqRole struct {
	ID       int64   `form:"id"                  json:"id,omitempty"`
	Title    string  `form:"title"               json:"title,omitempty"`                       //角色名字，中文备注
	Status   int     `form:"status"              json:"status,omitempty"`                      //状态
	SortNum  int     `form:"sort_num"            json:"sort_num,omitempty"`                    //显示排序
	Desc     string  `form:"desc"                json:"desc,omitempty"`                        //角色描述
	MenuList []int64 `form:"menu_list"         json:"menu_list,omitempty"  binding:"required"` //资源ID列表
}

// resource
// 资源 可以是前端页面、按钮，也可以是后端 API
type ReqResource struct {
	// ID          int64  `form:"id"         json:"id,omitempty"`
	MenuID    int64  `form:"menu_id"    json:"menuID,omitempty"`
	ParentID  int64  `form:"parent_id"  json:"parentID,omitempty"`  //上级菜单ID
	Type      int    `form:"type"       json:"type,omitempty"`      //资源类型（0 菜单 1 按钮）
	Title     string `form:"title"      json:"title,omitempty"`     //菜单名称
	Icon      string `form:"icon"       json:"icon,omitempty"`      //菜单图标
	Path      string `form:"path"       json:"path,omitempty"`      //路由地址
	Component string `form:"component"  json:"component,omitempty"` //组件路径
	SortNum   int    `form:"sortNum"    json:"sortNum,omitempty"`   //显示排序
	AuthMark  string `form:"auth_mark"  json:"auth_mark,omitempty"` //权限字符串
	Keepalive int    `form:"keepAlive"  json:"keepAlive,omitempty"` //是否缓存
	Visible   int    `form:"visible"    json:"visible,omitempty"`   //是否显示
	Status    int    `form:"status"      json:"status,omitempty"`   //状态
	Desc      string `form:"desc"        json:"desc,omitempty"`     // 备注
}

type ReqUserMessage struct {
	Type    int     `form:"type"      json:"type"`                            // 消息类型，字典枚举值
	From    int64   `form:"from"      json:"from"`                            // 消息创建者ID，系统消息，则为0
	To      []int64 `form:"to"        json:"to"`                              // 用户id 列表，消息接受者
	Title   string  `form:"title"     json:"title"        binding:"required"` // 标题
	Content string  `form:"content"   json:"content"      binding:"required"` // 内容
}
