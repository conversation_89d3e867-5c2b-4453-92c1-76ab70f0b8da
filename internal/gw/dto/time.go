package dto

import (
	"time"

	"bs.com/app/pkg/xlog"
)

// 时间戳，统一为单位 ms
type ReqTime struct {
	Start int64 `form:"start" json:"start"   `
	End   int64 `form:"end"   json:"end"`
}

func (t *ReqTime) CheckTime() {
	if t.End < t.Start {
		xlog.Warn("req time end < start, reset end to start")
		t.End = t.Start
	}
	if t.Start == 0 && t.End == 0 {
		t.SetDefaultTime()
	}
}
func (t *ReqTime) SetDefaultTime() {
	now := time.Now()

	// 设置开始时间为今天0点
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 设置结束时间为今天23:59:59
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())

	t.Start = startOfDay.UnixMilli()
	t.End = endOfDay.UnixMilli()
}

func (t *ReqTime) GetStart() int64 {
	return t.Start
}
func (t *ReqTime) GetEnd() int64 {
	return t.End
}
