package dto

import (
	"fmt"
	"strconv"
	"time"

	"bs.com/app/pkg/xlog"
)

type PositionInfo struct {
	ID                  int64          `json:"id"`                    // 序号
	DeviceID            string         `json:"device_id"`             // 设备号
	DeviceVersion       string         `json:"device_version"`        // 设备版本号
	Time                int64          `json:"time"`                  // 定位时间,UTC时间戳
	GPSTime             string         `json:"gpstime"`               // 定位时间，字符串
	Lng                 float64        `json:"lng"`                   // 经度,精确到10的6次方
	Lat                 float64        `json:"lat"`                   // 纬度,精确到10的6次方
	Speed               float64        `json:"speed"`                 // 速度,1/10KM/H
	Direction           int64          `json:"direction"`             // 方向,0-360
	NetSignal           int64          `json:"net_signal"`            // 网络信号,0-4:差;4-10:弱;11-17:一般,17-21:良;>22:优
	Satellite_num       int32          `json:"satellite_num"`         // 卫星定位数量
	DeviceMileage       float64        `json:"device_mileage"`        // 设备总里程
	DataFlow            int64          `json:"data_flow"`             // 流量, 1/100M
	Altitude            int64          `json:"altitude"`              // 海拔 米
	StatusFlag          int64          `json:"status_flag"`           // JT2023-2019状态标志位, http://8.130.14.213/api_doc/zh/internal/api/common_data/position.html#table1
	AlarmFlag           int64          `json:"alarm_flag"`            // JT2023-2019报警标志,定义http://8.130.14.213/api_doc/zh/internal/api/common_data/position.html#table2
	VideoAlarmFlag      int32          `json:"video_alarm_flag"`      // JT1078视频报警标志位 0:丢失;1:遮挡;2:存储单元;
	VideoLost           int64          `json:"video_lost"`            // 视频丢失,bit0-31位,表示1-32个逻辑通道
	VideoMask           int64          `json:"video_mask"`            // 视频屏蔽,bit0-31位,表示1-32个逻辑通道
	StorageStatus       int32          `json:"storage_status"`        // bit0-bit11,表示1-12个主存贮器，bit12-bit15为1-4个灾备存贮装置，对应位为1表示故障，0表示正常
	VehicleExternStatus int64          `json:"vehicle_extern_status"` // 车辆信号状态位,http://8.130.14.213/api_doc/zh/internal/api/common_data/position.html#table3
	OilValue            float64        `json:"oil_value"`             // 油量 1/10L
	DataValidFlag       int64          `json:"data_valid_flag"`       // 数据有效位,http://8.130.14.213/api_doc/zh/internal/api/common_data/position.html#table4
	DsmInfo             map[string]any `json:"dsm_info"`              // 胎压监测,http://8.130.14.213/api_doc/zh/internal/api/common_data/position.html#table7
	Bsd                 map[string]any `json:"bsd"`                   // 盲区监测,http://8.130.14.213/api_doc/zh/internal/api/common_data/position.html#table8
	VehicleGuid         int64          `json:"vehicle_guid"`          // 车辆ID
	PlatformMileage     int64          `json:"platform_mileage"`      // 平台总里程
	VehicleNo           string         `json:"vehicle_no"`            // 车牌号
	ReceiveTime         int64          `json:"receive_time"`          // 接收时间,UTC时间戳
	AdminGuid           int64          `json:"admin_guid"`            // 管理员id
	AppStatusFlag       int32          `json:"app_status_flag"`       // 应用的状态数据,定义查看http://8.130.14.213/api_doc/zh/internal/api/common_data/position.html#table9
	StopDriveStartTime  int64          `json:"stop_drive_start_time"` // 停车开始时间,UTC时间戳
	CurDayMileage       int64          `json:"cur_day_mileage"`       // 当天里程 米
	DriveTimeLen        int64          `json:"drive_time_len"`        // 当天行车时长 秒
}

// altitude: number  // 海拔
// direction: number // 方向
// gpstime: string // 定位时间
// lat: number // 纬度
// lng: number // 经度
// result: string // 位置
// speed: number // 速度

type PositionInfo2 struct {
	Altitude  int64   `json:"altitude"`  // 海拔
	Direction int64   `json:"direction"` // 方向
	GPSTime   string  `json:"gpstime"`   // 定位时间
	Lat       float64 `json:"lat"`       // 纬度
	Lng       float64 `json:"lng"`       // 经度
	Speed     float64 `json:"speed"`     // 速度
}

// 将系统时间字符串解析为Unix时间戳(秒)
// 输入格式: "20250706184008" (年月日时分秒)
// 返回: unix时间戳(秒)和可能的错误
func parseSysTimeToUnixTimestamp(systime string) (int64, error) {
	// 验证输入格式长度
	if len(systime) != 14 {
		return 0, fmt.Errorf("invalid systime format: %s, expected length 14", systime)
	}

	// 解析年月日时分秒
	year, err := strconv.Atoi(systime[0:4])
	if err != nil {
		return 0, fmt.Errorf("parse year error: %w", err)
	}

	month, err := strconv.Atoi(systime[4:6])
	if err != nil || month < 1 || month > 12 {
		return 0, fmt.Errorf("parse month error: %w", err)
	}

	day, err := strconv.Atoi(systime[6:8])
	if err != nil || day < 1 || day > 31 {
		return 0, fmt.Errorf("parse day error: %w", err)
	}

	hour, err := strconv.Atoi(systime[8:10])
	if err != nil || hour < 0 || hour > 23 {
		return 0, fmt.Errorf("parse hour error: %w", err)
	}

	minute, err := strconv.Atoi(systime[10:12])
	if err != nil || minute < 0 || minute > 59 {
		return 0, fmt.Errorf("parse minute error: %w", err)
	}

	second, err := strconv.Atoi(systime[12:14])
	if err != nil || second < 0 || second > 59 {
		return 0, fmt.Errorf("parse second error: %w", err)
	}

	// 创建时间对象
	t := time.Date(year, time.Month(month), day, hour, minute, second, 0, time.Local)

	// 返回Unix时间戳(秒)
	return t.Unix(), nil
}

func (p *PositionInfo) ToPositionInfo2() (*PositionInfo2, int64, error) {
	ts, e1 := parseSysTimeToUnixTimestamp(p.GPSTime)
	if e1 != nil {
		xlog.Error("parse systime error", "err", e1)
		return nil, 0, e1
	}
	gpsTime := time.Unix(ts, 0).Local().Format("2006-01-02 15:04:05")
	obj := &PositionInfo2{
		Altitude:  p.Altitude,
		Direction: p.Direction,
		GPSTime:   gpsTime,
		Lat:       p.Lat,
		Lng:       p.Lng,
		Speed:     p.Speed,
	}
	return obj, ts, nil
}
