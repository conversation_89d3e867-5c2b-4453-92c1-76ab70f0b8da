package dto

import (
	"fmt"

	"gorm.io/gorm"
)

const (
	OrderAes  = iota //从久到近排序
	OrderDesc        //时间从近到久排序
)

var orderMap = map[int64]string{
	OrderAes:  "aes",
	OrderDesc: "desc",
}

type OrderBy struct {
	Filed string `json:"filed" form:"filed"` //要排序的字段名
	Sort  int64  `json:"sort" form:"sort"`   //排序的方式：0 OrderAes、1 OrderDesc
}

// 分页信息：根据当前页号和每页元素数量，可以计算出查询的offset
type PageInfo struct {
	Page     int       `json:"page"            form:"page"`      //当前页序号
	PageSize int       `json:"page_size"       form:"page_size"` //每页元素数量
	Total    int64     `json:"total"           form:"total"`     //元素总数量，返回值的时候可以带上
	Orders   []OrderBy `json:"orderBy" form:"orderBy"`           // 排序信息
}

// 检查参数，如果为空，则用默认值
func (pi *PageInfo) CheckPage() {
	if pi.Page < 1 {
		pi.Page = 1
	}
	if pi.PageSize < 1 {
		pi.PageSize = 10
	}
}

func (p *PageInfo) GetLimit() int64 {
	if p == nil || p.PageSize == 0 {
		return 10
	}
	return int64(p.PageSize)
}
func (p *PageInfo) GetOffset() int64 {
	if p == nil || p.Page == 0 {
		return 0
	}
	return int64(p.PageSize * (p.Page - 1))
}

// 获取排序参数
func (p *PageInfo) GetOrders() (arr []string) {
	if p != nil && len(p.Orders) > 0 {
		for _, o := range p.Orders {
			arr = append(arr, fmt.Sprintf("%s %s", o.Filed, orderMap[o.Sort]))
		}
	}
	return
}

func (p *PageInfo) ToGorm(db *gorm.DB) *gorm.DB {
	if p == nil {
		return db
	}
	db = db.Offset(int(p.GetOffset())).Limit(int(p.GetLimit()))
	if len(p.Orders) != 0 {
		orders := p.GetOrders()
		for _, o := range orders {
			db = db.Order(o)
		}
	} else {
		db.Order("created_at desc")
	}
	return db
}

// 默认不分页的时候使用
var DefaultPi = PageInfo{
	Page:     1,
	PageSize: 100,
}
