package middleware

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"

	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xjwt"
)

var regexNoAuthUrlsPattern *regexp.Regexp
var unauthorizedPathList = []string{
	//以下列路径为前缀的接口，不需要校验token
	"/page",
	"/assets",
	"/cdn",

	"/api/iot/mg_device",
	"/api/iot/mg_station",
	"/api/iot/get_firmware",
	"/api/iot/latest_firmware",
	"/api/iot/gen_diff",
	"/api/iot/find_product",
	"/api/iot/pm_test",

	"/api/protocol",

	"/api/system/list_fault", //查看故障列表

	"/api/user/get_captcha",
	"/api/user/smscode",
	"/api/user/captcha",
	"/api/user/login",
	"/api/user/register",
	"/api/user/update_passwd",
	"/api/user/logout",
	"/api/system/ping",

	//方便使用
	"/api/adapter/sync_station",
	"/api/adapter/list_station",

	"/api/gnss", //gnss 数据推送
	"/api/ws",   // websocket连接，使用 ?token=xxx方式进行验证
	"/api/station/export_excel",

	// 生成 license
	"/api/system/gen_license",
}

func init() {
	regexNoAuthUrlsPattern, _ = regexp.Compile(fmt.Sprintf("%s", strings.Join(unauthorizedPathList, "|")))
}

const (
	ERRCODE_INVALID_TOKEN = 10021 // 无效的token
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Payload interface{} `json:"payload,omitempty"`
}

// AuthToken 鉴权，验证用户token是否有效
func Jwt() gin.HandlerFunc {
	return func(c *gin.Context) {
		r := c.Request
		if regexNoAuthUrlsPattern.MatchString(r.URL.Path) {
			c.Next()
			return
		}

		//xlog.Debug("jwt token parse")
		token, err := xjwt.GetJwtFromHeader(c)
		if err != nil {
			//c.JSON(http.StatusUnauthorized, respInvalidToKen)
			c.JSON(http.StatusOK, &Response{
				Code:    ERRCODE_INVALID_TOKEN,
				Message: "token丢失",
			})
			c.Abort()
			return
		}
		// 验证token是否正确
		claims, err := xjwt.ParseToken(token)
		if err != nil {
			c.JSON(http.StatusOK, &Response{
				Code:    ERRCODE_INVALID_TOKEN,
				Message: "token错误",
			})
			c.Abort()
			return
		}

		//如果 token 无效，则需要重新登录
		dao := model.NewIDao()
		if dao.IsTokenInValid(claims.UID) {
			//c.JSON(http.StatusUnauthorized, respInvalidToKen)
			c.JSON(http.StatusOK, &Response{
				Code:    ERRCODE_INVALID_TOKEN,
				Message: "平台引导重新登录",
			})
			c.Abort()
			return
		}

		//token 里面附带的信息
		c.Set("uid", claims.UID)
		c.Set("phone", claims.Phone)
		c.Set("is_super", claims.IsSuper)
		c.Set("company_id", claims.CompanyID)

		c.Set("province_id", claims.ProvinceID)
		c.Set("city_id", claims.CityID)
		c.Set("district_id", claims.DistrictID)

		c.Set("project_code", claims.ProjectCode)

		//xlog.Infof("phone: %s, company_id: %d", claims.Phone, claims.CompanyID)
		c.Next()
	}
}

// ==============================================
// // 自定义token data
// type CustomJwtClaims struct {
// 	jwt.StandardClaims
// 	UID       int64  `json:"uid"`
// 	Phone     string `json:"phone"`
// 	CompanyID int64  `json:"company_id"`

// 	ProvinceID int64 `json:"province_id"` //省ID
// 	CityID     int64 `json:"city_id"`     //市ID
// 	DistrictID int64 `json:"district_id"` //区县ID
// }

// const (
// 	tokenTTL  = 3600 * 24 * 7 // token 有效期 7 天
// 	jwtSecret = "dayu-jwt-secret-123456"
// )

const (
	JwtSecret = "dayu-jwt-secret-123456"
)

// 生成token
func GenerateToken(user *model.User) (string, error) {
	uid, phone := user.ID, user.Phone

	// token 有效期
	var tokenTTL int64
	// if user.IsSuper {
	tokenTTL = xjwt.TokenTTL * 7
	// } else {
	// 	tokenTTL = xjwt.TokenTTL * 1 // 1 小时
	// }

	staffClaims := xjwt.CustomJwtClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Second * time.Duration(tokenTTL))),
		},
		Phone:     phone,
		UID:       uid,
		IsSuper:   (phone == "18665991286"),
		CompanyID: user.CompanyID,

		ProvinceID: user.ProvinceID,
		CityID:     user.CityID,
		DistrictID: user.DistrictID,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, staffClaims)

	// SecretKey 用于对用户数据进行签名，不能暴露
	return token.SignedString([]byte(xjwt.JwtSecret))
}
