#### 介绍

gin的常用中间件。业务无关。

其中 jwt的中间件有如下配置需要抽取

```
// 请求头的形式为 Authorization: Bearer token
const (
	JwtHeader = "Authorization"
	tokenTTL            = 3600 * 24 * 90 // token 有效期 90天

	//中间件的错误码
	ERRCODE_InvalidToken uint32 = 4001 // jwt token错误
)

var regexNoAuthUrlsPattern *regexp.Regexp
var unauthorizedPathList = []string{
	//以下列路径为前缀的接口，不需要校验token
	"/api/common/",
	"/api/app/auth/",
	"/api/app/validation/",
	"/api/web/auth/",
	"/api/web/validation/",
}
```


## cp from eagle

Gin 自带的中间件有很多种，可以在 [https://github.com/gin-gonic/contrib](https://github.com/gin-gonic/contrib) 找到。

下面是一些常用的中间件

- RestGate：REST API 端点的安全身份验证
- gin-jwt：用于 Gin 框架的 JWT 中间件
- gin-sessions：基于 MongoDB 和 MySQL 的会话中间件
- gin-location：用于公开服务器主机名和方案的中间件
- gin-nice-recovery：异常错误恢复中间件，让您构建更好的用户体验
- gin-limit：限制同时请求，可以帮助解决高流量负载
- gin-oauth2：用于处理 OAuth2
- gin-template：简单易用的 Gin 框架 HTML/模板
- gin-redis-ip-limiter：基于 IP 地址的请求限制器
- gin-access-limit：通过指定允许的源 CIDR 表示法来访问控制中间件
- gin-session：Gin 的会话中间件
- gin-stats：轻量级且有用的请求指标中间件
- gin-session-middleware：一个高效，安全且易于使用的 Go 会话库
- ginception：漂亮的异常页面
- gin-inspector：用于调查 HTTP 请求的 Gin 中间件

## Reference
- https://github.com/chenjiandongx/ginprom