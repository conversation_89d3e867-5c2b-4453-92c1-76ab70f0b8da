package middleware

import (
	"bs.com/app/pkg/xlog"
	"github.com/gin-gonic/gin"
)

const (
	HeaderAppKey = "appkey"

	// 暂时不使用 appKey 鉴权
	// headerNonce     = "nonce"     // 随机字符串，长度是 10 ～ 30
	// headerTimestamp = "timestamp" // unix时间戳(字符串)，单位秒，请求的时候会检查时间戳，如果与服务器时间差异超过 10 秒，则认为请求非法
	// headerSign      = "sign"      // 签名结果
)

// APP调用接口，通过提取到 AppKey 来判断。这里检查到有 AppKey，就存入到 gin 的 context，让 handler 去使用
func MidForApp() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		appKey := ctx.Request.Header.Get(HeaderAppKey)

		if appKey != "" {
			// 将有效的 AppKey 存储到上下文中
			xlog.Debug("mid for app ------- get appKey : " + appKey)
			ctx.Set(Header<PERSON><PERSON><PERSON><PERSON>, appKey)
		}

		// 继续处理请求
		ctx.Next()
	}
}
