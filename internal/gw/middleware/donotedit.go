package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	ERRCODE_DEMO_ACCOUNT = 10022 // 无效的token
)

func DonotEdit() gin.HandlerFunc {
	return func(c *gin.Context) {
		r := c.Request
		url := r.URL.String()
		uid := c.GetInt64("uid")
		if uid == 21 {
			c.Next()
			return
		} else if r.Method != http.MethodGet && strings.HasPrefix(url, "/api/system") {
			//系统的非 GET 接口不允许操作
			c.JSON(http.StatusOK, &Response{
				Code:    ERRCODE_DEMO_ACCOUNT,
				Message: "演示平台，请勿修改数据",
			})
			c.Abort()
			return
		} else {
			c.Next()
			return
		}
	}
}
