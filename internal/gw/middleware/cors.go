package middleware

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

/*
如果允许跨域会发生诸多的安全问题，如CSRF攻击。所以浏览器默认会禁止跨域访问。
如果有需要，则需要服务器端配合浏览器做一些验证，表示对这种请求源的访问通过。

CORS需要浏览器和服务器同时支持。
目前，所有浏览器都支持该功能，IE浏览器不能低于IE10。
整个CORS通信过程，都是浏览器自动完成，不需要用户参与。
对于开发者来说，CORS通信与同源的AJAX通信没有差别，代码完全一样。
浏览器一旦发现AJAX请求跨源，就会自动添加一些附加的头信息，有时还会多出一次附加的请求，但用户不会有感觉。
因此，实现CORS通信的关键是服务器。只要服务器实现了CORS接口，就可以跨源通信。
*/

func GinCors() gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowMethods: []string{"GET", "POST", "OPTIONS"},
		AllowHeaders: []string{"Origin", "Authorization", "Content-Length", "Content-Type",
			"X-Requested-With", "Accept", "Connection", "User-Agent", "Cookie"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,

		//AllowAllOrigins:  true,
		AllowOriginFunc: func(origin string) bool {
			return true
		},
		MaxAge: 30 * 24 * time.Hour,
	})
}

// 简单的cors
func HttpCors() gin.HandlerFunc {
	return func(c *gin.Context) {
		w, r := c.Writer, c.Request
		w.Header().Set("Access-Control-Allow-Credentials", "true")
		origin := r.Header["Origin"]
		if len(origin) == 0 {
			w.Header().Set("Access-Control-Allow-Origin", "*")
		} else {
			w.Header().Set("Access-Control-Allow-Origin", origin[0])
		}
	}
}

const (
	maxAge = 12
)

// Cors add cors headers.
func EagleCors() gin.HandlerFunc {
	return cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"PUT", "PATCH", "GET", "POST", "OPTIONS", "DELETE"},
		AllowHeaders:     []string{"Origin", "Authorization", "Content-Type", "Accept"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		AllowOriginFunc: func(origin string) bool {
			return origin == "https://github.com"
		},
		MaxAge: maxAge * time.Hour,
	})
}
