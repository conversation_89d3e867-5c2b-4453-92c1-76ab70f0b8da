{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Golang Application Runtime metrics", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 10826, "graphTooltip": 0, "id": 3, "iteration": 1635506665171, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(eagle_http_request_duration_seconds_bucket{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m])) by (le))", "hide": false, "interval": "", "legendFormat": "all_request_duration_seconds_bucket | 1m", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(eagle_http_request_duration_seconds_bucket{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m]))by (service, handler, instance, le))", "hide": false, "interval": "", "legendFormat": "{{service}} | {{instance}} | url=\"{{handler}}\"  ", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "请求耗时", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 38, "panels": [], "repeat": null, "title": "Request Metrics", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 9}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(eagle_http_requests_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m])", "interval": "", "legendFormat": "{{pod}} | {{instance}} | url=\"{{handler}}\"  ", "refId": "A"}, {"expr": "sum(rate(eagle_http_requests_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m])) by (service, handler)", "interval": "", "legendFormat": " {{service}} | url=\"{{handler}}\" ", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 46, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(process_cpu_seconds_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m]) * 100)/count(process_cpu_seconds_total{namespace=\"$namespace\",service=\"$service\"})", "instant": false, "interval": "", "legendFormat": "CPU平均使用率", "refId": "A"}, {"expr": "count(process_cpu_seconds_total{namespace=\"$namespace\",service=\"$service\"})", "hide": false, "interval": "", "legendFormat": "pod数量", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "POD-CPU 平均使用率", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum  by (service, instance, handler, le) (rate(eagle_http_request_size_bytes_bucket{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\", le=\"+Inf\"}[5m]))", "hide": false, "interval": "", "legendFormat": "{{service}} | {{instance}} | \"{{handler}}\" | le={{le}}  | 5m", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request Size [+Inf]", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(process_cpu_seconds_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[30s]) * 100", "hide": true, "interval": "", "legendFormat": "process_cpu_usage_30s | {{service}} | {{instance}} | pod={{pod}}", "refId": "A"}, {"expr": "rate(process_cpu_seconds_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m]) * 100", "hide": false, "interval": "", "legendFormat": "process_cpu_usage_1m | {{service}} | {{instance}} | pod={{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "hiddenSeries": false, "id": 42, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(eagle_http_request_size_bytes_bucket{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[5m])) by (service, handler, le))", "interval": "", "legendFormat": "{{service}} | {{instance}} | \"{{handler}}\"  | 5m", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Size [95]", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 32}, "id": 36, "panels": [], "title": "Go Internal Metrics", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_open_fds{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "interval": "", "legendFormat": "{{service}} | {{instance}} | pod={{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Open File Handlers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_goroutines{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{service}} | {{instance}} | pod={{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Goroutines", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_gc_duration_seconds{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{service}} | {{instance}} | pod={{pod}} | quantile={{quantile}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC duration quantile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{service}} | {{instance}} | pod={{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total Used Memory", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 49}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_mspan_inuse_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_mspan_inuse_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "A"}, {"expr": "go_memstats_mspan_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_mcache_inuse_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "B"}, {"expr": "go_memstats_mcache_inuse_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_mcache_inuse_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "C"}, {"expr": "go_memstats_mcache_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_mcache_sys_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "D"}, {"expr": "go_memstats_buck_hash_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_buck_hash_sys_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "E"}, {"expr": "go_memstats_gc_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_gc_sys_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "F"}, {"expr": "go_memstats_other_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"} - go_memstats_other_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "bytes of memory are used for other runtime allocations | {{service}} | {{instance}} | pod={{pod}}", "refId": "G"}, {"expr": "go_memstats_next_gc_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_next_gc_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "H"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory in Off-Heap", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 49}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_heap_alloc_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_heap_alloc_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "B"}, {"expr": "go_memstats_heap_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_heap_sys_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "A"}, {"expr": "go_memstats_heap_idle_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_heap_idle_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "C"}, {"expr": "go_memstats_heap_inuse_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_heap_inuse_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "D"}, {"expr": "go_memstats_heap_released_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_heap_released_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory in Heap", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 57}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": false, "avg": true, "current": false, "max": true, "min": false, "rightSide": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_mallocs_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"} - go_memstats_frees_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "number_of_live_objects | {{service}} | {{instance}} | pod={{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Number of Live Objects", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "shows how many heap objects are allocated. This is a counter value so you can use rate() to objects allocated/s.", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 57}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(go_memstats_mallocs_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_mallocs | {{service}} | {{instance}} | pod={{pod}} | 1m", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rate of Objects Allocated", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 65}, "hiddenSeries": false, "id": 24, "legend": {"avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_stack_inuse_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_stack_inuse_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "A"}, {"expr": "go_memstats_stack_sys_bytes{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_stack_sys_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory in Stack", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 65}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 1, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(go_memstats_alloc_bytes_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_alloc_bytes | {{service}} | {{instance}} | pod={{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rates of Allocation", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "go_memstats_lookups_total – counts how many pointer dereferences happened. This is a counter value so you can use rate() to lookups/s.", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 73}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.2.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(go_memstats_lookups_total{namespace=\"$namespace\", service=\"$service\", pod=~\"$pod\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "go_memstats_lookups | {{service}} | {{instance}} | pod={{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rate of a Pointer Dereferences", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "30s", "schemaVersion": 31, "style": "dark", "tags": ["go", "golang"], "templating": {"list": [{"allValue": "", "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "label_values(go_goroutines, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(go_goroutines, namespace)", "refId": "Prometheus-namespace-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "label_values(go_goroutines{namespace=\"$namespace\"}, service)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "service", "multi": false, "name": "service", "options": [], "query": {"query": "label_values(go_goroutines{namespace=\"$namespace\"}, service)", "refId": "Prometheus-service-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "Prometheus", "definition": "label_values(go_goroutines{namespace=\"$namespace\",service=\"$service\"}, pod)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "pod", "multi": true, "name": "pod", "options": [], "query": {"query": "label_values(go_goroutines{namespace=\"$namespace\",service=\"$service\"}, pod)", "refId": "Prometheus-pod-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Go Metrics for testing", "uid": "CgCw8jKZz", "version": 19}