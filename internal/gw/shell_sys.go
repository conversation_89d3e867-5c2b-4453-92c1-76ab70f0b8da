package gw

import (
	"context"
	"strconv"

	"github.com/abiosoft/ishell/v2"

	"bs.com/app/global"
	"bs.com/app/internal/gw/tasks"
	"bs.com/app/pkg/now"
	"bs.com/app/pkg/xlog"
)

var (
	redisFlush = &ishell.Cmd{
		Name: "redis_flush",
		Help: "redis flush",
		Func: func(c *ishell.Context) {
			global.G.Cache.Client.FlushDB(context.Background())
		},
	}

	ts = &ishell.Cmd{
		Name: "ts",
		Help: "unix to loc date",
		Func: func(c *ishell.Context) {
			ts := c.Args[0]
			tsInt, err := strconv.ParseInt(ts, 10, 64)
			if err != nil {
				return
			}

			c.Println(">>>> : ", now.TimeUTCtoLocal2(tsInt))
		},
	}

	listCron = &ishell.Cmd{
		Name: "list_cron",
		Help: "列出所有cron任务",
		Func: func(c *ishell.Context) {
			m := tasks.ListAllCronTask()
			for k, v := range m {
				c.Println(k, v.ID, v.Next)
			}
		},
	}

	logLevel = &ishell.Cmd{
		Name: "log",
		Help: "修改log level",
		Func: func(c *ishell.Context) {
			if len(c.Args) != 2 {
				c.Println("usage:  error|warn|info|debug   text|json")
				return
			}
			level := c.Args[0]
			// format := c.Args[1]
			xlog.SetLevel(level)
		},
	}
)
