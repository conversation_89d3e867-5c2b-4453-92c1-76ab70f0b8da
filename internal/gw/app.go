package gw

import (
	"strconv"

	"bs.com/app/internal/gw/routers"
	"bs.com/app/pkg/httpserver"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

func NewHttpServer(port uint16) xwg.IService {
	xlog.Info("new http server", "port", port)
	//初始化路由
	handler := routers.InitHttpHandler()

	//转换为string
	portStr := strconv.FormatInt(int64(port), 10)

	return httpserver.NewServer("app-http-server", handler, httpserver.Port(portStr))
}
