package gw

import (
	"time"

	"github.com/abiosoft/ishell/v2"

	"bs.com/app/internal/dm"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

func NewGatewayShell() xwg.IService {
	xlog.Debug("NewGatewayShell")
	hi := &ishell.Cmd{
		Name: "hi",
		Help: "hi",
		Func: func(c *ishell.Context) {
			c.Println("hello: ", time.Now().String())
		},
	}

	db := &ishell.Cmd{
		Name: "db",
		Help: "db",
		Func: func(c *ishell.Context) {
			c.Println("db: ", time.Now().String())
			model.InitDeviceType()
		},
	}

	// 同步调用
	call := &ishell.Cmd{
		Name: "call",
		Help: "call",
		Func: func(c *ishell.Context) {
			c.Println("call: ", time.Now().String())
			deviceID := "qgby8nYj7B"
			resp, err := dm.OneDM.MessageDownToDeviceSync("action", deviceID, "area", map[string]any{
				"r":  100,
				"pi": 3.14,
			}, 10)
			if err != nil {
				c.Println("call: ", err)
				return
			}
			c.Println("call: ", resp)
		},
	}

	cmdArr := []*ishell.Cmd{
		hi, ts, db, call,
		listCron,

		redisFlush,
		logLevel,
	}
	return xwg.NewXshell("gw", cmdArr...)
}
