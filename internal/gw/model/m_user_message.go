package model

import (
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
)

type UserMessage struct {
	BaseModelCreateUpdate

	SendFrom int64  `gorm:"comment:消息创建者" json:"send_from"`       // 消息创建者ID，系统消息，则为0
	SendTo   int64  `gorm:"index;comment:用户ID" json:"send_to"`    // 用户id. user表主键
	Type     int    `gorm:"comment:消息类型" json:"type"`             // 消息类型，字典枚举值
	Title    string `gorm:"comment:消息标题" json:"title"`            // 标题
	Content  string `gorm:"comment:消息内容" json:"content"`          // 内容
	Status   int    `gorm:"default:1;comment:消息状态" json:"status"` // 状态, 1:未读，2:已读， 0：无效。默认未读
}

// 添加普通用户消息
// 一般由管理权限的人发送消息，或者选择某些消息推送给哪些人。管理权限的人在授权其他用户的时候，都会创建角色。所以发送消息可以根据角色ID。
// 如果是系统消息。sender为 0
func (d *IDao) MessageCreate(req *dto.ReqUserMessage) error {
	var msgArr []UserMessage
	for _, uid := range req.To {
		tmp := UserMessage{
			SendTo:   uid,
			SendFrom: req.From,
			Type:     req.Type,
			Title:    req.Title,
			Content:  req.Content,
		}
		msgArr = append(msgArr, tmp)
	}
	return d.db.Model(&UserMessage{}).Create(msgArr).Error
}

// 创建系统错误消息
func (d *IDao) MessageCreateErr(title, content string) error {
	var idArr []int64
	if err := d.db.Model(&User{}).Where("phone = ?", "18665991286").Select("id").Find(&idArr).Error; err != nil {
		return err
	}

	var msgArr []UserMessage
	for _, uid := range idArr {
		tmp := UserMessage{
			SendTo:   uid,
			SendFrom: 0,
			Type:     1, //系统故障
			Title:    title,
			Content:  content,
		}
		msgArr = append(msgArr, tmp)
	}
	return d.db.Model(&UserMessage{}).Create(msgArr).Error
}

// 创建系统警告消息
func (d *IDao) MessageCreateWarn(title, content string) error {
	idArr := []int64{1} //默认只有 ID为 1 的用户收到系统警告信息

	var msgArr []UserMessage
	for _, uid := range idArr {
		tmp := UserMessage{
			SendTo:   uid,
			SendFrom: 0,
			Type:     2, //系统警告
			Title:    title,
			Content:  content,
		}
		msgArr = append(msgArr, tmp)
	}
	return d.db.Model(&UserMessage{}).Create(msgArr).Error
}

// 修改用户消息：设置为已读
func (d *IDao) MessageRead(id int64) error {
	data := make(map[string]interface{})
	data["status"] = 2 //2：已读
	return d.db.Model(&UserMessage{}).Where("id = ? ", id).Updates(data).Error
}

// UID  ：消息接受者
func (d *IDao) MessageQuery(pi *dto.PageInfo, uid int64, status int, typ string) ([]*UserMessage, error) {
	var msgs []*UserMessage
	var session *gorm.DB

	data := make(map[string]interface{})
	data["send_to"] = uid

	if typ != "" {
		data["`type`"] = typ
	}

	if status != 0 {
		data["status"] = status
		session = d.db.Model(&UserMessage{}).Where(data).Order("id DESC")
	} else {
		//先按照状态排序，未读的靠前，在按照ID降序
		session = d.db.Model(&UserMessage{}).Where(data).Order("status ASC").Order("id DESC")
	}

	err := PagedFind(session, &msgs, pi)
	return msgs, err
}
