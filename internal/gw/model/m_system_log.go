package model

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/xlog"
)

// 日志表：操作日志
type SysLog struct {
	BaseModelCreate

	Title   string `gorm:"type:varchar(128);comment:操作标题" json:"title"`
	Content string `gorm:"type:varchar(255);comment:操作内容" json:"content"`

	//操作者身份
	CompanyID int64  `gorm:"index;default:0;comment:公司ID" json:"company_id"` //操作者所属公司ID
	UserID    int64  `gorm:"index;comment:操作者用户ID" json:"user_id"`
	Username  string `gorm:"comment:操作者名字" json:"username"`

	//操作者IP和地址
	OperIP   string `gorm:"comment:操作者IP" json:"oper_ip"`
	OperAddr string `gorm:"comment:操作者地址" json:"oper_addr"`

	Status int    `gorm:"comment:操作状态" json:"status"`
	Phone  string `gorm:"type:varchar(128);comment:操作者电话号码" json:"phone"`
}

func (d *IDao) AddNewLog(company_id, uid int64, title, content string) error {
	var user User
	err := d.db.Model(&user).Where("id = ?", uid).Find(&user).Error
	if err != nil {
		xlog.Error("add new log : find user by id failed")
		return err
	}
	one := &SysLog{
		Title:     title,
		Content:   content,
		CompanyID: company_id,
		UserID:    user.ID,
		Username:  user.Username,
		Phone:     user.Phone,
	}

	return d.db.Model(one).Create(one).Error
}

func (d *IDao) FindLogList(pi *dto.PageInfo, company_id int64, name, title string) ([]*SysLog, error) {
	var logs []*SysLog
	session := d.db.Model(&SysLog{})

	if company_id != 0 {
		session = session.Where("company_id = ?", company_id)
	}

	if name != "" {
		//名字的模糊搜素
		session = session.Where("username like ?", "%"+name+"%")
	} else if title != "" {
		//title 的模糊搜索
		session = session.Where("title like ?", "%"+title+"%")
	}

	//倒序
	session = session.Order("id DESC")
	err := PagedFind(session, &logs, pi)

	return logs, err
}
