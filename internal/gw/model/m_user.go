package model

import (
	"errors"
	"fmt"

	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"gorm.io/gorm"
)

// root 系统运维，后台管理
// admin 系统管理员
// user  普通用户
type User struct {
	BaseModelCreateUpdate

	Username string `gorm:"type:varchar(100);index;comment:姓名" json:"username"` //用户名
	Password string `gorm:"type:varchar(100);comment:密码" json:"-"`              //密码
	Sex      int    `gorm:"comment:性别" json:"sex"`                              // 1:man  2：woman，0：未知
	Phone    string `gorm:"index;unique;comment:手机号码" json:"phone"`             //手机号码
	TelPhone string `gorm:"comment:手机号码" json:"tel_phone"`                      //座机号码
	Avatar   string `gorm:"comment:头像" json:"avatar"`                           //头像

	//辖区，某些代理公司下的用户通过辖区来做测站数据的隔离
	ProvinceID int64 `gorm:"index;comment:省"  json:"province_id"` //省ID
	CityID     int64 `gorm:"index;comment:城市" json:"city_id"`     //市ID
	DistrictID int64 `gorm:"index;comment:区县" json:"district_id"` //区县ID

	//分组ID：多对多
	//公司ID：1对多
	//Organization string `gorm:"comment:所属单位" json:"organization"` //所属单位
	CompanyID int64  `gorm:"index;comment:公司ID" json:"company_id"`
	Position  string `gorm:"comment:职位" json:"position"` //职位

	Digit string `gorm:"type:text;comment:职位" json:"-"` //存储用过的密码

	Desc   string `gorm:"type:varchar(255);comment:描述信息" json:"desc"`
	Status int64  `gorm:"default:1;comment:状态(0:无效，1:允许，2:禁止)" json:"status"` //默认1

	//当前用户所属角色、可用菜单
	Roles []*Role `gorm:"-" json:"roles"`
	// Resources []*Resource `gorm:"-" json:"resources"`

	//公司和分组
	Company *Company     `gorm:"-" json:"company"`
	Groups  []*UserGroup `gorm:"-" json:"groups"`
	IsSuper bool         `gorm:"-" json:"is_super"`
}

// ================================================================

func (d *IDao) GetUserByPhone(phone string) (*User, error) {
	var u User
	err := d.db.Model(&u).Where("phone = ?", phone).First(&u).Error
	if err != nil {
		return nil, err
	} else {
		return &u, nil
	}
}

func (d *IDao) GetUserByPhoneOrName(phoneOrName string) (*User, error) {
	var u User
	err := d.db.Model(&u).Where("username = ? or phone = ?", phoneOrName, phoneOrName).First(&u).Error
	if err != nil {
		return nil, err
	} else {
		return &u, nil
	}
}

func (d *IDao) GetUserByUID(uid int64) (*User, error) {
	var user User
	err := d.db.Model(user).Where("id = ? ", uid).First(&user).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		xlog.Warn("record not found")
	}
	return &user, err
}

func (d *IDao) CheckSmsCode(phone, code string) bool {
	if code == "1234" || code == "8888" {
		return true
	}

	key := fmt.Sprintf("%s.%v", SMSPhonePrefix, phone)
	result, err := d.cache.GetString(key)
	if err != nil {
		return false
	}

	_ = d.cache.Del(key)

	return result == code
}

func (d *IDao) CheckPassword(uid int64, password string) bool {
	var one User
	//对比密码
	if err := d.db.Model(&User{}).Where("id = ?", uid).First(&one).Error; err != nil {
		return false
	}
	//对比hash
	loginPassword := xutils.HashPassword(password)
	return one.Password == loginPassword
}

// 第一次注册的时候，只更新用户名和密码
func (d *IDao) UpdateUserFirst(uid int64, name, password string) error {
	data := make(map[string]any)
	if name != "" {
		data["username"] = name
	}
	data["password"] = password
	return d.db.Model(&User{}).Where("id = ?", uid).Updates(data).Error
}

// 个人更新自己的信息
func (d *IDao) UpdateUser(uid int64, param *dto.ReqUserUpdate) error {
	data := bean.StructToMap(param)

	delete(data, "old_password")
	delete(data, "new_password")
	delete(data, "avatar_filename")

	if param.OldPassword != "" && param.NewPassword != "" {
		//TODO: 不建议使用明文密码
		data["password"] = param.NewPassword
	}

	if param.PhoneCode == "" || param.Phone == "" {
		delete(data, "phone") //短线验证码不是表格的信息
	}
	delete(data, "phone_code")      //短线验证码不是表格的信息
	delete(data, "avatar_filename") //短线验证码不是表格的信息
	return d.db.Model(&User{}).Where("id = ?", uid).Updates(data).Error
}

// 个人更新自己的信息
func (d *IDao) ResetPassword(uid int64, password string) error {
	return d.db.Model(&User{}).Where("id = ?", uid).Update("password", password).Error
}

// 更新子用户信息，包括描述信息、用户名、角色
// 当使用 struct 进行更新时，GORM 只会更新非零值的字段。 你可以使用 map 更新字段，或者使用 Select 指定要更新的字段
func (d *IDao) UpdateSubUser(param *dto.ReqSubUser) error {
	//事物中处理
	err := d.db.Transaction(func(tx *gorm.DB) error {
		//更新 user 表
		//整理参数：去掉user表不需要的字段
		fmt.Println("param:", param)
		data := StructToMap(param)
		delete(data, "roles")
		delete(data, "groups")

		fmt.Println("data:", data)

		if err := tx.Model(&User{}).Where("id = ?", param.ID).Updates(data).Error; err != nil {
			return err
		}

		//user-group-map
		if err := tx.Where("uid = ?", param.ID).Delete(&UserGroupMap{}).Error; err != nil {
			return err
		}

		if len(param.Groups) > 0 {
			if err := d.AddUserGroupMap(param.ID, param.Groups); err != nil {
				return err
			}
		}

		//重建：先删，后加
		if err := d.DelRoleUserMap(param.ID, 0); err != nil {
			return nil
		}
		if err := d.AddRoleUserMap(param.ID, param.Roles); err != nil {
			return err
		}

		return nil
	})
	return err
}

// 事务里面处理
func (d *IDao) AddSubUser(company_id int64, param *dto.ReqSubUser) error {
	return d.db.Transaction(func(tx *gorm.DB) error {

		//添加用户
		user := User{
			Username:   param.UserName,
			Phone:      param.Phone,
			TelPhone:   param.TelPhone,
			Sex:        param.Sex,
			Desc:       param.Desc,
			Position:   param.Position,
			CompanyID:  company_id,
			ProvinceID: param.ProvinceID,
			CityID:     param.CityID,
			DistrictID: param.DistrictID,
		}

		if err := tx.Model(&User{}).Create(&user).Error; err != nil {
			return err
		}

		if len(param.Groups) > 0 {
			//user-user_group-map
			if err := d.AddUserGroupMap(user.ID, param.Groups); err != nil {
				return err
			}
		}
		//角色绑定
		if err := d.AddRoleUserMap(user.ID, param.Roles); err != nil {
			return err
		}

		return nil
	})

}

// 删除用户：修改状态和时间
// 删除 role-user-map
func (d *IDao) DeleteSubUser(uid int64) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id = ?", uid).Delete(&User{}).Error; err != nil {
			return err
		}

		//delete role-user-map

		if err := d.DelRoleUserMap(uid, 0); err != nil {
			return err
		}
		return nil
	})
}

func (d *IDao) GetUsersByCompanyID(pi *dto.PageInfo, company_id int64) ([]*User, error) {
	var arr []*User
	session := d.db.Model(&User{}).Where("company_id = ?", company_id).Order("id ASC")
	err := PagedFind(session, &arr, pi)
	return arr, err
}

func (d *IDao) GetUsersByCompanyIDAndQuery(company_id int64, req *dto.ReqSubUserQuery) ([]*User, error) {
	var arr []*User
	condition := User{CompanyID: company_id}
	if req.ID > 0 {
		condition.ID = req.ID
	}
	if req.Status > 0 {
		condition.Status = int64(req.Status)
	}
	if req.Username != "" {
		condition.Username = req.Username
	}
	if req.Phone != "" {
		condition.Phone = req.Phone
	}
	if req.Sex > 0 {
		condition.Sex = req.Sex
	}
	session := d.db.Model(&User{}).Where(condition).Order("id ASC")
	err := PagedFind(session, &arr, &req.PageInfo)
	return arr, err
}

func (d *IDao) GetUserList(pi *dto.PageInfo, name string) ([]*User, error) {
	var arr []*User

	var err error
	if name != "" {
		err = d.db.Model(&User{}).Where("username like ?", "%"+name+"%").Find(&arr).Error
	} else {
		session := d.db.Model(&User{}).Order("id DESC")
		err = PagedFind(session, &arr, pi)
	}
	return arr, err
}

func (d *IDao) QueryUserList(company_id int64, name string) ([]*User, error) {
	var arr []*User

	var err error
	if name != "" {
		err = d.db.Model(&User{}).Where("company_id = ?", company_id).Where("username like ?", "%"+name+"%").Find(&arr).Error
	} else {
		err = d.db.Model(&User{}).Where("company_id = ?", company_id).Find(&arr).Error
	}
	return arr, err
}

// token invalid 用得上
// 超级用户就管理全部用户
// 非超级用户，就管理自己公司的用户
func (d *IDao) TokenInvalidAll(company_id int64) error {
	var arr []int64
	var err error
	if company_id == 0 {
		err = d.db.Model(&User{}).Select("id").Find(&arr).Error
	} else {
		err = d.db.Model(&User{}).Where("company_id = ?", company_id).Select("id").Find(&arr).Error
	}
	if err != nil {
		return err
	}
	for _, uid := range arr {
		//expire 时间是 jwt的token失效时间
		err = d.cache.Set(fmt.Sprintf("token_%d", uid), "invalid", 3600*24*7)
		if err != nil {
			break
		}
	}

	return err
}

// 添加redis缓存，标记此token 失效
func (d *IDao) TokenInvalid(uid int64) error {
	//expire 时间是 jwt的token失效时间
	return d.cache.Set(fmt.Sprintf("token_%d", uid), "invalid", 3600*24*7)
}

// 如果redis 里面有这个标记，则说明 token 无效，需要用户重新登录
func (d *IDao) IsTokenInValid(uid int64) bool {
	val, _ := d.cache.GetString(fmt.Sprintf("token_%d", uid))
	return val == "invalid"
}

// 删除 redis 缓存即可
func (d *IDao) TokenValid(uid int64) error {
	return d.cache.Del(fmt.Sprintf("token_%d", uid))
}
