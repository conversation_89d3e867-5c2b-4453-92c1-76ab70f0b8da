package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*
这个是参考样例
使用教程:
1. 将 Example全局替换为模型的表名
2. 完善todo
*/

// 数据库Model
type SysExample struct {
	ID int64 `gorm:"-" json:"id"`
}

type ExampleRepo struct {
	db *gorm.DB
}

func NewExampleRepo() *ExampleRepo {
	return &ExampleRepo{db: global.DB()}
}

type ExampleFilter struct {
	//todo 添加过滤字段
}

func (p ExampleRepo) fmtFilter(f ExampleFilter) *gorm.DB {
	db := p.db
	//todo 添加条件
	return db
}

func (p ExampleRepo) Insert(data *SysExample) error {
	return p.db.Create(data).Error
}

func (p ExampleRepo) FindOneByFilter(f ExampleFilter) (*SysExample, error) {
	var result SysExample
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (p ExampleRepo) FindByFilter(f ExampleFilter, page *dto.PageInfo) ([]*SysExample, error) {
	var results []*SysExample
	db := p.fmtFilter(f).Model(&SysExample{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (p ExampleRepo) CountByFilter(f ExampleFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&SysExample{})
	err = db.Count(&size).Error
	return size, err
}

func (p ExampleRepo) Update(data *SysExample) error {
	err := p.db.Where(SysExample{ID: data.ID}).Save(data).Error
	return err
}

func (p ExampleRepo) DeleteByFilter(f ExampleFilter) error {
	db := p.fmtFilter(f)
	err := db.Delete(&SysExample{}).Error
	return err
}

func (p ExampleRepo) Delete(id int64) error {
	err := p.db.Where(SysExample{ID: id}).Delete(&SysExample{}).Error
	return err
}
func (p ExampleRepo) FindOne(id int64) (*SysExample, error) {
	var result SysExample
	err := p.db.Where(SysExample{ID: id}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p ExampleRepo) MultiInsert(data []*SysExample) error {
	err := p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&SysExample{}).Create(data).Error
	return err
}
