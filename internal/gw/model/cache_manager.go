package model

import (
	"sync"
	"time"
)

// CacheManager 全局缓存管理器
type CacheManager struct {
	deviceTypeCache *Cache[*DeviceType]
	attributeCache  *Cache[[]*Attribute]
	eventCache      *Cache[[]*Event]
	commandCache    *Cache[[]*Command]
	deviceCache     *Cache[*Device]
}

var (
	cacheManager *CacheManager
	cacheOnce    sync.Once
)

// GetCacheManager 获取全局缓存管理器实例
func GetCacheManager() *CacheManager {
	cacheOnce.Do(func() {
		cacheManager = &CacheManager{
			deviceTypeCache: NewCache[*DeviceType](10 * time.Minute),
			attributeCache:  NewCache[[]*Attribute](10 * time.Minute),
			eventCache:      NewCache[[]*Event](10 * time.Minute),
			commandCache:    NewCache[[]*Command](10 * time.Minute),
			deviceCache:     NewCache[*Device](10 * time.Minute),
		}
	})
	return cacheManager
}

// DeviceTypeCache 获取设备类型缓存
func (cm *CacheManager) DeviceTypeCache() *Cache[*DeviceType] {
	return cm.deviceTypeCache
}

// AttributeCache 获取属性缓存
func (cm *CacheManager) AttributeCache() *Cache[[]*Attribute] {
	return cm.attributeCache
}

// EventCache 获取事件缓存
func (cm *CacheManager) EventCache() *Cache[[]*Event] {
	return cm.eventCache
}

// CommandCache 获取命令缓存
func (cm *CacheManager) CommandCache() *Cache[[]*Command] {
	return cm.commandCache
}

// DeviceCache 获取设备缓存
func (cm *CacheManager) DeviceCache() *Cache[*Device] {
	return cm.deviceCache
}
