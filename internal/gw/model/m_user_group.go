package model

import (
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 分组、组织、部门，必须属于某个 company
// 树状结构
type UserGroup struct {
	BaseModelCreateUpdate

	ParentID  int64  `gorm:"index;comment:父分组ID" json:"parent_id"`            //上级ID，如果为0，则为根分组
	CompanyID int64  `gorm:"index;not null;comment:所属公司ID" json:"company_id"` //公司ID
	Name      string `gorm:"type:varchar(100);index;comment:用户分组名字" json:"name"`
	Desc      string `gorm:"type:varchar(255);comment:描述信息" json:"desc"`

	Children []*UserGroup `gorm:"-" json:"children"`  //下属分组
	UserList []*User      `gorm:"-" json:"user_list"` //此分组下的所有用户列表
}

// 每个 company 都有自己的用户分组。所以需要缓存这个树，方便使用。
var mUserGroupTree map[int64]*UserGroup

func (d *IDao) rebuildUserGroupTree() error {
	var arr []*UserGroup
	if err := d.db.Model(&UserGroup{}).Order("id ASC").Find(&arr).Error; err != nil {
		return err
	}
	mUserGroupTree = make(map[int64]*UserGroup)
	for _, one := range arr {
		mUserGroupTree[one.ID] = one
		parent, ok := mUserGroupTree[one.ParentID]
		if ok {
			parent.Children = append(parent.Children, one)
		}
	}
	return nil
}

// parent_id 可以是空
// company_id 不可以为0
func (d *IDao) AddUserGroup(company_id, parent_id int64, name, desc string) error {
	one := UserGroup{
		ParentID:  parent_id,
		CompanyID: company_id,
		Name:      name,
		Desc:      desc,
	}

	mUserGroupTree = nil

	return d.db.Create(&one).Error
}

// 删除分组
func (d *IDao) DelUserGroup(id int64) error {
	//删除 group
	//同步删除 user-group-map
	return d.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("group_id = ?", id).Delete(&UserGroupMap{}).Error; err != nil {
			return err
		}

		if err := tx.Where("id = ?", id).Delete(&UserGroup{}).Error; err != nil {
			return err
		}

		mUserGroupTree = nil

		return nil
	})
}

// 仅更新名字和描述
func (d *IDao) UpdateUserGroup(id, parent_id int64, name, desc string) error {
	data := map[string]interface{}{
		"parent_id": parent_id,
		"name":      name,
		"desc":      desc,
	}
	mUserGroupTree = nil
	return d.db.Model(&UserGroup{}).Where("id = ?", id).Updates(data).Error
}

func (d *IDao) GetUserGroupList(company_id int64, pi *dto.PageInfo) ([]*UserGroup, error) {
	var arr []*UserGroup
	session := d.db.Model(&UserGroup{}).Where("company_id = ?", company_id)

	err := PagedFind(session, &arr, pi)
	return arr, err
}

// 返回某个 company 全部的分组。树状显示
func (d *IDao) GetUserGroupTree(company_id int64) ([]*UserGroup, error) {
	var idsArr []int64
	err := d.db.Model(&UserGroup{}).Where("company_id = ? and parent_id = 0", company_id).Select("id").Find(&idsArr).Error
	if err != nil {
		return nil, err
	}

	if mUserGroupTree == nil {
		err = d.rebuildUserGroupTree()
		if err != nil {
			return nil, err
		}
	}

	var arr []*UserGroup
	for _, one := range idsArr {
		arr = append(arr, mUserGroupTree[one])
	}

	return arr, nil
}

// user-usergroup 映射： 一个用户可以属于多个分组。一个分组也可以有多个用户
// 用户可以不属于任何一个分组，未被分组的用户，不建立 uid-gid 映射
// idx_usergroupm_uid_gid 联合唯一索引
type UserGroupMap struct {
	BaseModelCreate
	//联合唯一索引

	UID     int64 `gorm:"uniqueIndex:idx_usergroupm_uid_gid;comment:用户ID" json:"uid"`
	GroupID int64 `gorm:"uniqueIndex:idx_usergroupm_uid_gid;comment:用户分组ID" json:"group_id"`
}

func (d *IDao) AddUserGroupMap(uid int64, groups []int64) error {
	var arr []UserGroupMap

	for _, group_id := range groups {
		arr = append(arr, UserGroupMap{
			UID:     uid,
			GroupID: group_id,
		})
	}
	//如果已经存在，则啥也不干，否则插入
	return d.db.Model(&UserGroupMap{}).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "uid"}, {Name: "group_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"id"}),
	}).Create(&arr).Error
}

func (d *IDao) DelUserGroupMap(uid, gid int64) error {
	mUserGroupTree = nil

	return d.db.Where("uid = ? and group_id = ?", uid, gid).Delete(&UserGroupMap{}).Error
}

// name 模糊搜索
func (d *IDao) QueryUserGroup(company_id int64, name string) ([]*UserGroup, error) {
	var arr []*UserGroup
	err := d.db.Model(&UserGroup{}).Where("company_id = ?", company_id).Where("name like ?", "%"+name+"%").Find(&arr).Error
	if err != nil {
		return nil, err
	}

	return arr, nil
}

// 返回某人所属分组
func (d *IDao) GetUserGroupByUID(uid int64) ([]*UserGroup, error) {
	var idsArr []int64
	err := d.db.Model(&UserGroupMap{}).Where("uid = ?", uid).Select("group_id").Find(&idsArr).Error
	if err != nil {
		return nil, err
	}

	var arr []*UserGroup
	err = d.db.Model(&UserGroup{}).Where("id in ?", idsArr).Find(&arr).Error

	return arr, err
}
func (d *IDao) GetUserListByGID(gid int64) ([]*User, error) {
	var idsArr []int64
	err := d.db.Model(&UserGroupMap{}).Where("group_id = ?", gid).Select("uid").Find(&idsArr).Error
	if err != nil {
		return nil, err
	}
	var arr []*User
	err = d.db.Model(&User{}).Where("id in ?", idsArr).Find(&arr).Error
	return arr, err
}

/*
[4.188ms] [rows:0] SELECT `id` FROM `user_group` WHERE company_id = 2
INFO   [2023-01-23 11:50:59] /Users/<USER>/GolandProjects/go1/bs.com/app/internal/gw/model/m_user_group.go:181
[3.454ms] [rows:0] SELECT `uid` FROM `user_group_map` WHERE group_id in (NULL)
INFO   [2023-01-23 11:50:59] /Users/<USER>/GolandProjects/go1/bs.com/app/internal/gw/model/m_user_group.go:187
[4.000ms] [rows:0] SELECT * FROM `user` WHERE company_id = 2 AND id not in (NULL)

每次都要先判空吗？
*/
func (d *IDao) GetUserListUnGrouped(company_id int64) ([]*User, error) {
	var gidArr []int64
	var uidGroupedArr []int64

	//查询此company 的所有分组 id
	if err := d.db.Model(&UserGroup{}).Where("company_id = ?", company_id).Select("id").Find(&gidArr).Error; err != nil {
		return nil, err
	}

	//查询此公司，没在这个分组里面的用户
	session := d.db.Model(&User{}).Where("company_id = ?", company_id)
	if len(gidArr) > 0 {
		//查询已经分组的用户ID
		if err := d.db.Model(&UserGroupMap{}).Where("group_id in ?", gidArr).Select("uid").Find(&uidGroupedArr).Error; err != nil {
			return nil, err
		}
		//过滤已经分组的
		if len(uidGroupedArr) > 0 {
			session = session.Where("id not in ?", uidGroupedArr)
		}
	}
	var arr []*User
	if err := session.Find(&arr).Error; err != nil {
		return nil, err
	}
	return arr, nil
}
