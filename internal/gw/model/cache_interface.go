package model

// CacheInterface 缓存操作接口
type CacheInterface[T any] interface {
	// Get 获取缓存数据
	Get(key string) (T, bool)

	// Set 设置缓存数据
	Set(key string, value T)

	// Delete 删除指定键的缓存
	Delete(key string)
}

// RepoCacheInterface repo层缓存操作接口
// 为各个repo提供统一的缓存操作方法
type RepoCacheInterface[T any] interface {
	// CacheGet 从缓存获取数据
	CacheGet(key string) (T, bool)

	// CacheSet 设置数据到缓存
	CacheSet(key string, value T)

	// CacheDelete 删除缓存中的数据
	CacheDelete(key string)
}
