package model

import (
	"sync"
	"time"

	"bs.com/app/global"
	"gorm.io/gorm"
)

type BaseModel struct {
	//gorm.Model
	ID int64 `gorm:"primaryKey" json:"id"` // 默认是主键ID,auto_increment
}

// 时间统一使用时间戳，单位秒
type BaseModelCreate struct {
	ID        int64     `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time `gorm:"index;comment:创建时间" json:"created_at"`
}

type BaseModelCreateUpdate struct {
	ID        int64     `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time `gorm:"index;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"comment:更新时间" json:"updated_at"`
}

type BaseModelCreateUpdateLite struct {
	ID        int64     `gorm:"primaryKey" json:"id"`
	CreatedAt time.Time `gorm:"index;comment:创建时间" json:"-"`
	UpdatedAt time.Time `gorm:"comment:更新时间" json:"-"`
}

// 如果需要软删除功能，使用此定义
// type BaseModelCreateUpdateDelete struct {
// 	ID        int64          `gorm:"primaryKey" json:"id"`
// 	CreatedAt time.Time      `gorm:"index;comment:创建时间" json:"created_at"`
// 	UpdatedAt time.Time      `gorm:"comment:更新时间" json:"-"`
// 	DeletedAt gorm.DeletedAt `gorm:"index;comment:删除时间" json:"-"` //删除时间，默认创建索引
// }

// type BaseModelCreateUpdateDeleteLite struct {
// 	ID        int64          `gorm:"primaryKey" json:"id"`
// 	CreatedAt time.Time      `gorm:"index;comment:创建时间" json:"-"`
// 	UpdatedAt time.Time      `gorm:"comment:更新时间" json:"-"`
// 	DeletedAt gorm.DeletedAt `gorm:"comment:删除时间" json:"-"` //删除时间，默认创建索引
// }

// //-----------------------------------

type IDao struct {
	db    *gorm.DB
	cache *global.Redis
	lock  sync.Mutex

	deviceType *DeviceTypeRepo
}

func NewIDao() *IDao {
	return &IDao{
		db:         global.DB(),
		cache:      global.Cache(),
		lock:       sync.Mutex{},
		deviceType: NewDeviceTypeRepo(),
	}
}
