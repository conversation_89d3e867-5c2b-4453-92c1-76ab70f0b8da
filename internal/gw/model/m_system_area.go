package model

import (
	"errors"
	"strings"

	"bs.com/app/pkg/xlog"
)

// 行政区域表
type Area struct {
	ID        int64  `gorm:"primaryKey" json:"id"` // 默认是主键ID,auto_increment
	Name      string `gorm:"type:varchar(255);comment:区域名" json:"name"`
	ParentID  int64  `gorm:"index;comment:父级区域ID" json:"parent_id"`
	AreaCode  string `gorm:"index;type:varchar(32);comment:行政区域编码" json:"area_code"`
	CityCode  string `gorm:"type:varchar(16);comment:城市编码" json:"city_code"`
	LngLat    string `gorm:"type:varchar(32);comment:经纬度" json:"lng_lat"`
	AreaLevel string `gorm:"type:varchar(16);comment:级别" json:"area_level"`
	Level     int    `gorm:"index;comment:级别：1省 2市 3区" json:"level"`

	ChildrenNum int     `gorm:"-" json:"children_num"`
	Children    []*Area `gorm:"-" json:"children"`
}

var mapAreaTree map[int64]*Area //key: id , val:area

const (
	AreaLevel_Country  int = 0
	AreaLevel_Province     = 1
	AreaLevel_City         = 2
	AreaLevel_District     = 3
)

func (d *IDao) ToAreaLongName222(province_id, city_id, district_id int64) string {
	var err error
	sb := strings.Builder{}

	// province := Area{}
	// err = d.db.Model(&Area{}).Where("id = ?", province_id).First(&province).Error
	// if err != nil {
	// 	return sb.String()
	// } else {
	// 	sb.WriteString(province.Name)
	// }

	city := Area{}
	err = d.db.Model(&Area{}).Where("id = ?", city_id).First(&city).Error
	if err != nil {
		return sb.String()
	} else {
		sb.WriteString(city.Name)
	}

	district := Area{}
	err = d.db.Model(&Area{}).Where("id = ?", district_id).First(&district).Error
	if err != nil {
		return sb.String()
	} else {
		sb.WriteString(district.Name)
	}

	return sb.String()
}

// 返回市+区的长名字
func (d *IDao) ToAreaLongName(province_id, city_id, district_id int64) string {
	if len(mapAreaTree) == 0 {
		//插入成功，强制更新缓存
		_ = d.InitAreaTree()
	}

	sb := strings.Builder{}
	if province, ok1 := mapAreaTree[province_id]; ok1 {
		_ = province
		// sb.WriteString(province.Name)
		if city, ok2 := mapAreaTree[city_id]; ok2 {
			sb.WriteString(city.Name)
			if district, ok3 := mapAreaTree[district_id]; ok3 {
				sb.WriteString(district.Name)
			}
		}
	}

	return sb.String()
}

// 返回最后一级行政区域的名字
func (d *IDao) ToAreaShortName(province_id, city_id, district_id int64) string {
	if len(mapAreaTree) == 0 {
		//插入成功，强制更新缓存
		_ = d.InitAreaTree()
	}

	district, ok := mapAreaTree[district_id]
	if ok {
		return district.Name
	}

	city, ok := mapAreaTree[city_id]
	if ok {
		return city.Name
	}

	province, ok := mapAreaTree[province_id]
	if ok {
		return province.Name
	}

	return ""
}

// 返回某个行政区域的名字
func (d *IDao) ToAreaName(area_id int64) string {

	if len(mapAreaTree) == 0 {
		_ = d.InitAreaTree() //插入成功，强制更新缓存
	}

	a, ok := mapAreaTree[area_id]
	if ok {
		return a.Name
	}

	return ""
}

func (d *IDao) AreacodeToAreaID(area_code string) (province_id, city_id, district_id int64, err error) {
	if len(mapAreaTree) == 0 {
		//插入成功，强制更新缓存
		_ = d.InitAreaTree()
	}

	var one Area

	err = d.db.Model(&Area{}).Where("area_code = ?", area_code).First(&one).Error
	if err != nil {
		return
	}

	var ok bool
	tmp := &one
	for {
		//xlog.Debug("tmp: ", xutils.JSONString(tmp))
		if tmp.Level == AreaLevel_Province {
			province_id = tmp.ID
			break
		} else if tmp.Level == AreaLevel_City {
			city_id = tmp.ID
		} else if tmp.Level == AreaLevel_District {
			district_id = tmp.ID
		}

		if tmp.ParentID == 0 {
			break
		}

		tmp, ok = mapAreaTree[tmp.ParentID]
		if !ok {
			err = errors.New("invalid parant: " + tmp.Name)
			return
		}
	}

	return
}

// 通过省市区ID，返回区域ID
func (d *IDao) AreaIDToAreaCode(province_id, city_id, district_id int64) (area_code string, err error) {
	if len(mapAreaTree) == 0 {
		//插入成功，强制更新缓存
		_ = d.InitAreaTree()
	}

	//先匹配区县
	if district_id != 0 {
		district, ok := mapAreaTree[district_id]
		if ok {
			area_code = district.AreaCode
		} else {
			err = errors.New("invalid district ID")
		}
		return
	}

	if city_id != 0 {
		city, ok := mapAreaTree[city_id]
		if ok {
			area_code = city.AreaCode
		} else {
			err = errors.New("invalid city ID")
		}
		return
	}
	if province_id != 0 {
		province, ok := mapAreaTree[province_id]
		if ok {
			area_code = province.AreaCode
		} else {
			err = errors.New("invalid province ID")
		}
		return
	}

	err = errors.New("invalid area ID")
	return
}

// 全局 缓存
func (d *IDao) InitAreaTree() error {
	xlog.Info("init area tree")

	d.lock.Lock()
	defer d.lock.Unlock()

	areas := make([]*Area, 0)
	//err := d.db.Model(&Area{}).Where("level > ?", AreaLevel_Province).Find(&areas).Order("id ASC").Error
	err := d.db.Model(&Area{}).Find(&areas).Order("id ASC").Error
	if err != nil {
		xlog.Error("init area tree failed", "err", err)
		return err
	}

	mapAreaTree = make(map[int64]*Area) //key: id , val:area
	//一次循环
	for _, one := range areas {
		mapAreaTree[one.ID] = one
	}

	//二次循环：填充children
	for _, one := range areas {
		if parent, ok := mapAreaTree[one.ParentID]; ok {
			parent.Children = append(parent.Children, one)
			parent.ChildrenNum += 1
		}
	}
	return nil
}

// 查询下一级
func (d *IDao) GetAreaListByParentID(id int64) ([]*Area, error) {
	if len(mapAreaTree) == 0 {
		_ = d.InitAreaTree()
	}
	parent, ok := mapAreaTree[id]
	if !ok {
		return nil, errors.New("not found")
	}

	arr := []*Area{}
	for _, child := range parent.Children {
		childCopy := &Area{
			ID:          child.ID,
			Name:        child.Name,
			ParentID:    child.ParentID,
			AreaCode:    child.AreaCode,
			CityCode:    child.CityCode,
			LngLat:      child.LngLat,
			AreaLevel:   child.AreaLevel,
			Level:       child.Level,
			ChildrenNum: len(child.Children),
		}
		arr = append(arr, childCopy)
	}
	return arr, nil
}

func (d *IDao) GetAreaTreeByID(id int64) (*Area, error) {
	if len(mapAreaTree) == 0 {
		//插入成功，强制更新缓存
		_ = d.InitAreaTree()
	}
	tree, ok := mapAreaTree[id]
	//xlog.Debugf("get area tree For %d , out : %s", id, xutils.JSONString(tree))

	if ok {
		return tree, nil
	} else {
		return nil, errors.New("not found")
	}
}

func (d *IDao) GetAreaByID(id int64) (*Area, error) {
	if len(mapAreaTree) == 0 {
		//插入成功，强制更新缓存
		_ = d.InitAreaTree()
	}

	one, ok := mapAreaTree[id]
	//xlog.Debugf("get area tree For %d , out : %s", id, xutils.JSONString(tree))
	if ok {
		one.ChildrenNum = len(one.Children)
		// for _, son := range one.Children {
		// 	son.Children = nil //只显示子级，不显示孙级
		// }
		return one, nil
	} else {
		return nil, errors.New("not found")
	}
}

func (d *IDao) GetAreaByCode(code string) (*Area, error) {
	var one Area
	err := d.db.Model(&Area{}).Where("area_cdeo =?", code).First(&one).Error
	if err != nil {
		return nil, err
	}

	return &one, err
}

func (d *IDao) GetAreaListByLevel(level int) ([]*Area, error) {
	areas := make([]*Area, 0)
	err := d.db.Model(&Area{}).Where("level=?", level).Find(&areas).Order("id ASC").Error
	if err != nil {
		return nil, err
	}
	if len(mapAreaTree) == 0 {
		//插入成功，强制更新缓存
		_ = d.InitAreaTree()
	}

	for _, one := range areas {
		parent := mapAreaTree[one.ID]
		one.ChildrenNum = len(parent.Children)
	}
	return areas, nil
}

// 手动添加下一级（有时候为了实际需求，要手动添加）
func (d *IDao) AddSubArea(one *Area) error {
	//查询parent_id 是否合法
	parent := Area{}
	err := d.db.Model(&Area{}).Where("id=?", one.ParentID).First(&parent).Error
	if err != nil {
		return err
	}

	//插入
	err = d.db.Create(one).Error
	if err != nil {
		return err
	}

	//插入成功，强制更新缓存
	_ = d.InitAreaTree()
	return nil
}
