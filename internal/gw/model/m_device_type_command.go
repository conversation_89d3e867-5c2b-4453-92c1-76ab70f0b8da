package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 命令
type Command struct {
	BaseModelCreateUpdate

	DeviceTypeID string `gorm:"index;comment:设备类型ID"      json:"device_type_id"` // 设备类型ID

	CommandID  string `gorm:"unique;comment:命令id"      json:"command_id"`
	Name       string `gorm:"comment:命令名称"           json:"name"`
	Identifier string `gorm:"comment:命令标识"            json:"identifier"`
	Desc       string `gorm:"comment:描述"               json:"desc"`

	SendParams         datatypes.JSONSlice[dto.Param] `gorm:"comment:命令参数"          json:"send_params"`
	SendParamsDefault  datatypes.JSONMap              `gorm:"comment:命令参数默认值"     json:"send_params_default"`
	ReplyParams        datatypes.JSONSlice[dto.Param] `gorm:"comment:命令回复参数"       json:"reply_params"`
	ReplyParamsDefault datatypes.JSONMap              `gorm:"comment:命令回复参数默认值"  json:"reply_params_default"`
}

type CommandRepo struct {
	db    *gorm.DB
	cache *Cache[[]*Command] // 命令数组缓存
}

func NewCommandRepo() *CommandRepo {
	return &CommandRepo{
		db:    global.DB(),
		cache: NewCache[[]*Command](), // 初始化命令数组缓存，默认10分钟过期
	}
}

type CommandFilter struct {
	// 添加过滤字段
	Name         string
	Identifier   string
	CommandID    string
	DeviceTypeID string
}

func (p CommandRepo) fmtFilter(f CommandFilter) *gorm.DB {
	db := p.db
	// 添加条件
	if f.Name != "" {
		db = db.Where(Command{Name: f.Name})
	}
	if f.Identifier != "" {
		db = db.Where(Command{Identifier: f.Identifier})
	}
	if f.CommandID != "" {
		db = db.Where(Command{CommandID: f.CommandID})
	}
	if f.DeviceTypeID != "" {
		db = db.Where(Command{DeviceTypeID: f.DeviceTypeID})
	}
	return db
}

func (p CommandRepo) Insert(data *Command) error {
	err := p.db.Create(data).Error
	// 插入后清理相关缓存
	if err == nil && data.DeviceTypeID != "" {
		p.CacheDelete(data.DeviceTypeID)
	}
	return err
}

func (p CommandRepo) FindOneByFilter(f CommandFilter) (*Command, error) {
	var result Command
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (p CommandRepo) FindByFilter(f CommandFilter, page *dto.PageInfo) ([]*Command, error) {
	var results []*Command
	db := p.fmtFilter(f).Model(&Command{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (p CommandRepo) CountByFilter(f CommandFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&Command{})
	err = db.Count(&size).Error
	return size, err
}

func (p CommandRepo) Update(data *Command) error {
	// 先查询要删除的命令信息，获取DeviceTypeID用于清理缓存
	var cmd Command
	if err := p.db.Where(Command{CommandID: data.CommandID}).First(&cmd).Error; err != nil {
		// 命令不存在
		return err
	}

	err := p.db.Where(Command{CommandID: data.CommandID}).Updates(data).Error
	// 更新后清理相关缓存
	if err == nil && cmd.DeviceTypeID != "" {
		p.CacheDelete(cmd.DeviceTypeID)
	}
	return err
}

func (p CommandRepo) DeleteByDeviceTypeID(deviceTypeID string) error {
	db := p.fmtFilter(CommandFilter{DeviceTypeID: deviceTypeID})
	err := db.Delete(&Command{}).Error
	// 删除后清理相关缓存
	if err == nil && deviceTypeID != "" {
		p.CacheDelete(deviceTypeID)
	}
	return err
}
func (p CommandRepo) DeleteByFilter(f CommandFilter) error {
	db := p.fmtFilter(f)
	err := db.Delete(&Command{}).Error
	return err
}

func (p CommandRepo) Delete(commandID string) error {
	// 先查询要删除的命令信息，获取DeviceTypeID用于清理缓存
	var cmd Command
	if err := p.db.Where(Command{CommandID: commandID}).First(&cmd).Error; err != nil {
		// 命令不存在
		return err
	}

	// 如果查询成功，执行删除操作
	err := p.db.Where(Command{CommandID: commandID}).Delete(&Command{}).Error
	// 删除成功后清理相关缓存
	if err == nil && cmd.DeviceTypeID != "" {
		p.CacheDelete(cmd.DeviceTypeID)
	}
	return err
}
func (p CommandRepo) FindOne(id string) (*Command, error) {
	var result Command
	err := p.db.Where(Command{CommandID: id}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p CommandRepo) MultiInsert(data []*Command) error {
	err := p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&Command{}).Create(data).Error
	// 批量插入后，清理相关的缓存
	if err == nil {
		p.invalidateCacheByCommands(data)
	}
	return err
}

// ================== 缓存操作方法 ===========================

// getCacheKey 生成缓存键
func (p CommandRepo) getCacheKey(deviceTypeID string) string {
	return "commands:" + deviceTypeID
}

// CacheGet 从缓存获取命令数组
func (p *CommandRepo) CacheGet(deviceTypeID string) ([]*Command, bool) {
	return p.cache.Get(p.getCacheKey(deviceTypeID))
}

// CacheSet 设置命令数组到缓存
func (p *CommandRepo) CacheSet(deviceTypeID string, commands []*Command) {
	p.cache.Set(p.getCacheKey(deviceTypeID), commands)
}

// CacheDelete 删除指定设备类型的命令缓存
func (p *CommandRepo) CacheDelete(deviceTypeID string) {
	p.cache.Delete(p.getCacheKey(deviceTypeID))
}

// FindByDeviceTypeIDWithCache 带缓存的设备类型命令查询
// 优先从缓存获取，缓存未命中时从数据库查询并缓存结果
func (p *CommandRepo) FindByDeviceTypeIDWithCache(deviceTypeID string) ([]*Command, error) {
	// 先尝试从缓存获取
	if commands, found := p.CacheGet(deviceTypeID); found {
		return commands, nil
	}

	// 缓存未命中，从数据库查询
	var results []*Command
	db := p.fmtFilter(CommandFilter{DeviceTypeID: deviceTypeID}).Model(&Command{})
	err := db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 查询成功后缓存结果
	p.CacheSet(deviceTypeID, results)

	return results, nil
}

// invalidateCacheByCommands 根据命令列表清理相关缓存
func (p *CommandRepo) invalidateCacheByCommands(commands []*Command) {
	deviceTypeIDs := make(map[string]bool)
	for _, cmd := range commands {
		if cmd.DeviceTypeID != "" {
			deviceTypeIDs[cmd.DeviceTypeID] = true
		}
	}

	// 清理所有相关的设备类型缓存
	for deviceTypeID := range deviceTypeIDs {
		p.CacheDelete(deviceTypeID)
	}
}
