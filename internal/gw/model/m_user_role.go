package model

import (
	"fmt"

	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"bs.com/app/internal/gw/dto"
)

// 角色：更新仅能更新描述，名字应该唯一
type Role struct {
	BaseModelCreateUpdate

	Title   string `gorm:"index;comment:角色名字" json:"title"`    //角色名字，中文备注
	Status  int    `gorm:"default:2;comment:状态" json:"status"` //状态,1正常，2禁用
	SortNum int    `gorm:"index;comment:显示排序" json:"sort_num"` //显示排序
	Desc    string `gorm:"comment:描述" json:"desc"`             //角色描述
	// 仅角色创建者可以修改角色
	CreatedBy int64 `gorm:"index;comment:创建者公司ID" json:"created_by"` //创建角色的公司ID。

	MenuList []int64 `gorm:"-" json:"menu_list,omitempty"` //映射的资源列表
	// CompanyList []*Company `gorm:"-" json:"-"`
}

func (r *Role) String() string {
	return fmt.Sprintf("%d:%s", r.ID, r.Title)
}

// 修改一个角色的权限，会影响使用这个角色的所有子公司的角色，这 tm 要循环、遍历删除
// 角色还是有一个树啊。但是对于取子集的角色创建来说，父级角色的修改本来就是非常恶心的事情。
//TODO：关联的公司列表。如果下放了角色管理权限，需要解决如下问题。
/*
•	角色1、角色2 是公司级别的主角色（菜单权限完整）
•	管理员创建的新角色 new_role，是角色1 + 角色2 权限的子集
•	超级管理员更新角色1 / 角色2 的菜单权限后：
•	new_role 自动失去对应被撤销的权限（因为它不能有比父角色多的权限）

这样的权限继承（ new_role ⊂ (role1 ∪ role2)， 新的角色是父级角色的子集，不是完全继承，无法使用 casbin 来管理）
当父级角色（role1、role2）权限变动，需要自动更新子角色 new_role 的权限
*/
func (d *IDao) CheckResourceDiff(role_id int64, resource_list []int64) error {
	com_ids, err := d.GetCompanyByRoleID(role_id)
	if err != nil {
		return err
	}

	//查找使用此角色的子公司
	if len(com_ids) == 0 {
		//当没有子级公司使用这个角色，则返回
		return nil
	}

	//一个公司一个公司的处理
	for _, one_com_id := range com_ids {
		//查看此公司的所有角色：因为支持多角色，所以要确认下，修改了某个角色的菜单，对公司整体权限的影响
		roles, err := d.GetRolesByCompanyID(one_com_id)
		if err != nil {
			return err
		}

		var dstRoleArr []int64 //可能需要被修改的子角色列表

		var oldArr, newArr []int64
		//遍历角色列表，获取更新此角色前后，对某个子公司的权限的影响
		for _, one_role := range roles {
			if one_com_id != one_role.CreatedBy {
				//查询、聚合被赋予的某个角色的资源列表
				tmp, err := d.GetMenuIdsByRoleID(one_role.ID) // d.GetResourceIdsByRoleID(one_role.ID)
				if err != nil {
					return err
				}
				oldArr = append(oldArr, tmp...)

				if one_role.ID == role_id {
					newArr = append(newArr, resource_list...)
				} else {
					newArr = append(newArr, tmp...)
				}

			} else {
				//可能影响到的自己创建的角色
				dstRoleArr = append(dstRoleArr, one_role.ID)
			}
		}

		//为更新后的资源列表新表建一个 map，方便检查
		newMap := make(map[int64]uint8, 0)
		for _, res_id := range newArr {
			newMap[res_id] = 1
		}

		//遍历旧表，列出被移除的资源 ID
		diffMap := make(map[int64]uint8, 0) //差异资源去重
		for _, oldone := range oldArr {
			if _, ok := newMap[oldone]; !ok {
				diffMap[oldone] = 1
			}
		}

		//此次修改没有影响此公司的权限资源列表
		if len(diffMap) == 0 {
			continue //处理下一个子公司
		}
		xlog.Infof("one_com_id : %d, diff:%s", one_com_id, xutils.JSONString(diffMap))

		//需要为此公司以及所有子公司去掉某个菜单权限
		//获取所有此公司的所有子公司的角色列表
		myroles, err := d.GetRoleIdsBySubCompany(one_com_id)
		if err != nil {
			xlog.Warn("get roleIds by sub company: not found")
		}

		//所有可能被修改的角色
		myroles = append(myroles, dstRoleArr...)

		//批量删除角色、资源映射（优化性能）
		if len(myroles) > 0 && len(diffMap) > 0 {
			// 去重角色ID
			uniqueRoleIDs := make([]int64, 0)
			ridMap := make(map[int64]bool)
			for _, rid := range myroles {
				if !ridMap[rid] {
					ridMap[rid] = true
					uniqueRoleIDs = append(uniqueRoleIDs, rid)
				}
			}

			// 提取被撤销的菜单ID
			revokedMenuIDs := make([]int64, 0, len(diffMap))
			for menuID := range diffMap {
				revokedMenuIDs = append(revokedMenuIDs, menuID)
			}

			// 批量删除：一次SQL删除所有相关的角色-菜单映射
			err = d.BatchDelRoleMenuMap(uniqueRoleIDs, revokedMenuIDs)
			if err != nil {
				xlog.Errorf("批量删除角色菜单映射失败, company_id: %d, roles: %v, menus: %v, err: %v",
					one_com_id, uniqueRoleIDs, revokedMenuIDs, err)
				return err
			} else {
				xlog.Infof("批量删除角色菜单映射成功, company_id: %d, 影响角色数: %d, 撤销菜单数: %d",
					one_com_id, len(uniqueRoleIDs), len(revokedMenuIDs))
			}
		}

		//处理下一个公司
	}

	return nil
}

// 添加新的角色、更新角色
func (d *IDao) AddUpdateRole(company_id int64, req *dto.ReqRole) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		//添加或者更新 role
		one := &Role{
			Title:   req.Title,
			Status:  req.Status,
			SortNum: req.SortNum,
			Desc:    req.Desc,
		}

		var rid int64
		var err error

		if req.ID == 0 {
			//添加新角色
			one.CreatedBy = company_id
			err = tx.Create(one).Error
			if err != nil {
				return err
			}
			rid = one.ID
		} else {
			//事务里面检查：是否去掉了子级公司的一些菜单，如果影响到了，就遍历更新所有子级角色，为所有子级角色解除与那些菜单的绑定
			err = d.CheckResourceDiff(req.ID, req.MenuList)
			if err != nil {
				xlog.Error("check resourece diff when do role update err:", err)
				return err
			}

			//更新
			m := bean.StructToMap(req)
			delete(m, "menu_list")
			delete(m, "id")
			if err = tx.Model(&Role{}).Where("id = ?", req.ID).Updates(m).Error; err != nil {
				return err
			}
			rid = req.ID

			//更新的话，需要先删掉旧的 role-resource-map
			if err = d.DelRoleMenuMap(rid, 0); err != nil { // d.DelRoleResourceMap(rid, 0); err != nil {
				return err
			}
		}

		//添加 role-resource-map
		if len(req.MenuList) > 0 {
			return d.AddRoleMenuMap(req.ID, req.MenuList) //  d.AddRoleResourceMap(rid, req.ResourceList)
		} else {
			return nil
		}

	})
}

func (d *IDao) GetRoleByID(id int64) (*Role, error) {
	var one Role
	err := d.db.Model(&Role{}).Where("id = ?", id).First(&one).Error
	return &one, err
}

// 删除role
// 删除所有role-user 映射， role-resource 映射
func (d *IDao) DelRole(id int64) error {
	return d.db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id = ? ", id).Delete(&Role{}).Error; err != nil {
			return err
		}

		//delete role-user-map
		if err := d.DelRoleUserMap(0, id); err != nil {
			return err
		}

		//dekete role-resource-map
		// if err := d.DelRoleResourceMap(id, 0); err != nil {
		// 	return err
		// }
		if err := d.DelRoleMenuMap(id, 0); err != nil {
			return err
		}
		return nil
	})
}

// 查询自己所在公司所属角色、创建的角色，仅可以修改自己创建的角色
// 支持 title 的模糊搜索
func (d *IDao) QueryRole(company_id int64, title string) ([]*Role, error) {
	var ids []int64
	err := d.db.Model(&CompanyRoleMap{}).Where("company_id = ?", company_id).Select("role_id").Find(&ids).Error
	if err != nil {
		return nil, err
	}

	var ids2 []int64
	err = d.db.Model(&Role{}).Where("created_by = ?", company_id).Select("id").Find(&ids2).Error
	if err != nil {
		return nil, err
	}

	// 自己创建的和自己关联的角色理论上不应该有重复，不过这里不去重也没关系
	ids = append(ids, ids2...)

	var arr []*Role
	if title == "" {
		err = d.db.Model(&Role{}).Where("id in ?", ids).Find(&arr).Order("sort_num ASC").Error
	} else {
		err = d.db.Model(&Role{}).Where("id in ?", ids).Where("title like ?", "%"+title+"%").Find(&arr).Order("sort_num ASC").Error
	}

	if err != nil {
		return nil, err
	}

	return arr, nil
}

// -----------------------------------------------------------------------------
// 用户角色映射
// idx_rum_uid_rid 联合唯一索引
type RoleUserMap struct {
	BaseModelCreate

	UID    int64 `gorm:"uniqueIndex:idx_rum_uid_rid;comment:用户ID" json:"uid"`
	RoleID int64 `gorm:"uniqueIndex:idx_rum_uid_rid;comment:角色ID" json:"role_id"`
}

func (d *IDao) AddRoleUserMap(uid int64, roles []int64) error {
	var arr []RoleUserMap

	for _, rid := range roles {
		arr = append(arr, RoleUserMap{
			UID:    uid,
			RoleID: rid,
		})
	}
	//如果已经存在，则啥也不干，否则插入
	return d.db.Model(&RoleUserMap{}).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "uid"}, {Name: "role_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"id"}),
	}).Create(&arr).Error
}

func (d *IDao) DelRoleUserMap(uid, rid int64) error {
	if rid == 0 {
		//删除用户时
		return d.db.Where("uid = ?", uid).Delete(&RoleUserMap{}).Error
	} else if uid == 0 {
		//删除角色时
		return d.db.Where("role_id = ?", rid).Delete(&RoleUserMap{}).Error
	} else {
		return d.db.Where("uid = ? and role_id = ?", uid, rid).Delete(&RoleUserMap{}).Error
	}
}

// 返回某人所属分组
func (d *IDao) GetRolesByUID(uid int64) ([]*Role, error) {
	var idsArr []int64
	err := d.db.Model(&RoleUserMap{}).Where("uid = ?", uid).Select("role_id").Find(&idsArr).Error
	if err != nil {
		return nil, err
	}

	var arr []*Role
	err = d.db.Model(&Role{}).Where("id in ?", idsArr).Find(&arr).Error

	return arr, err
}

// func (d *IDao) GetResourceByUID(uid int64) ([]*Resource, error) {
// 	// 1. 获取用户的所有角色
// 	roles, err := d.GetRolesByUID(uid)
// 	if err != nil {
// 		return nil, err
// 	}

// 	// 2. 获取所有角色对应的资源ID
// 	var roleIds []int64
// 	for _, role := range roles {
// 		roleIds = append(roleIds, role.ID)
// 	}

// 	return d.GetResourcesByRoles(roleIds)
// }
