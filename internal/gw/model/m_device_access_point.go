package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// 管理接入点
// 接入点负责作为设备和平台之间的桥梁。包括了传输协议、数据格式、数据解析相关参数等。
// 支持多个，每一个数据流在服务器是有固定的配置信息，比如 IP、端口等。
// 界面上会用下拉列表列出所有可用接入服务点。
// 每一个接入点，是一个独立的进程，一般需要定义一些静态信息，和一些动态信息。
// 接入点管理：服务器的地址和端口。服务器要记录每一个接入点的信息，和状态 （当启动一个接入点的时候，必去先去服务器注册自己的信息）。
// 同样的连接协议和消息协议的接入点，可以有多个。
// 对于 mqtt，也可以包括所有 topic 的规范。
type AccessPoint struct {
	BaseModelCreateUpdate
	AccessPointID   string            `gorm:"unique;comment:接入点ID"        json:"access_point_id"`   // 唯一标识
	AccessPointCode string            `gorm:"unique;comment:接入点code"      json:"access_point_code"` // mqtt 接入点鉴权 Code
	CompanyID       int64             `gorm:"comment:公司ID"     json:"company_id"`                   // 公司ID
	Name            string            `gorm:"comment:接入点名称"  json:"name"`
	ConnProtocol    string            `gorm:"comment:接入协议"   json:"conn_protocol"` // 接入协议.mqtt, tcp, http, 其他等
	MsgProtocol     string            `gorm:"comment:消息协议"   json:"msg_protocol"`  // 消息上传或下发使用何种协议
	Host            string            `gorm:"comment:host"      json:"host"`
	Port            uint16            `gorm:"comment:port"      json:"port"`
	ExtendInfo      datatypes.JSONMap `gorm:"comment:扩展信息"    json:"extend_info"` //其他配置，如用户名和密码。
	Status          int32             `gorm:"default:1;comment:状态" json:"status"` // 状态 1:正常, -1:禁用
}

// //////// report //////////
type AccessPointRepo struct {
	db *gorm.DB
}

func NewAccessPointRepo() *AccessPointRepo {
	return &AccessPointRepo{db: global.DB()}
}

type AccessPointFilter struct {
	// 添加过滤字段
	AccessPointID string
	Name          string
	ConnProtocol  string
	MsgProtocol   string
	CompanyID     int64
	Status        int32
}

func (p AccessPointRepo) fmtFilter(f AccessPointFilter) *gorm.DB {
	db := p.db

	if f.CompanyID > 0 {
		db = db.Where("company_id = ? OR company_id = 0", f.CompanyID)
	} else {
		db = db.Where(AccessPoint{CompanyID: 0}) // 默认公司为0的，都可以访问
	}

	if f.AccessPointID != "" {
		db = db.Where(AccessPoint{AccessPointID: f.AccessPointID})
	}
	if f.Name != "" {
		db = db.Where(AccessPoint{Name: f.Name})
	}
	if f.ConnProtocol != "" {
		db = db.Where(AccessPoint{ConnProtocol: f.ConnProtocol})
	}

	if f.MsgProtocol != "" {
		db = db.Where(AccessPoint{MsgProtocol: f.MsgProtocol})
	}

	if f.Status != 0 {
		db = db.Where(AccessPoint{Status: f.Status})
	}
	return db
}

func (p AccessPointRepo) Insert(data *AccessPoint) error {
	data.AccessPointCode = xutils.RandStringN(32)
	return p.db.Create(data).Error
}

func (p AccessPointRepo) FindOneByFilter(f AccessPointFilter) (*AccessPoint, error) {
	var result AccessPoint
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p AccessPointRepo) FindByFilter(f AccessPointFilter, page *dto.PageInfo) ([]*AccessPoint, error) {
	var results []*AccessPoint
	db := p.fmtFilter(f).Model(&AccessPoint{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (p AccessPointRepo) CountByFilter(f AccessPointFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&AccessPoint{})
	err = db.Count(&size).Error
	return size, err
}

func (p AccessPointRepo) Update(data *AccessPoint) error {
	err := p.db.Where(AccessPoint{AccessPointID: data.AccessPointID}).Updates(data).Error
	return err
}

func (p AccessPointRepo) Delete(accessPointID string) error {
	err := p.db.Where(AccessPoint{AccessPointID: accessPointID}).Delete(&AccessPoint{}).Error
	return err
}

func (p AccessPointRepo) FindOne(accessPointID string) (*AccessPoint, error) {
	var result AccessPoint
	err := p.db.Where(AccessPoint{AccessPointID: accessPointID}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p AccessPointRepo) GenConnectInfo(dev *Device, accessPointIDs []string) []*dto.ConnectInfoResp {
	connInfoList := []*dto.ConnectInfoResp{}
	if dev == nil {
		return connInfoList
	}
	for _, accPoint := range accessPointIDs {
		accPointInfo, err := p.FindOne(accPoint)
		if err != nil {
			xlog.Error("find access point info error:", err)
			continue
		}
		if accPointInfo.ConnProtocol == ConnProtocolMQTT {
			clientID, username, password := bean.GenMqttAuthInfo(dev.DeviceTypeID, dev.DeviceID, dev.DeviceCode)
			connInfo := dto.ConnectInfoResp{
				ConnProtocol: accPointInfo.ConnProtocol,
				MsgProtocol:  accPointInfo.MsgProtocol,
				Host:         accPointInfo.Host,
				Port:         accPointInfo.Port,
				ExtendInfo: dto.MQTTConnectInfo{
					Broker:   accPointInfo.Host,
					ClientID: clientID,
					Username: username,
					Password: password,
				},
			}
			connInfoList = append(connInfoList, &connInfo)
		}
		if accPointInfo.ConnProtocol == ConnProtocolTCP {
			connInfo := dto.ConnectInfoResp{
				ConnProtocol: accPointInfo.ConnProtocol,
				MsgProtocol:  accPointInfo.MsgProtocol,
				Host:         accPointInfo.Host,
				Port:         accPointInfo.Port,
				ExtendInfo:   datatypes.JSONMap{},
			}
			connInfoList = append(connInfoList, &connInfo)
		}
	}
	return connInfoList
}
