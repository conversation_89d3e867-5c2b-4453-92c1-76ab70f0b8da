package model

import (
	"os"
	"strings"

	"github.com/lionsoul2014/ip2region/binding/golang/xdb"

	"bs.com/app/pkg/xlog"
)

type Tool struct {
	searcher *xdb.Searcher //可全局单例
}

const dbPath = "./deploy/ip2region.xdb"

var toolInstance *Tool

func NewToolInstance() *Tool {
	// 1、从 dbPath 加载整个 xdb 到内存
	cBuff, err := xdb.LoadContentFromFile(dbPath)
	if err != nil {
		xlog.Errorf("ip2region: failed to load content from `%s`: %s", dbPath, err)
		os.Exit(1)
	}

	// 2、用全局的 cBuff 创建完全基于内存的查询对象。
	searcher, err := xdb.NewWithBuffer(cBuff)
	if err != nil {
		xlog.Errorf("ip2region: failed to create searcher with content: %s\n", err)
		os.Exit(1)
	}

	// 备注：并发使用，用整个 xdb 缓存创建的 searcher 对象可以安全用于并发。
	toolInstance = &Tool{
		searcher: searcher,
	}
	return toolInstance
}

func ParseReginByIP(ip string) string {
	if ip == "" {
		return "本地环境"
	}
	region, err := toolInstance.searcher.SearchByStr(ip)
	if err != nil {
		xlog.Error("failed to search ip region", "ip", ip, "err", err)
		return "未知 IP"
	}
	xlog.Debug("login regin", "ip", ip, "addr", region)

	return parseRegion(region)
}

// 每条ip数据段都固定了格式：
// _城市Id|国家|区域|省份|城市|ISP_
// 只有中国的数据精确到了城市，其他国家有部分数据只能定位到国家，后前的选项全部是0
func parseRegion(region string) string {
	//curl  http://localhost:10090/ip2addr\?ip\=***************
	//DEBUG  [2022-12-23 12:48:06] ip  :**************
	//DEBUG  [2022-12-23 12:48:06] addr:爱沙尼亚|0|0|0|0
	//DEBUG  [2022-12-23 12:48:33] ip  :*************
	//DEBUG  [2022-12-23 12:48:33] addr:中国|0|广东省|深圳市|阿里云
	//DEBUG  [2022-12-23 12:49:24] ip  :***************
	//DEBUG  [2022-12-23 12:49:24] addr:中国|0|广东省|深圳市|电信

	arr := strings.Split(region, "|")
	if len(arr) != 5 {
		return region
	}
	nation := arr[0]
	province := arr[2]
	city := arr[3]
	isp := arr[4]

	retAddr := city
	if city == "0" {
		retAddr = province
		if province == "0" {
			retAddr = nation
		}
	}

	if isp != "0" {
		retAddr += "." + isp
	}

	return retAddr
}
