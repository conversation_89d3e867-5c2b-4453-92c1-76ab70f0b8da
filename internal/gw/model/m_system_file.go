package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 文件模型
type File struct {
	BaseModelCreate
	FileID    string `gorm:"uniqueIndex;comment:文件ID" json:"file_id"`
	Name      string `gorm:"comment:文件名" json:"name"`
	Size      int64  `gorm:"comment:文件大小" json:"size"`
	LocalPath string `gorm:"type:varchar(128);comment:文件存储路径" json:"local_path"`
	HttpPath  string `gorm:"type:varchar(255);comment:OSS存储路径" json:"http_path"`
	MD5       string `gorm:"comment:文件签名" json:"md5"`
	CRC16     uint16 `gorm:"comment:文件CRC校验" json:"crc16"`
	MimeType  string `gorm:"type:varchar(64);comment:文件类型" json:"mime_type"`
	Desc      string `gorm:"type:varchar(255);comment:描述" json:"desc"`
}
type FileRepo struct {
	db *gorm.DB
}

func NewFileRepo() *FileRepo {
	return &FileRepo{
		db: global.DB(),
	}
}

type FileFilter struct {
	FileID string
}

// 格式化文件过滤条件
func (p FileRepo) fmtFilter(f FileFilter) *gorm.DB {
	db := p.db

	// 添加条件
	if f.FileID != "" {
		db = db.Where(File{FileID: f.FileID})
	}
	return db
}

// 插入文件
func (p FileRepo) Insert(data *File) error {
	return p.db.Create(data).Error
}

// 批量插入文件
func (p FileRepo) MultiInsert(data []*File) error {
	return p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&File{}).Create(data).Error
}

// 根据过滤条件查找单个文件
func (p FileRepo) FindOneByFilter(f FileFilter) (*File, error) {
	var result File
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据ID查找文件
func (p FileRepo) FindOne(fileID string) (*File, error) {
	var result File
	err := p.db.Where(File{FileID: fileID}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据MD5查找文件，用于查重
func (p FileRepo) FindByMD5(md5 string) (*File, error) {
	var result File
	err := p.db.Where(File{MD5: md5}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据过滤条件查找多个文件
func (p FileRepo) FindByFilter(f FileFilter, page *dto.PageInfo) ([]*File, error) {
	var results []*File
	db := p.fmtFilter(f).Model(&File{})
	var err error

	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}

	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// 根据过滤条件统计文件数量
func (p FileRepo) CountByFilter(f FileFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&File{})
	err = db.Count(&size).Error
	return size, err
}

// 更新文件
func (p FileRepo) Update(data *File) error {
	return p.db.Where(File{FileID: data.FileID}).Updates(data).Error
}

// 根据过滤条件删除文件
func (p FileRepo) DeleteByFilter(f FileFilter) error {
	db := p.fmtFilter(f)
	return db.Delete(&File{}).Error
}

// 根据ID删除文件
func (p FileRepo) Delete(fileID string) error {
	return p.db.Where(File{FileID: fileID}).Delete(&File{}).Error
}
