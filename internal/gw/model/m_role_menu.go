package model

import "gorm.io/gorm/clause"

// -----------------------------------------------------------------------------
// 角色资源映射
type RoleMenuMap struct {
	BaseModelCreate

	RoleID int64 `gorm:"uniqueIndex:idx_rrsmap_rid_resid;comment:角色ID" json:"role_id"`
	MenuID int64 `gorm:"uniqueIndex:idx_rrsmap_rid_resid;comment:资源ID" json:"menu_id"`
}

func (d *IDao) AddRoleMenuMap(role_id int64, menu_ids []int64) error {
	var arr []RoleMenuMap

	for _, res_id := range menu_ids {
		arr = append(arr, RoleMenuMap{
			MenuID: res_id,
			RoleID: role_id,
		})
	}
	//如果已经存在，则啥也不干，否则插入
	return d.db.Model(&RoleMenuMap{}).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "role_id"}, {Name: "menu_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"id"}),
		DoNothing: true, // 冲突时什么都不做
	}).Create(&arr).Error
}

func (d *IDao) DelRoleMenuMap(role_id, menu_id int64) error {
	if role_id == 0 {
		return d.db.Where(RoleMenuMap{MenuID: menu_id}).Delete(&RoleMenuMap{}).Error
	} else if menu_id == 0 {
		return d.db.Where(RoleMenuMap{RoleID: role_id}).Delete(&RoleMenuMap{}).Error
	} else {
		return d.db.Where(RoleMenuMap{RoleID: role_id, MenuID: menu_id}).Delete(&RoleMenuMap{}).Error
	}
}

// BatchDelRoleMenuMap 批量删除角色菜单映射关系
// 删除指定角色列表和菜单列表的所有组合映射关系
func (d *IDao) BatchDelRoleMenuMap(roleIDs []int64, menuIDs []int64) error {
	if len(roleIDs) == 0 || len(menuIDs) == 0 {
		return nil // 没有数据需要删除
	}

	// 使用 IN 查询批量删除：WHERE role_id IN (...) AND menu_id IN (...)
	return d.db.Where("role_id IN ? AND menu_id IN ?", roleIDs, menuIDs).Delete(&RoleMenuMap{}).Error
}

func (d *IDao) GetMenuIdsByRoleID(role_id int64) ([]int64, error) {
	var idsArr []int64
	err := d.db.Model(&RoleMenuMap{}).Where(RoleMenuMap{RoleID: role_id}).Select("menu_id").Find(&idsArr).Error
	if err != nil {
		return nil, err
	}
	return idsArr, nil
}

// func (d *IDao) GetResourceByRoleID(role_id int64) ([]*Resource, error) {
// 	//查询 resource ID
// 	idsArr, err := d.GetResourceIdsByRoleID(role_id)
// 	if err != nil {
// 		return nil, err
// 	}

// 	return d.ListResource(idsArr)
// }
