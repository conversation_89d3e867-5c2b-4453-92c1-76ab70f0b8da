## 介绍

定义了 models和controller 。一共有下面这些表：

## 事物

- 如果需要事物，操作如下：
  - 使用相同的tx 初始化 dao 
  - 在第一个dao操作前调用gorm的Begin()方法，在最后一个dao操作成功后调用Commit()方法。
  - 若保存出现异常，需要在每个dao操作后做下判断，若失败使用Rollback()做回退处理。


## gorm 中零值的问题

如果在 golang struct 中定义为 string，则在保存时，不指定值，会默认存入空字符串。正常情况下并没有什么影响。
但是如果恰好这个字段上建了唯一索引。那么非 Null 值都必须保证唯一性。就会造成运行时异常了。

（但是唯一索引最好不要用 null，从性能和需求逻辑上来说，都不合理。具体可以参考这里：https://juejin.cn/post/7146193524889747486）

方案：使用指针。



## 字典表 2023-01-16

- 字典、枚举值是不可修改的，因为字典、枚举通常会和具体代码实现紧密耦合。修改可能导致系统无法正常工作。
- 字典表如果被频繁访问，可以将数据放入缓存中。



## product 和 deviceType

- product 侧重硬件设备，比如 dtu、rtu，是一类硬件设备的抽象。具体每一个硬件是 hardware， 平台为 hardware 提供了 参数设置、固件升级等功能。一般是我们自己的硬件产品。
- deviceType 侧重业务需求抽象，基于物模型，仅关心业务需求。可以是其他公司的硬件设备。

一个 product 可以通过抽象定义，对应多个 deviceType。  

product 基于 mqtt 协议扩展。默认一型一密。鉴权的时候，会使用 productID、productCode、 hardwareID


## 缓存的使用

缓存的实现参考 cache.go 这个文件。
缓存的使用根据实际需求来添加的：
- 比如对于 device 和 deviceType ，是以 deviceID、deviceTypeID 作为 key，缓存 model。
- 对于物模型属性，以 deviceTypeID 作为 key，缓存数组 []model。


