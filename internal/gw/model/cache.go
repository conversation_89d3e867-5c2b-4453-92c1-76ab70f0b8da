package model

import (
	"sync"
	"time"
)

// CacheItem 缓存项，包含数据和过期时间
type CacheItem[T any] struct {
	Data      T         // 缓存的数据
	ExpiresAt time.Time // 过期时间
}

// IsExpired 检查缓存项是否过期
func (item *CacheItem[T]) IsExpired() bool {
	return time.Now().After(item.ExpiresAt)
}

// Cache 泛型缓存结构
type Cache[T any] struct {
	mu    sync.RWMutex             // 读写锁，保证并发安全
	items map[string]*CacheItem[T] // 缓存数据存储
	ttl   time.Duration            // 缓存过期时间
}

// NewCache 创建新的缓存实例
// ttl: 缓存过期时间，默认10分钟
func NewCache[T any](ttl ...time.Duration) *Cache[T] {
	cacheTTL := 10 * time.Minute // 默认10分钟过期
	if len(ttl) > 0 {
		cacheTTL = ttl[0]
	}

	return &Cache[T]{
		items: make(map[string]*CacheItem[T]),
		ttl:   cacheTTL,
	}
}

// Get 获取缓存数据
// key: 缓存键
// 返回: 数据, 是否存在
func (c *Cache[T]) Get(key string) (T, bool) {
	c.mu.RLock()
	item, exists := c.items[key]
	c.mu.RUnlock()

	var zero T
	if !exists {
		return zero, false
	}

	// 检查是否过期，如果过期则删除并返回不存在
	if item.IsExpired() {
		c.mu.Lock()
		delete(c.items, key) // 被动清理过期数据
		c.mu.Unlock()
		return zero, false
	}

	return item.Data, true
}

// Set 设置缓存数据
// key: 缓存键
// value: 要缓存的数据
func (c *Cache[T]) Set(key string, value T) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.items[key] = &CacheItem[T]{
		Data:      value,
		ExpiresAt: time.Now().Add(c.ttl),
	}
}

// Delete 删除指定键的缓存
// key: 要删除的缓存键
func (c *Cache[T]) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	delete(c.items, key)
}

// Clear 清空所有缓存
func (c *Cache[T]) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.items = make(map[string]*CacheItem[T])
}

// Size 获取当前缓存项数量（包括可能过期的项）
func (c *Cache[T]) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()

	return len(c.items)
}

// Keys 获取所有缓存键（不包括过期的）
func (c *Cache[T]) Keys() []string {
	c.mu.RLock()
	defer c.mu.RUnlock()

	now := time.Now()
	keys := make([]string, 0, len(c.items))

	for key, item := range c.items {
		if !now.After(item.ExpiresAt) { // 未过期的键
			keys = append(keys, key)
		}
	}

	return keys
}
