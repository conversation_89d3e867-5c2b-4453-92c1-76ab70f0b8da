package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 事件
type Event struct {
	BaseModelCreateUpdate

	DeviceTypeID string `gorm:"index;comment:设备类型ID"   json:"device_type_id"` // 设备类型ID

	EventID    string                         `gorm:"unique;comment:事件id"        json:"event_id"`
	Name       string                         `gorm:"comment:时间名称"             json:"name"`
	Identifier string                         `gorm:"comment:事件标识"             json:"identifier"`
	Desc       string                         `gorm:"comment:描述"                json:"desc"`
	Params     datatypes.JSONSlice[dto.Param] `gorm:"comment:事件参数"            json:"params"`
}

type EventRepo struct {
	db    *gorm.DB
	cache *Cache[[]*Event] // 事件数组缓存
}

func NewEventRepo() *EventRepo {
	return &EventRepo{
		db:    global.DB(),
		cache: NewCache[[]*Event](), // 初始化事件数组缓存，默认10分钟过期
	}
}

type EventFilter struct {
	// 添加过滤字段
	Name         string
	EventID      string
	Identifier   string
	DeviceTypeID string
}

func (p EventRepo) fmtFilter(f EventFilter) *gorm.DB {
	db := p.db
	// 添加条件
	if f.Name != "" {
		db = db.Where(Event{Name: f.Name})
	}
	if f.Identifier != "" {
		db = db.Where(Event{Identifier: f.Identifier})
	}
	if f.EventID != "" {
		db = db.Where(Event{EventID: f.EventID})
	}
	if f.DeviceTypeID != "" {
		db = db.Where(Event{DeviceTypeID: f.DeviceTypeID})
	}
	return db
}

func (p EventRepo) Insert(data *Event) error {
	err := p.db.Create(data).Error
	// 插入后清理相关缓存
	if err == nil && data.DeviceTypeID != "" {
		p.CacheDelete(data.DeviceTypeID)
	}
	return err
}

func (p EventRepo) FindOneByFilter(f EventFilter) (*Event, error) {
	var result Event
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (p EventRepo) FindByFilter(f EventFilter, page *dto.PageInfo) ([]*Event, error) {
	var results []*Event
	db := p.fmtFilter(f).Model(&Event{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (p EventRepo) CountByFilter(f EventFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&Event{})
	err = db.Count(&size).Error
	return size, err
}

func (p EventRepo) Update(data *Event) error {
	// 先查询要删除的事件信息，获取DeviceTypeID用于清理缓存
	var event Event
	if err := p.db.Where(Event{EventID: data.EventID}).First(&event).Error; err != nil {
		// 事件不存在
		return err
	}
	err := p.db.Where(Event{EventID: data.EventID}).Updates(data).Error
	// 更新后清理相关缓存
	if err == nil && event.DeviceTypeID != "" {
		p.CacheDelete(event.DeviceTypeID)
	}
	return err
}

func (p EventRepo) DeleteByDeviceTypeID(deviceTypeID string) error {
	db := p.fmtFilter(EventFilter{DeviceTypeID: deviceTypeID})
	err := db.Delete(&Event{}).Error
	// 删除后清理相关缓存
	if err == nil && deviceTypeID != "" {
		p.CacheDelete(deviceTypeID)
	}
	return err
}

func (p EventRepo) Delete(eventID string) error {
	// 先查询要删除的事件信息，获取DeviceTypeID用于清理缓存
	var event Event
	if err := p.db.Where(Event{EventID: eventID}).First(&event).Error; err != nil {
		// 事件不存在
		return err
	}

	// 如果查询成功，执行删除操作
	err := p.db.Where(Event{EventID: eventID}).Delete(&Event{}).Error
	// 删除成功后清理相关缓存
	if err == nil && event.DeviceTypeID != "" {
		p.CacheDelete(event.DeviceTypeID)
	}
	return err
}
func (p EventRepo) FindOne(id string) (*Event, error) {
	var result Event
	err := p.db.Where(Event{EventID: id}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p EventRepo) MultiInsert(data []*Event) error {
	err := p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&Event{}).Create(data).Error
	// 批量插入后，清理相关的缓存
	if err == nil {
		p.invalidateCacheByEvents(data)
	}
	return err
}

// ================== 缓存操作方法 ===========================

// getCacheKey 生成缓存键
func (p EventRepo) getCacheKey(deviceTypeID string) string {
	return "events:" + deviceTypeID
}

// CacheGet 从缓存获取事件数组
func (p *EventRepo) CacheGet(deviceTypeID string) ([]*Event, bool) {
	return p.cache.Get(p.getCacheKey(deviceTypeID))
}

// CacheSet 设置事件数组到缓存
func (p *EventRepo) CacheSet(deviceTypeID string, events []*Event) {
	p.cache.Set(p.getCacheKey(deviceTypeID), events)
}

// CacheDelete 删除指定设备类型的事件缓存
func (p *EventRepo) CacheDelete(deviceTypeID string) {
	p.cache.Delete(p.getCacheKey(deviceTypeID))
}

// FindByDeviceTypeIDWithCache 带缓存的设备类型事件查询
// 优先从缓存获取，缓存未命中时从数据库查询并缓存结果
func (p *EventRepo) FindByDeviceTypeIDWithCache(deviceTypeID string) ([]*Event, error) {
	// 先尝试从缓存获取
	if events, found := p.CacheGet(deviceTypeID); found {
		return events, nil
	}

	// 缓存未命中，从数据库查询
	var results []*Event
	db := p.fmtFilter(EventFilter{DeviceTypeID: deviceTypeID}).Model(&Event{})
	err := db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 查询成功后缓存结果
	p.CacheSet(deviceTypeID, results)

	return results, nil
}

// invalidateCacheByEvents 根据事件列表清理相关缓存
func (p *EventRepo) invalidateCacheByEvents(events []*Event) {
	deviceTypeIDs := make(map[string]bool)
	for _, event := range events {
		if event.DeviceTypeID != "" {
			deviceTypeIDs[event.DeviceTypeID] = true
		}
	}

	// 清理所有相关的设备类型缓存
	for deviceTypeID := range deviceTypeIDs {
		p.CacheDelete(deviceTypeID)
	}
}
