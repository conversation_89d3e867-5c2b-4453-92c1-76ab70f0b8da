package model

const RedisPrefix = "shuili.user.session"

/* system config key */
const (
	FrontendKey = "frontend"
)

const (
	Enable  = 1
	Disable = -1
)

const (

	TypeAuthType = 1
	DeviceAuthType  = 2
)

const (
	Online  = 1
	Offline = 2
)

/* redis key */
const (
	OneMinutes     = 50
	TTLTime        = 5 * 60  // 缓存的查询数据 5分钟有效
	DeviceShowTime = 10 * 60 // 缓存的查询数据 5分钟有效
	WaterTTLTime   = 15 * 60
	TenMinTime     = 10 * 60
	DayTTLTime     = 60 * 60 * 24
	MonthTTLTime   = 60 * 60 * 24 * 30

	DistributionKey    = "shuili.api.station.distribution" // 测站分布
	DashBoardYuliang24 = "shuili.api.sample.yuliang24"     // dashboard 24小时雨量
	MapStationKeyName  = "shuili.api.map.station.name"     // 地图测站缓存
	StationMonitorKey  = "shuili.api.dashboard.monitor"    // dashboard

	AdCodeConfigKey      = "shuili.adcode.area"            // 根据adcode获取当前adcode的名字
	UserCodeStationList  = "shuili.user.code.station.list" // 用户的区域下所有测站列表
	UnderCodeStationList = "shuili.code.adcode.list"       // 某个code下面下的所有区域

	SMSPhonePrefix = "shuili.sms.redis.key"       // 验证短信
	SMSLimitPrefix = "shuili.limite60s.redis.key" // 验证短信，1分钟内仅能请求一次

	CurWaterLevelKey    = "shuili.latest.water"      //当前水位
	CurStationImageKey  = "shuili.latest.image"      //最新图像url
	CurStationTakePhoto = "shuili.latest.take.photo" //最近拍照
)

// 设备影子
const (
	RedisKeyOtaProgress  = "shuili.ota.progress"   // 设备升级进度
	RedisKeyDeviceShadow = "shuili.device.shadow1" // 设备影子
)

// 设备分组：正式组、开发组、测试组
const (
	DeviceGroupRelease = "release"
	DeviceGroupTest    = "test"
	DeviceGroupDev     = "dev"
)

// 连接类型
const (
	ConnProtocolMQTT = "mqtt"
	ConnProtocolTCP = "tcp"
)

//设备属性相关常量映射表

// 布尔型，整数型，浮点数型，字符型，枚举型
var mDataType = map[string]string{
	"bool":   "布尔型",
	"int":    "整数型",
	"float":  "浮点型",
	"string": "字符型",
	"enum":   "枚举型",
	"date":   "时间型",
	"raw":    "原始型",
	"number": "数值型",
}

// 控制，采集、运行，故障，配置
var mPropPermission = map[string]string{
	"RO": "只读",
	"RW": "读写",
	"WO": "只写",
}

// 控制，采集、运行，故障，配置
var mPropType = map[string]string{
	"control": "控制类型",  // 一般是业务相关，控制器操作，可写
	"sensor":  "传感类型",  // 一般是业务相关，传感器采集，只读
	"runtime": "运行时类型", // 系统运行环境，常量，不可修改
	"fault":   "故障类型",  // 运行故障
	"config":  "配置类型",  // 设备参数
}

// ======================================================================
// 测站扩展信息：建站的时候，需配置测站的设备制造商和承建商。

// 设备制造商商
// type Manufacturer struct {
// 	Code string `json:"code"` //编号
// 	Name string `json:"name"` //名称
// 	Desc string `json:"desc"` //描述
// }

// var arrManufacturer = []Manufacturer{}
// 暂时仅支持承建商

// 承建商
type Contractor struct {
	Code string `json:"code"` //编号
	Name string `json:"name"` //名称
	Desc string `json:"desc"` //描述
}

// TODO:对于某个公司来说，有承建商、运维人员、业主等不同角色
// 承建商的概念，不应该对应我们平台的 company
// 运维人员会维护一个地区的测站。一个地区的测站，会有多个承建商建造。
// 测站中承建商编码，在导入数据，建站的时候，会导入承建商信息。可以手动修改，手动建站的时候可以选择。

// 所以，承建商信息，是在创建 company 的时候，填写的
func ListContractor() *[]Contractor {
	return &arrContractor
}

// 湖南，承建商信息表
var arrContractor = []Contractor{
	{Code: "01", Name: "小水库承建商"},
	{Code: "02", Name: "中南院"},
	{Code: "04", Name: "中国电信"},
	{Code: "05", Name: "中移集成"},
	{Code: "09", Name: "中国铁塔"},
	{Code: "12", Name: "亿海兰特"},
	{Code: "13", Name: "中国软件"},
	{Code: "14", Name: "国检湖南"},
	{Code: "17", Name: "中国移动"},
	{Code: "18", Name: "中水北方"},
	{Code: "21", Name: "国信华源"},
	{Code: "23", Name: "深圳宏电"},
	{Code: "29", Name: "日恒智能"},
	{Code: "32", Name: "广州远动"},
	{Code: "33", Name: "湖南科宏"},
	{Code: "38", Name: "湖南宏禹"},
	{Code: "40", Name: "广州科弗联"},
	{Code: "41", Name: "新烽光电"},
	{Code: "42", Name: "湘银河"},
	{Code: "43", Name: "威胜信息"},
	{Code: "45", Name: "万江港利"},
	{Code: "46", Name: "航天宏图"},
	{Code: "52", Name: "省水电设计院"},
	{Code: "53", Name: "铁塔智联"},
	{Code: "54", Name: "上海华测"},
	{Code: "55", Name: "合睿达"},
	{Code: "56", Name: "千泓安防"},
	{Code: "57", Name: "长城云网"},
	{Code: "58", Name: "联通数科"},
	{Code: "59", Name: "中水信通"},
	{Code: "60", Name: "禹通水利"},
	{Code: "61", Name: "源涛信息"},
	{Code: "63", Name: "长教智能"},
	{Code: "64", Name: "中电科27所"},
	{Code: "65", Name: "信通建"},
	{Code: "66", Name: "湖南计然"},
	{Code: "68", Name: "中电信"},
	{Code: "69", Name: "中移公司"},
	{Code: "70", Name: "华自科技"},
	{Code: "71", Name: "中国联通"},
	{Code: "74", Name: "中通服"},
	{Code: "75", Name: "鸿和达"},
	{Code: "78", Name: "盛唐科技"},
	{Code: "79", Name: "湘江智慧"},
	{Code: "80", Name: "同方股份"},
	{Code: "81", Name: "博轩智能"},
	{Code: "83", Name: "湖南新途"},
	{Code: "86", Name: "湖南广电"},
}
