package model

import (
	"bytes"
	"encoding/json"
	"reflect"

	"gorm.io/gorm"

	"bs.com/app/global"
	"bs.com/app/pkg/xlog"

	"bs.com/app/internal/gw/dto"
)

// 确认val是否结构体，或者是结构体的指针
func IsStruct(val interface{}) bool {
	//结构体
	if reflect.TypeOf(val).Kind() == reflect.Struct {
		return true
	}

	//结构体指针
	if reflect.TypeOf(val).Kind() == reflect.Ptr {
		if reflect.TypeOf(val).Elem().Kind() == reflect.Struct {
			return true
		}
	}
	return false
}

// StructToMap 将 struct 转换为 map，只包含非零值字段，并使用 json tag 作为 key
// 如果带有 omitempty 且值为零值，则跳过此字段
/*

func StructToMap(data interface{}) (map[string]interface{}, error) {
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return nil, errors.New("data must be a struct or a pointer to a struct")
	}

	result := make(map[string]interface{})
	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldType := t.Field(i)
		fieldName := fieldType.Name

		// 获取 json tag
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag != "" && jsonTag != "-" {
			var omitempty bool
			fieldName, omitempty = parseJSONTag(jsonTag)
			if omitempty && isZero(field) {
				continue
			}
		}

		// 只添加非零值字段到 map
		result[fieldName] = field.Interface()
	}
	return result, nil
}

// isZero 检查字段是否为零值
func isZero(v reflect.Value) bool {
	return v.Interface() == reflect.Zero(v.Type()).Interface()
}

// parseJSONTag 解析 json tag 获取字段名和 omitempty 标志
func parseJSONTag(tag string) (string, bool) {
	parts := strings.Split(tag, ",")
	fieldName := parts[0]
	omitempty := false
	if len(parts) > 1 {
		for _, part := range parts[1:] {
			if part == "omitempty" {
				omitempty = true
				break
			}
		}
	}
	return fieldName, omitempty
}
*/

// 结构体转 map
func StructToMap(val interface{}) (m map[string]interface{}) {
	m = make(map[string]interface{})
	if !IsStruct(val) {
		xlog.Error("invalid struct to map")
		return m
	}

	buf, err := json.Marshal(val)
	if err != nil {
		return
	}

	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	_ = d.Decode(&m)

	delete(m, "created_at")
	delete(m, "updated_at")
	//xlog.Info("struct to map : ", string(buf))
	return
}

// gorm的工具函数
func GetByID(session *gorm.DB, ID int64, obj interface{}) error {
	if session == nil {
		session = global.DB()
	}
	err := session.Where("id=?", ID).First(obj).Error
	return err
}

// 默认 ORDER BY id ASC LIMIT 1
// id 升序
func First(session *gorm.DB, obj interface{}) error {
	if session == nil {
		session = global.DB()
	}
	err := session.First(obj).Error
	return err
}

// 默认 ORDER BY id DESC LIMIT 1
// id 降序
func Last(session *gorm.DB, obj interface{}) error {
	if session == nil {
		session = global.DB()
	}
	err := session.Last(obj).Error
	return err
}

func Find(session *gorm.DB, obj interface{}) error {
	if session == nil {
		session = global.DB()
	}
	err := session.Find(obj).Error
	return err
}

func PagedFind(session *gorm.DB, obj interface{}, pi *dto.PageInfo) (err error) {
	if session == nil {
		session = global.DB()
	}

	if pi == nil || pi.PageSize == 0 {
		pi = &dto.PageInfo{}
		pi.CheckPage()
	}

	page, pageSize := pi.Page, pi.PageSize
	offset := (page - 1) * pageSize

	var count int64
	//查询完成之后，把Offset值设为 -1（-1代表取消offset限制），然后查询总数
	err = session.Offset(offset).Limit(pageSize).Find(obj).Offset(-1).Limit(-1).Count(&count).Error
	pi.Total = count
	return err

	//return session.Offset(offset).Limit(pageSize).Find(obj).Error
}

func PagedOrderFind(session *gorm.DB, obj interface{}, pageIndex, pageCount int, orderBy string) (err error) {
	if session == nil {
		session = global.DB()
	}
	if pageIndex < 1 {
		pageIndex = 1
	}
	if pageCount < 1 {
		pageCount = 10
	}
	offset := (pageIndex - 1) * pageCount
	return session.Order(orderBy).Offset(offset).Limit(pageCount).Find(obj).Error
}

func Count(session *gorm.DB, model interface{}, total *int64) error {
	if session == nil {
		session = global.DB()
	}
	if model != nil {
		session = session.Model(model)
	}
	err := session.Count(total).Error
	return err
}

//func Create(engine *gorm.DB, model interface{}) error {
//	if engine == nil {
//		engine = DB()
//	}
//	return engine.Create(model).Error
//}
