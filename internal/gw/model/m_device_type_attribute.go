package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 属性
type Attribute struct {
	BaseModelCreateUpdate
	DeviceTypeID string `gorm:"index;comment:设备类型ID"   json:"device_type_id"` // 设备类型ID

	AttrID      string            `gorm:"unique;comment:属性id"      json:"attr_id"` // 属性id, 使用字符串而非自增。方便数据迁移
	Name        string            `gorm:"comment:属性名称"           json:"name"`
	Identifier  string            `gorm:"comment:属性标识"           json:"identifier"`
	AttrType    string            `gorm:"comment:属性类型"           json:"attr_type"`    // 属性类型,上报，下发等
	DataType    bean.DataType     `gorm:"comment:数据类型"           json:"data_type"`    // 数据类型
	DataOptions datatypes.JSONMap `gorm:"comment:属性选项"           json:"data_options"` // 属性选项

	Desc string `gorm:"comment:属性描述"           json:"desc"`
}

type AttributeRepo struct {
	db    *gorm.DB
	cache *Cache[[]*Attribute] // 属性数组缓存
}

func NewAttributeRepo() *AttributeRepo {
	return &AttributeRepo{
		db:    global.DB(),
		cache: NewCache[[]*Attribute](), // 初始化属性数组缓存，默认10分钟过期
	}
}

type AttributeFilter struct {
	// 添加过滤字段
	Name         string
	AttrID       string
	DeviceTypeID string
}

func (p AttributeRepo) fmtFilter(f AttributeFilter) *gorm.DB {
	db := p.db
	// 添加条件
	if f.DeviceTypeID != "" {
		db = db.Where(Attribute{DeviceTypeID: f.DeviceTypeID})
	}
	if f.Name != "" {
		db = db.Where(Attribute{Name: f.Name})
	}
	if f.AttrID != "" {
		db = db.Where(Attribute{AttrID: f.AttrID})
	}
	return db
}

func (p AttributeRepo) Insert(data *Attribute) error {
	err := p.db.Create(data).Error
	// 插入后清理相关缓存
	if err == nil && data.DeviceTypeID != "" {
		p.CacheDelete(data.DeviceTypeID)
	}
	return err
}

func (p AttributeRepo) FindOneByFilter(f AttributeFilter) (*Attribute, error) {
	var result Attribute
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据设备类型ID查询物模型属性
func (p AttributeRepo) FindByDeviceTypeID(deviceTypeID string) ([]*Attribute, error) {
	return p.FindByDeviceTypeIDWithCache(deviceTypeID)
}

func (p AttributeRepo) FindByFilter(f AttributeFilter, page *dto.PageInfo) ([]*Attribute, error) {
	xlog.Debug("attr model find by filter:", "filter", f)
	var results []*Attribute
	db := p.fmtFilter(f).Model(&Attribute{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (p AttributeRepo) CountByFilter(f AttributeFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&Attribute{})
	err = db.Count(&size).Error
	return size, err
}

func (p AttributeRepo) Update(data *Attribute) error {
	// 先查询要删除的属性信息，获取DeviceTypeID用于清理缓存
	var attr Attribute
	if err := p.db.Where(Attribute{AttrID: data.AttrID}).First(&attr).Error; err != nil {
		// 属性不存在
		return err
	}
	err := p.db.Where(Attribute{AttrID: data.AttrID}).Updates(data).Error
	// 更新后清理相关缓存
	if err == nil && attr.DeviceTypeID != "" {
		p.CacheDelete(attr.DeviceTypeID)
	}
	return err
}

func (p AttributeRepo) DeleteByDeviceTypeID(deviceTypeID string) error {
	db := p.fmtFilter(AttributeFilter{DeviceTypeID: deviceTypeID})
	err := db.Delete(&Attribute{}).Error
	// 删除后清理相关缓存
	if err == nil && deviceTypeID != "" {
		p.CacheDelete(deviceTypeID)
	}
	return err
}

// 根据属性ID删除物模型属性
func (p AttributeRepo) Delete(attrID string) error {
	// 先查询要删除的属性信息，获取DeviceTypeID用于清理缓存
	var attr Attribute
	if err := p.db.Where(Attribute{AttrID: attrID}).First(&attr).Error; err != nil {
		// 属性不存在
		return err
	}

	// 如果查询成功，执行删除操作
	err := p.db.Where(Attribute{AttrID: attrID}).Delete(&Attribute{}).Error
	// 删除成功后清理相关缓存
	if err == nil && attr.DeviceTypeID != "" {
		p.CacheDelete(attr.DeviceTypeID)
	}
	return err
}

func (p AttributeRepo) FindOne(attrID string) (*Attribute, error) {
	var result Attribute
	err := p.db.Where(Attribute{AttrID: attrID}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p AttributeRepo) MultiInsert(data []*Attribute) error {
	err := p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&Attribute{}).Create(data).Error
	// 批量插入后，清理相关的缓存
	if err == nil {
		p.invalidateCacheByAttributes(data)
	}
	return err
}

// ================== 缓存操作方法 ===========================

// getCacheKey 生成缓存键
func (p AttributeRepo) getCacheKey(deviceTypeID string) string {
	return "attrs:" + deviceTypeID
}

// CacheGet 从缓存获取属性数组
func (p *AttributeRepo) CacheGet(deviceTypeID string) ([]*Attribute, bool) {
	return p.cache.Get(p.getCacheKey(deviceTypeID))
}

// CacheSet 设置属性数组到缓存
func (p *AttributeRepo) CacheSet(deviceTypeID string, attributes []*Attribute) {
	p.cache.Set(p.getCacheKey(deviceTypeID), attributes)
}

// CacheDelete 删除指定设备类型的属性缓存
func (p *AttributeRepo) CacheDelete(deviceTypeID string) {
	p.cache.Delete(p.getCacheKey(deviceTypeID))
}

// FindByDeviceTypeIDWithCache 带缓存的设备类型属性查询
// 优先从缓存获取，缓存未命中时从数据库查询并缓存结果
func (p *AttributeRepo) FindByDeviceTypeIDWithCache(deviceTypeID string) ([]*Attribute, error) {
	// 先尝试从缓存获取
	if attributes, found := p.CacheGet(deviceTypeID); found {
		return attributes, nil
	}

	// 缓存未命中，从数据库查询
	var results []*Attribute
	db := p.fmtFilter(AttributeFilter{DeviceTypeID: deviceTypeID}).Model(&Attribute{})
	err := db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	// 查询成功后缓存结果
	p.CacheSet(deviceTypeID, results)

	return results, nil
}

// invalidateCacheByAttributes 根据属性列表清理相关缓存
func (p *AttributeRepo) invalidateCacheByAttributes(attributes []*Attribute) {
	deviceTypeIDs := make(map[string]bool)
	for _, attr := range attributes {
		if attr.DeviceTypeID != "" {
			deviceTypeIDs[attr.DeviceTypeID] = true
		}
	}

	// 清理所有相关的设备类型缓存
	for deviceTypeID := range deviceTypeIDs {
		p.CacheDelete(deviceTypeID)
	}
}
