package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm/clause"
)

type SysConfig struct {
	BaseModelCreateUpdate
	Key    string            `gorm:"uniqueIndex;comment:键值"         json:"key"` //
	Config datatypes.JSONMap `gorm:"comment:配置信息"     json:"config"`            //公司其他信息，比如承建商公司的话，会有承建商的编码信息
}

func (d *IDao) QuerySystemConfig(key string) (*SysConfig, error) {
	sconfig := &SysConfig{}
	err := d.db.Model(SysConfig{}).Where(SysConfig{Key: key}).First(sconfig).Error
	if err != nil {
		return sconfig, err
	}
	return sconfig, nil
}

func (d *IDao) UpdateSystemConfig(key string, configMap datatypes.JSONMap) error {
	sysConfig := SysConfig{
		Key:    key,
		Config: configMap,
	}
	// 使用OnConflict子句处理冲突，假设"key"字段有唯一约束
	err := d.db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "key"}},               // 冲突列
		DoUpdates: clause.AssignmentColumns([]string{"config"}), // 冲突时更新的字段
	}).Create(&sysConfig).Error

	if err != nil {
		return err
	}
	return nil
}
