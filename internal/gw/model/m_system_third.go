package model

import (
	"fmt"

	"bs.com/app/config"
	"bs.com/app/pkg/xminio"
	"bs.com/app/pkg/xutils"
)

// upload to OSS
// 兼容性设计：如果获取 oss 下载路径失败，则前端拼接 Host + 本地路径，得到文件下载路径
func (d *IDao) OssUpload(uid int64, path string) (string, error) {
	//都放在一个bucket吧，省的麻烦
	mConf := config.Get().Minio
	conf := xminio.Config{
		EndPoint:        mConf.Endpoint,
		Domain:          mConf.Domain,
		AccessKeyID:     mConf.AccessKey,
		SecretAccessKey: mConf.SecretKey,
		BucketName:      mConf.BucketOther,
	}
	var client = xminio.NewClient(&conf)

	objName := xutils.FileName(path)
	urlStr, err := client.FPutObject(objName, path)
	if err != nil {
		//上传失败要记录日志

		var user *User
		user, err = d.GetUserByUID(uid)
		if err != nil {
			company_id := user.CompanyID
			_ = d.AddNewLog(company_id, uid, "上传文件失败", fmt.Sprintf("文件本地路径：%s, 错误提示：%s", path, err.Error()))
		}
	}

	return urlStr, err
}
