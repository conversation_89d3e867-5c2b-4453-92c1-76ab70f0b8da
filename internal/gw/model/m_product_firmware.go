package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 添加 firmware 的时候，需要选择product 和 category
type Firmware struct {
	BaseModelCreateUpdate

	FirmwareID string `gorm:"uniqueIndex;type:varchar(64);comment:软件ID" json:"firmware_id"` //firmware id
	ProductID  string `gorm:"index;comment:产品ID" json:"product_id"`                         //产品ID，外键

	//文件信息：分组、版本、文件名、文件大小、文件保存路径、文件下载地址、文件签名、文件描述
	FileID  string `gorm:"index;comment:文件ID" json:"file_id"`                //文件ID，外键
	Version string `gorm:"index;comment:软件版本SemVer" json:"version"`          //版本，必须唯一，semVer ，x.y.z
	Group   string `gorm:"type:varchar(64);index;comment:固件分组" json:"group"` //对应设备分组
	Status  bool   `gorm:"default:true; comment:描述" json:"status"`           //是否使能
	Desc    string `gorm:"type:varchar(255);comment:描述" json:"desc"`         //描述

	// 关联文件信息
	File *File `gorm:"foreignKey:FileID" json:"file,omitempty"`
}

type FirmwareRepo struct {
	db *gorm.DB
}

func NewFirmwareRepo() *FirmwareRepo {
	return &FirmwareRepo{
		db: global.DB(),
	}
}

type FirmwareFilter struct {
	ProductID  string
	Status     bool
	FirmwareID string
	Version    string
	Group      string
}

// 格式化固件过滤条件
func (p FirmwareRepo) fmtFilter(f FirmwareFilter) *gorm.DB {
	db := p.db

	// 添加条件
	if f.ProductID != "" {
		db = db.Where(Firmware{ProductID: f.ProductID})
	}
	if f.FirmwareID != "" {
		db = db.Where(Firmware{FirmwareID: f.FirmwareID})
	}
	if f.Version != "" {
		db = db.Where(Firmware{Version: f.Version})
	}
	if f.Group != "" {
		db = db.Where(Firmware{Group: f.Group})
	}
	if f.Status {
		db = db.Where("status = ?", f.Status)
	}

	return db
}

// 插入固件
func (p FirmwareRepo) Insert(data *Firmware) error {
	return p.db.Create(data).Error
}

// 批量插入固件
func (p FirmwareRepo) MultiInsert(data []*Firmware) error {
	return p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&Firmware{}).Create(data).Error
}

// 根据过滤条件查找单个固件
func (p FirmwareRepo) FindOneByFilter(f FirmwareFilter) (*Firmware, error) {
	var result Firmware
	db := p.fmtFilter(f)
	// 预加载文件信息
	db = db.Preload("File")
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据ID查找固件
func (p FirmwareRepo) FindOne(firmwareID string) (*Firmware, error) {
	var result Firmware
	// 预加载文件信息
	err := p.db.Where(Firmware{FirmwareID: firmwareID}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据过滤条件查找多个固件
func (p FirmwareRepo) FindByFilter(f FirmwareFilter, page *dto.PageInfo) ([]*Firmware, error) {
	var results []*Firmware
	db := p.fmtFilter(f).Model(&Firmware{})
	// 预加载文件信息
	db = db.Preload("File")
	var err error

	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}

	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// 根据过滤条件统计固件数量
func (p FirmwareRepo) CountByFilter(f FirmwareFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&Firmware{})
	err = db.Count(&size).Error
	return size, err
}

// 更新固件
func (p FirmwareRepo) Update(data *Firmware) error {
	return p.db.Where(Firmware{FirmwareID: data.FirmwareID}).Updates(data).Error
}

// 根据过滤条件删除固件
func (p FirmwareRepo) DeleteByFilter(f FirmwareFilter) error {
	db := p.fmtFilter(f)
	return db.Delete(&Firmware{}).Error
}

// 根据ID删除固件
func (p FirmwareRepo) Delete(firmwareID string) error {
	return p.db.Where(Firmware{FirmwareID: firmwareID}).Delete(&Firmware{}).Error
}

// 根据产品ID查找最新版本固件
func (p FirmwareRepo) FindLatestByProduct(productID string) (*Firmware, error) {
	var result Firmware
	// 预加载文件信息
	err := p.db.Where(Firmware{ProductID: productID}).Preload("File").Order("created_at DESC").First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 取最新版本号的 firmware，不关心分组
func (p *FirmwareRepo) FirmwareQueryLatest(product_id string) (*Firmware, error) {
	data := make(map[string]interface{})

	data["product_id"] = product_id
	data["status"] = true

	var ret Firmware
	err := p.db.Model(&Firmware{}).Where(data).Order("ID DESC").First(&ret).Error
	if err != nil {
		return nil, err
	}
	return &ret, err
}

// ====================================================================================================
// 添加 firmware的时候，为此全量固件生成对应的差分包
// 差分升级包：两个文件的差分
type FirmwareDiff struct {
	BaseModelCreate
	FirmwareDiffID string `gorm:"uniqueIndex;comment:差分ID" json:"firmware_diff_id"` //差分ID
	FirmwareID     string `gorm:"index;comment:全量固件ID" json:"firmware_id"`          //新固件的全量固件ID，外键

	// 联合唯一 ID
	NewVersion string `gorm:"uniqueIndex:idx_firmwarediff_version;comment:新版本号" json:"new_version"` //新固件版本号，对应 FirmwareID
	OldVersion string `gorm:"uniqueIndex:idx_firmwarediff_version;comment:旧版本号" json:"old_version"` //旧固件版本号

	//文件信息：文件大小、文件保存路径、文件下载地址、文件签名、文件描述
	FileID string `gorm:"index;comment:文件ID" json:"file_id"`        //文件ID，外键
	Desc   string `gorm:"type:varchar(255);comment:描述" json:"desc"` //描述

	// 关联信息
	File *File `gorm:"foreignKey:FileID" json:"file,omitempty"`
}

type FirmwareDiffRepo struct {
	db *gorm.DB
}

func NewFirmwareDiffRepo() *FirmwareDiffRepo {
	return &FirmwareDiffRepo{
		db: global.DB(),
	}
}

type FirmwareDiffFilter struct {
	FirmwareID     string
	FirmwareDiffID string
	NewVersion     string
	OldVersion     string
}

// 格式化固件差分过滤条件
func (p FirmwareDiffRepo) fmtFilter(f FirmwareDiffFilter) *gorm.DB {
	db := p.db

	// 添加条件
	if f.FirmwareID != "" {
		db = db.Where(FirmwareDiff{FirmwareID: f.FirmwareID})
	}
	if f.FirmwareDiffID != "" {
		db = db.Where(FirmwareDiff{FirmwareDiffID: f.FirmwareDiffID})
	}
	if f.NewVersion != "" {
		db = db.Where(FirmwareDiff{NewVersion: f.NewVersion})
	}
	if f.OldVersion != "" {
		db = db.Where(FirmwareDiff{OldVersion: f.OldVersion})
	}

	return db
}

// 插入固件差分
func (p FirmwareDiffRepo) Insert(data *FirmwareDiff) error {
	return p.db.Create(data).Error
}

// 批量插入固件差分
func (p FirmwareDiffRepo) MultiInsert(data []*FirmwareDiff) error {
	return p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&FirmwareDiff{}).Create(data).Error
}

// 根据过滤条件查找单个固件差分
func (p FirmwareDiffRepo) FindOneByFilter(f FirmwareDiffFilter) (*FirmwareDiff, error) {
	var result FirmwareDiff
	db := p.fmtFilter(f)
	// 预加载文件和固件信息
	db = db.Preload("File")
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据ID查找固件差分
func (p FirmwareDiffRepo) FindOne(firmwareDiffID string) (*FirmwareDiff, error) {
	var result FirmwareDiff
	// 预加载文件和固件信息
	err := p.db.Where(FirmwareDiff{FirmwareDiffID: firmwareDiffID}).Preload("File").First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据过滤条件查找多个固件差分
func (p FirmwareDiffRepo) FindByFilter(f FirmwareDiffFilter, page *dto.PageInfo) ([]*FirmwareDiff, error) {
	var results []*FirmwareDiff
	db := p.fmtFilter(f).Model(&FirmwareDiff{})
	// 预加载文件和固件信息
	db = db.Preload("File")
	var err error

	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}

	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// 根据过滤条件统计固件差分数量
func (p FirmwareDiffRepo) CountByFilter(f FirmwareDiffFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&FirmwareDiff{})
	err = db.Count(&size).Error
	return size, err
}

// 更新固件差分
func (p FirmwareDiffRepo) Update(data *FirmwareDiff) error {
	return p.db.Where(FirmwareDiff{FirmwareDiffID: data.FirmwareDiffID}).Updates(data).Error
}

// 根据过滤条件删除固件差分
func (p FirmwareDiffRepo) DeleteByFilter(f FirmwareDiffFilter) error {
	db := p.fmtFilter(f)
	return db.Delete(&FirmwareDiff{}).Error
}

// 根据ID删除固件差分
func (p FirmwareDiffRepo) Delete(firmwareDiffID string) error {
	return p.db.Where(FirmwareDiff{FirmwareDiffID: firmwareDiffID}).Delete(&FirmwareDiff{}).Error
}

// 根据固件ID和旧版本查找差分包
func (p FirmwareDiffRepo) FindDiffByVersions(firmwareID string, oldVersion string) (*FirmwareDiff, error) {
	var result FirmwareDiff
	// 预加载文件和固件信息
	err := p.db.Where(FirmwareDiff{FirmwareID: firmwareID, OldVersion: oldVersion}).
		Preload("File").
		First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
