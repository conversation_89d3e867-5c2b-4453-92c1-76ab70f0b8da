package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/xlog"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type DeviceType struct {
	BaseModelCreateUpdate
	CompanyID int64 `gorm:"index;comment:公司ID"      json:"company_id"` // 如果按公司进行划分数据权限的话

	DeviceTypeID   string `gorm:"unique;comment:设备类型ID"   json:"device_type_id"` // 设备类型ID，不用本身的自增id.一:让用户见的类型ID,二,数据迁移方便
	DeviceTypeCode string `gorm:"comment:设备类型code"      json:"device_type_code"` // 设备类型code，一型一密使用
	AuthType       int32  `gorm:"comment::鉴权类型"         json:"auth_type"`        // 1:一型一密,2:一机一密
	Name           string `gorm:"comment:名称"              json:"name"`           // 名称
	Icon           string `gorm:"comment:类型图标"          json:"icon"`
	ConnType       string `gorm:"comment:网络类型"           json:"conn_type"`  // 1:wifi, 2:ethernet, 3:2G, 4:3G, 5:4G, 6:5G,7.其他
	AccessType     int32  `gorm:"comment:接入类型"          json:"access_type"` // 1. 直连设备, 2. 网关子设备, 3.网关
	// 自定义数据流：设备的接入服务。
	// 用户选择了一个，这里就保存此接入服务器的唯一 ID。
	// 接入点 ID、鉴权信息(实现一型一密的鉴权)
	AccessPointIDs datatypes.JSONMap `gorm:"comment:接入点" json:"access_point_ids"` // key:accessPointID value： accessPointName

	TemplateKind  int32                               `gorm:"comment:类型"             json:"template_kind"`   // 类型:1:自定义,2.模板库,3.产品库导入
	TemplateLibID string                              `gorm:"comment:模板库ID"          json:"template_lib_id"` // 模板库ID
	Desc          string                              `gorm:"comment:描述"              json:"desc"`
	ExtendInfo    datatypes.JSONSlice[dto.ExtendInfo] `gorm:"comment:扩展信息"           json:"extend_info"` // 扩展信息.设备位置、维护人、到期时间、合同编号、所属单位等

	ActiveConfig datatypes.JSONMap                   `gorm:"comment:激活配置"   json:"active_config"` // 设备在线及活跃设置,{online_delay: 600000, active_delay: 300000}
	Tags         datatypes.JSONSlice[map[string]any] `gorm:"comment:标签"       json:"tags"`        // 多个 tag，key: tag value： tagName
	Status       int32                               `gorm:"comment:状态"       json:"status"`      // 1启用,-1禁用
}

//  ---------------------------------------------

// //////////  device type repo //////////////
type DeviceTypeRepo struct {
	db            *gorm.DB
	AttributeRepo *AttributeRepo
	EventRepo     *EventRepo
	CommandRepo   *CommandRepo
	cache         *Cache[*DeviceType] // 设备类型缓存
}

func NewDeviceTypeRepo() *DeviceTypeRepo {
	return &DeviceTypeRepo{
		db:            global.DB(),
		AttributeRepo: NewAttributeRepo(),
		EventRepo:     NewEventRepo(),
		CommandRepo:   NewCommandRepo(),
		cache:         GetCacheManager().DeviceTypeCache(),
	}
}

type DeviceTypeFilter struct {
	// 添加过滤字段
	CompanyID    int64
	DeviceTypeID string
	Name         string
	Status       int32
}

func (p DeviceTypeRepo) fmtFilter(f DeviceTypeFilter) *gorm.DB {
	db := p.db
	if f.CompanyID != 0 {
		db = db.Where(DeviceType{CompanyID: f.CompanyID})
	}
	if f.DeviceTypeID != "" {
		db = db.Where(DeviceType{DeviceTypeID: f.DeviceTypeID})
	}
	if f.Name != "" {
		xlog.Debug("query name:", "name", f.Name)
		db = db.Where(DeviceType{Name: f.Name})
	}
	if f.Status != 0 {
		db = db.Where(DeviceType{Status: f.Status})
	}
	return db
}

func (p DeviceTypeRepo) Insert(data *DeviceType) error {
	return p.db.Create(data).Error
}

func (p DeviceTypeRepo) FindOneByFilter(f DeviceTypeFilter) (*DeviceType, error) {
	var result DeviceType
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p DeviceTypeRepo) FindByFilter(f DeviceTypeFilter, page *dto.PageInfo) ([]*DeviceType, error) {
	var results []*DeviceType
	db := p.fmtFilter(f).Model(&DeviceType{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (p DeviceTypeRepo) CountByFilter(f DeviceTypeFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&DeviceType{})
	err = db.Count(&size).Error
	return size, err
}

func (p DeviceTypeRepo) Update(data *DeviceType) error {
	err := p.db.Where(DeviceType{DeviceTypeID: data.DeviceTypeID}).Updates(data).Error
	// 更新后清理相关缓存
	if err == nil && data.DeviceTypeID != "" {
		p.CacheDelete(data.DeviceTypeID)
	}
	return err
}

func (p DeviceTypeRepo) DeleteByID(device_type_id string) error {
	err := p.db.Where("device_type_id = ?", device_type_id).Delete(&DeviceType{}).Error
	// 删除后清理缓存
	if err == nil {
		p.CacheDelete(device_type_id)
	}
	return err
}

// 根据设备类型ID查询物模型属性
func (p DeviceTypeRepo) FindAttrsByDeviceTypeID(device_type_id string) (attr []*Attribute, err error) {
	return p.AttributeRepo.FindByFilter(AttributeFilter{DeviceTypeID: device_type_id}, nil)
}

// 根据设备类型ID查询物模型事件
func (p DeviceTypeRepo) FindEventsByDeviceTypeID(device_type_id string) (attr []*Event, err error) {
	return p.EventRepo.FindByFilter(EventFilter{DeviceTypeID: device_type_id}, nil)
}

// 根据设备类型ID查询物模型命令
func (p DeviceTypeRepo) FindCommandsByDeviceTypeID(device_type_id string) (attr []*Command, err error) {
	return p.CommandRepo.FindByFilter(CommandFilter{DeviceTypeID: device_type_id}, nil)
}

// ================== 缓存操作方法 ===========================

// CacheGet 从缓存获取设备类型数据
func (p *DeviceTypeRepo) CacheGet(key string) (*DeviceType, bool) {
	return p.cache.Get(key)
}

// CacheSet 设置设备类型数据到缓存
func (p *DeviceTypeRepo) CacheSet(key string, deviceType *DeviceType) {
	p.cache.Set(key, deviceType)
}

// CacheDelete 删除缓存中的设备类型数据
func (p *DeviceTypeRepo) CacheDelete(key string) {
	p.cache.Delete(key)
}

// FindOneWithCache 带缓存的单个设备类型查询
func (p *DeviceTypeRepo) FindOne(deviceTypeID string) (*DeviceType, error) {
	// 先尝试从缓存获取
	if deviceType, found := p.CacheGet(deviceTypeID); found {
		return deviceType, nil
	}

	// 缓存未命中，直接查询数据库（避免递归调用FindOne）
	var result DeviceType
	err := p.db.Where(DeviceType{DeviceTypeID: deviceTypeID}).First(&result).Error
	if err != nil {
		return nil, err
	}

	// 查询成功后缓存结果
	p.CacheSet(deviceTypeID, &result)
	return &result, nil
}
