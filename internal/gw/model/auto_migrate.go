package model

import (
	"fmt"
	"reflect"

	"bs.com/app/global"
	"bs.com/app/pkg/xlog"
	"gorm.io/gorm"
)

var tblArr = []any{
	// system
	&SysConfig{},
	&User{},
	&Menu{},
	&Role{},
	&RoleMenuMap{},
	&RoleUserMap{},
	&AccessLog{},
	&SysLog{},
	&Company{},
	&CompanyRoleMap{},
	&UserGroup{},
	&UserGroupMap{},
	&UserMessage{},

	&Area{},

	// device
	&DeviceType{},
	&Device{},
	&Attribute{},
	&Event{},
	&Command{},
	&DeviceGroup{},
	&AccessPoint{}, // 设备接入点
	&DeviceGroup{},

	// product
	&Product{},
	&Hardware{},
	&Firmware{},
	&FirmwareDiff{},
}

func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(tblArr...)
}

// mysql
// 数据库迁移:参考 migrate.md
func MysqlAutoMigrate() error {
	db := global.DB()

	return autoMigrate(db.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"))
}

// postgresql
func PGAutoMigrate() error {

	db := global.DB()

	// 删除索引和约束，重建
	// DropIndex()
	// DropConstaint()

	// 1. 同步表结果
	err := autoMigrate(db)
	if err != nil {
		xlog.Error("auto migrate error")
		return err
	}

	// 2. 处理主键 primaryKey
	pgAddPrimaryKey(db)

	// 3、 同步全部表的 _id_seq
	for _, one := range tblArr {
		pgSyncSequence(db, one)
	}

	return nil
}

// 支持传输 struct or struct ptr
func GetTableName(model any) string {
	var modelType reflect.Type

	// 如果传入的是指针类型，获取它指向的元素类型
	if reflect.TypeOf(model).Kind() == reflect.Ptr {
		modelType = reflect.TypeOf(model).Elem()
	} else {
		modelType = reflect.TypeOf(model)
	}

	// 创建一个 gorm.Statement 并解析类型
	stmt := &gorm.Statement{DB: global.DB()}
	stmt.Parse(reflect.New(modelType).Interface())

	return stmt.Schema.Table
}

func getTableList() []string {
	tbNameList := []string{}
	for _, one := range tblArr {
		tbName := GetTableName(one)
		tbNameList = append(tbNameList, tbName)
	}
	return tbNameList
}

func pgAddPrimaryKey(db *gorm.DB) {
	tbNameList := getTableList()
	for _, tbName := range tbNameList {
		idx := fmt.Sprintf("%s_pkey", tbName)
		if !db.Migrator().HasConstraint(tbName, idx) {
			xlog.Warn(">>> add primary key for table : " + tbName)
			// err := db.Migrator().CreateIndex(tbName, idx) // 不确定是不是在 id 上添加 PRIMARY KEY
			sql := fmt.Sprintf("ALTER TABLE public.%s ADD CONSTRAINT %s PRIMARY KEY (id);", tbName, idx)
			if err := db.Exec(sql).Error; err != nil {
				xlog.Error("create primary key error:" + err.Error())
			}
		}
	}
}

// 同步表的序列  xxx_id_seq
// 当迁移数据的时候，如果没能同步修改 sequence_id 的时候，会报错：
// ERROR: duplicate key value violates unique constraint \"dict_item_pkey\" (SQLSTATE 23505)}
// 当在命令行下插入数据、删除数据的时候，会导致这种不同步的情况

func pgSyncSequence(db *gorm.DB, model interface{}) error {
	tableName := GetTableName(model)
	columnName := "id"
	sequenceName := fmt.Sprintf("%s_%s_seq", tableName, columnName)

	var maxID int

	// 查询表中最大的 ID 值，如果为空则使用 0。使用 COALESCE 函数处理 NULL 值
	result := db.Table(tableName).Select(fmt.Sprintf("COALESCE(MAX(%s), 0)", columnName)).Scan(&maxID)
	if result.Error != nil {
		xlog.Error("SELECT MAX(id) FROM tablename failed", "err", result.Error)
		return result.Error
	}

	// 设置序列值
	// 为了让序列生效，你需要确保 is_called 为 true。
	execResult := db.Exec("SELECT setval(?, ?, true)", sequenceName, maxID+1)
	if execResult.Error != nil {
		xlog.Error("SELECT setval('my_table_id_seq', maxID) failed", "err", result.Error)
		return execResult.Error
	}

	// rowsAffected := execResult.RowsAffected
	// xlog.Debug("Sequence  synced", "table", tableName, "rows affected", rowsAffected)
	return nil
}

// 删除所有索引，PRIMARY KEY: 属于 约束 (Constraint)，不是索引 (Index)
func DropIndex() {
	db := global.DB()
	for _, one := range tblArr {
		arrIdx, err := db.Migrator().GetIndexes(one)
		if err != nil {
			xlog.Error("get indexes err", "err", err)
			return
		}
		for _, idx := range arrIdx {
			xlog.Debug("drop index", "table_name", GetTableName(one), "index", idx.Name())

			err = db.Migrator().DropIndex(one, idx.Name())
			if err != nil {
				xlog.Error("drop indexes err", "err", err)
				return
			}
		}
	}
}

func DropConstaint() {
	db := global.DB()
	for _, one := range tblArr {

		// 对于有一些表，比如 user，名字是关键字，需要带 schematic，默认是 public
		tbName := "public." + GetTableName(one)
		// tbName := getTableName(DB, one)

		var connNameArr []string
		ret := db.Raw(fmt.Sprintf("SELECT conname FROM pg_constraint WHERE conrelid = '%s'::regclass;", tbName)).Find(&connNameArr).Error
		if ret != nil {
			xlog.Error("get constraint faild", "err", ret.Error())
			return
		}

		for _, constraint := range connNameArr {
			// constraint := fmt.Sprintf("%s_pkey", getTableName(DB, one))
			xlog.Debug("drop constaint", "table_name", tbName, "constraint", constraint)

			db.Exec(fmt.Sprintf("ALTER TABLE %s DROP CONSTRAINT IF EXISTS %s;", tbName, constraint))
			// if DB.Migrator().HasConstraint(one, constraint) {
			// 	xlog.Info("drop constraint: drop it")
			// 	DB.Migrator().DropConstraint(one, constraint)
			// } else {
			// 	xlog.Info("drop constraint: not exist")
			// }
		}
	}
}
