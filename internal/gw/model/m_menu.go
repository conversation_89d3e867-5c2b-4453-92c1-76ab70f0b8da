package model

import (
	"errors"
	"fmt"
	"sort"

	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xlog"
)

type Menu struct {
	BaseModelCreateUpdateLite

	MenuID   int64 `gorm:"index;unique;not null;default:0;comment:菜单ID;" json:"menu_id"` // 菜单id不再用自增id(为了方便更新)，而以menuid作为唯一标识
	ParentID int64 `gorm:"index;comment:父ID"     json:"parent_id"`                       //上级菜单ID

	Title     string `gorm:"comment:菜单名称"                 json:"title"`  // 菜单名称
	LangTag   string `gorm:"comment:中英文标识的json的key"     json:"lang_tag"` // 中英文标识的json的key，例如: menus.dashboard.title
	Name      string `gorm:"comment:组件或按钮英文名称"        json:"name"`       // 组件名称, 或者按钮英文名称
	Icon      string `gorm:"comment:菜单图标"                json:"icon"`    // 菜单图标
	Path      string `gorm:"comment:路由地址"        json:"path"`            // 路由地址, 需唯一
	Component string `gorm:"comment:组件地址"        json:"component"`       //组件路径
	Sort      int    `gorm:"index;comment:显示排序"  json:"sort"`            //显示排序，需唯一(这个不唯一)
	AuthMark  string `gorm:"comment:权限"            json:"auth_mark"`     //权限字符串
	Keepalive int    `gorm:"comment:是否缓存"        json:"keepalive"`       //是否缓存,1缓存,2不keepalive
	IsMenu    int    `gorm:"comment:资源类型"        json:"is_menu"`         //资源类型（1 菜单 2 按钮）
	IsHide    int    `gorm:"comment:是否显示"        json:"is_hide"`         //是否显示,1显示，2不显示
	IsHideTab int    `gorm:"comment:是否显示在tab上" json:"is_hide_tab"`       // 是否显示在菜单上,可能使用，但不显示在菜单上,1显示，2不显示
	IsEnable  int    `gorm:"comment:状态"           json:"is_enable"`      //状态,1正常，其他数字异常
	Desc      string `gorm:"comment:描述"           json:"desc"`           // 备注

	IsIframe          int    `gorm:"comment:是否是iframe"   json:"is_iframe"`
	ShowBadge         int    `gorm:"comment:是否显示徽标"    json:"show_badge"`
	ShowTextBadge     int    `gorm:"comment:是否显示新徽标"  json:"show_text_badge"`
	IsInMainContainer int    `gorm:"comment:是否在主容器中"  json:"is_in_main_container"` //是否在主容器中,1显示，其他不显示
	Link              string `gorm:"comment:连接"          json:"link"`
	ActivePath        string `gorm:"comment:高亮路径"       json:"active_path"`

	Children []*Menu `gorm:"-"  json:"children"` // 子菜单
}

func (i *Menu) String() string {
	return fmt.Sprintf("%d:%s", i.MenuID, i.Title)
}

// 排序用
type MenuArr []*Menu

func (r MenuArr) Len() int { return len(r) }
func (r MenuArr) Less(i, j int) bool {
	return r[i].Sort < r[j].Sort
}
func (r MenuArr) Swap(i, j int) {
	r[i], r[j] = r[j], r[i]
}

// 递归遍历children
func (i *Menu) SortChildren() {
	if len(i.Children) == 0 {
		return
	}
	sort.Stable(MenuArr(i.Children))
	for _, child := range i.Children {
		child.SortChildren()
	}
}

func (d *IDao) BuildMenuTree(menus []*Menu) []*dto.MenuResp {
	sort.Stable(MenuArr(menus))
	for _, one := range menus {
		one.SortChildren()
	}
	// 创建节点映射表（使用指针方便修改）
	nodeMap := make(map[int64]*dto.MenuResp)

	// 第一步：转换所有节点为MenuResp并初始化
	for _, menu := range menus {
		resp := &dto.MenuResp{
			MenuID:    menu.MenuID,
			Path:      menu.Path,
			Name:      menu.Name,
			Component: menu.Component,
			Meta: &dto.Meta{
				Title:             menu.Title,
				LangTag:           menu.LangTag,
				Icon:              menu.Icon,
				Sort:              menu.Sort,
				IsHide:            menu.IsHide == 1,
				KeepAlive:         menu.Keepalive == 1,
				IsHideTab:         menu.IsHideTab == 1,
				IsEnable:          menu.IsEnable == 1,
				IsMenu:            menu.IsMenu == 1,
				IsIframe:          menu.IsIframe == 1,
				ShowBadge:         menu.ShowBadge == 1,
				ShowTextBadge:     menu.ShowTextBadge == 1,
				IsInMainContainer: menu.IsInMainContainer == 1,
				ActivePath:        menu.ActivePath,
				AuthList:          []*dto.AuthBtn{},
			},
			UpdatedAt: menu.UpdatedAt,
		}
		nodeMap[menu.MenuID] = resp
	}

	// 第二步：建立父子关系
	var rootMenus []*dto.MenuResp
	for _, menu := range menus {
		current := nodeMap[menu.MenuID]
		if menu.ParentID == 0 {
			// 根节点直接添加
			rootMenus = append(rootMenus, current)
			continue
		}
		if parent, ok := nodeMap[menu.ParentID]; ok {
			if menu.IsMenu == 1 {
				// 普通菜单添加到父的Children
				parent.Children = append(parent.Children, current)
			} else if menu.IsMenu == 2 {
				// 按钮添加到父的AuthList
				authBtn := &dto.AuthBtn{
					MenuID:   menu.MenuID,
					Name:     menu.Name,
					AuthMark: menu.AuthMark,
					Sort:     menu.Sort,
					Icon:     menu.Icon,
					Title:    menu.Title,
				}
				parent.Meta.AuthList = append(parent.Meta.AuthList, authBtn)
			}
		}
	}
	// 第三步：保持原始排序顺序
	sort.Slice(rootMenus, func(i, j int) bool {
		return rootMenus[i].Meta.Sort < rootMenus[j].Meta.Sort
	})
	return rootMenus
}

func (d *IDao) ListMenu(menuIDs []int64) ([]*Menu, error) {
	var arr []*Menu
	if menuIDs == nil || len(menuIDs) == 0 {
		return arr, nil
	}
	err := d.db.Model(&Menu{}).Where("menu_id in ?", menuIDs).Find(&arr).Error
	return arr, err
}

func (d *IDao) GetAllMenus() ([]*Menu, error) {
	var menus []*Menu
	err := d.db.Model(&Menu{}).Find(&menus).Error
	if err != nil {
		return menus, err
	}
	return menus, nil
}

// 查询用户拥有的菜单权限
func (d *IDao) GetMenusByUID(uid int64) ([]*Menu, error) {
	//查询角色
	rolesArr, err := d.GetRolesByUID(uid)
	if err != nil {
		return nil, err
	}
	//聚合每个角色的资源，注意去重
	var idsArr []int64
	m := make(map[int64]bool) //用于去重
	for _, one := range rolesArr {
		tmpArr, err := d.GetMenuIdsByRoleID(one.ID)
		if err != nil {
			return nil, err
		}
		//遍历，利用map去重，将资源ID存入 数组
		for _, id := range tmpArr {
			_, ok := m[id]
			if ok {
				continue
			}
			idsArr = append(idsArr, id)
			m[id] = true
		}
	}

	return d.ListMenu(idsArr)
}

func (d *IDao) getMaxMenuID() (int64, error) {
	// err = d.db.Model(&Resource{})
	var maxMenuID int64
	err := d.db.Model(&Menu{}).Select("MAX(menu_id)").Scan(&maxMenuID).Error
	if err != nil {
		return 0, err
	}
	return maxMenuID, nil
}

func (d *IDao) AddMenu(req *dto.AddUpdateMenuReq) error {
	// 如果为0，则是新增，查询数据库中最大的menu_id然后加1
	maxMenuID, err := d.getMaxMenuID()
	xlog.Debug("max menuid:", "menuid", maxMenuID, "err", err)
	if err != nil {
		return err
	}

	if req.Path == nil || req.Component == nil {
		return errors.New("req path == nil or component == nil")
	}

	menu := &Menu{
		MenuID:     maxMenuID + 1,
		Name:       req.Name,
		Title:      req.Title,
		LangTag:    req.LangTag,
		Icon:       req.Icon,
		Path:       *req.Path,
		Component:  *req.Component,
		Sort:       req.Sort,
		ActivePath: req.ActivePath,
	}
	if req.MenuID > 0 { // 如果是在某个menu下新增
		menu.ParentID = req.MenuID
	}
	xlog.Debug("max menuid:", "menuid", menu.MenuID)
	menu.IsEnable = boolToInt(req.IsEnable)
	menu.Keepalive = boolToInt(req.KeepAlive)
	menu.IsHide = boolToInt(req.IsHide)
	menu.IsMenu = boolToInt(req.IsMenu)

	if req.IsMenu != nil && !*req.IsMenu {
		menu.AuthMark = req.Name // 如果是btn，则
	}

	return d.db.Model(&Menu{}).Create(&menu).Error
}

func (d *IDao) DeleteMenu(mid int64) error {
	return d.db.Model(&Menu{}).Where(Menu{MenuID: mid}).Delete(&Menu{}).Error
}

func (d *IDao) UpdateMenu(req *dto.AddUpdateMenuReq) error {
	var menu Menu
	err := d.db.Model(&Menu{}).First(&menu, Menu{MenuID: req.MenuID}).Error
	if err != nil {
		return err
	}
	if req.LangTag != "" {
		menu.LangTag = req.LangTag
	}
	if req.Name != "" {
		menu.Name = req.Name
	}
	if req.Title != "" {
		menu.Title = req.Title
	}
	if req.Icon != "" {
		menu.Icon = req.Icon
	}
	if req.Path != nil && *req.Path != "" {
		menu.Path = *req.Path
	}
	if req.Component != nil && *req.Component != "" {
		menu.Component = *req.Component
	}
	if req.ActivePath != "" {
		menu.ActivePath = req.ActivePath
	}

	menu.Sort = 0
	if req.Sort > 0 {
		menu.Sort = req.Sort
	}

	// 处理布尔型字段（使用三元表达式简化）
	menu.IsEnable = boolToInt(req.IsEnable)
	menu.Keepalive = boolToInt(req.KeepAlive)
	menu.IsHide = boolToInt(req.IsHide)
	menu.IsMenu = boolToInt(req.IsMenu)

	if req.IsMenu != nil && !*req.IsMenu {
		menu.AuthMark = req.Name // 如果是btn
	}
	m := bean.StructToMap(menu)
	delete(m, "children")
	return d.db.Model(&Menu{}).Where(Menu{MenuID: req.MenuID}).Updates(m).Error
}

// boolToInt 将布尔值转换为 1/2（1=true, 2=false）
func boolToInt(b *bool) int {
	if b == nil {
		return 2
	}
	if *b {
		return 1
	}
	return 2
}

// 导入菜单函数: 根据menu_id, 存在则更新，不存在则添加
func (d *IDao) ImportMenuToDB(resList []*Menu) {
	var err error
	for _, item := range resList {
		var count int64
		d.db.Model(&Menu{}).Where(Menu{MenuID: item.MenuID}).Count(&count)
		if count > 0 {
			// 存在符合条件的记录, 则更新
			m := StructToMap(item)
			delete(m, "children")
			err = d.db.Model(&Menu{}).Where("menu_id = ?", item.MenuID).Updates(m).Error
			if err != nil {
				xlog.Errorf("update resource error:%s", err.Error())
			}
		} else {
			err = d.db.Model(&Menu{}).Create(item).Error
			if err != nil {
				xlog.Errorf("create resource error:%s", err.Error())
			}
		}
	}
}
