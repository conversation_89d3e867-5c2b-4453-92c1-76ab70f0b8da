package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type DeviceGroup struct {
	BaseModelCreateUpdate

	UserID    int64 `gorm:"index;comment:用户ID"            json:"user_id"`   // 只有用户自己可见自己所创建的产品
	CompanyID int64 `gorm:"index;comment:公司ID"           json:"company_id"` // 如果按公司进行划分数据权限的话

	Name    string `gorm:"comment:名称"               json:"name"`     // 名称
	GroupID string `gorm:"unique;comment:分组id"      json:"group_id"` // 分组id，不使用自增id，方便数据迁移
	Desc    string `gorm:"comment:描述"               json:"desc"`     // 分组描述
}

type DeviceGroupRepo struct {
	db *gorm.DB
}

func NewDeviceGroupRepo() *DeviceGroupRepo {
	return &DeviceGroupRepo{db: global.DB()}
}

type DeviceGroupFilter struct {
	// 添加过滤字段
	Name      string
	CompanyID int64
	GroupIDs  []string
}

func (p DeviceGroupRepo) fmtFilter(f DeviceGroupFilter) *gorm.DB {
	db := p.db
	// 添加条件
	if f.Name != "" {
		db = db.Where(DeviceGroup{Name: f.Name})
	}
	if f.CompanyID != 0 {
		db = db.Where(DeviceGroup{CompanyID: f.CompanyID})
	}
	if len(f.GroupIDs) > 0 {
		db = db.Where("group_id IN ?", f.GroupIDs)
	}
	return db
}

func (p DeviceGroupRepo) Insert(data *DeviceGroup) error {
	return p.db.Create(data).Error
}

func (p DeviceGroupRepo) FindOneByFilter(f DeviceGroupFilter) (*DeviceGroup, error) {
	var result DeviceGroup
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (p DeviceGroupRepo) FindByFilter(f DeviceGroupFilter, page *dto.PageInfo) ([]*DeviceGroup, error) {
	var results []*DeviceGroup
	db := p.fmtFilter(f).Model(&DeviceGroup{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (p DeviceGroupRepo) CountByFilter(f DeviceGroupFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&DeviceGroup{})
	err = db.Count(&size).Error
	return size, err
}

func (p DeviceGroupRepo) Update(data *DeviceGroup) error {
	err := p.db.Where(DeviceGroup{GroupID: data.GroupID}).Updates(data).Error
	return err
}

func (p DeviceGroupRepo) Delete(id string) error {
	err := p.db.Where(DeviceGroup{GroupID: id}).Delete(&DeviceGroup{}).Error
	return err
}
func (p DeviceGroupRepo) FindOne(id string) (*DeviceGroup, error) {
	var result DeviceGroup
	err := p.db.Where(DeviceGroup{GroupID: id}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (p DeviceGroupRepo) MultiInsert(data []*DeviceGroup) error {
	err := p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&DeviceGroup{}).Create(data).Error
	return err
}
