package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Device struct {
	BaseModelCreateUpdate
	CompanyID    int64                               `gorm:"index;comment:公司ID"    json:"company_id"      excel:"A;width:10;header:公司ID"`           // 如果按公司进行划分数据权限的话
	DeviceTypeID string                              `gorm:"index;comment:设备类型ID" json:"device_type_id"  excel:"B;width:20;required;header:设备类型ID"` // 设备类型ID
	DeviceID     string                              `gorm:"unique;comment:设备ID"    json:"device_id"      excel:"C;width:20;required;header:设备ID"`  // 设备ID, 硬件唯一 ID
	DeviceCode   string                              `gorm:"comment:设备code"        json:"device_code"     excel:"D;width:10;header:设备Code"`         // 一机一密使用
	Name         string                              `gorm:"comment:设备名称"         json:"name"            excel:"E;width:20;required;header:设备名称"`   // 设备名称
	Desc         string                              `gorm:"comment:设备描述"         json:"desc"           excel:"F;width:30;header:描述"`               // 设备描述
	ExtendInfo   datatypes.JSONMap                   `gorm:"comment:设备扩展信息"      json:"extend_info"    excel:"G;width:20;header:扩展信息"`              // 设备扩展信息
	Status       int32                               `gorm:"comment:设备状态"         json:"status"         excel:"H;width:20;header:状态(1.可用)"`         // 设备状态，1：可用，2：停用
	ActiveOnline int32                               `gorm:"default:1;comment:是否在线" json:"active_online"  excel:"I;width:20;header:是否在线"`           // 在线状态，1：在线，2：离线
	Alarm        datatypes.JSONMap                   `gorm:"comment:告警"              json:"alarm"         excel:"J;width:20;header:告警"`             // 告警配置
	Groups       datatypes.JSONSlice[string]         `gorm:"comment:分组"              json:"groups"        excel:"K;width:20;header:分组"`             // 分组，里面为分组ID， 可以为多个分组
	Debug        bool                                `gorm:"comment:是否调试"          json:"debug"          excel:"L;width:20;header:是否调试"`            // 是否调试
	Tags         datatypes.JSONSlice[map[string]any] `gorm:"comment:标签"           json:"tags"               excel:"M;width:20;header:标签"`

	DeviceTypeInfo *DeviceType    `gorm:"-"         json:"device_type_info"`
	GroupInfo      []*DeviceGroup `gorm:"-"         json:"group_info"`
}

/// ================== 设备 repo ===========================

type DeviceRepo struct {
	db             *gorm.DB
	DeviceTypeRepo *DeviceTypeRepo
	cache          *Cache[*Device] // 设备缓存
}

func NewDeviceRepo() *DeviceRepo {
	return &DeviceRepo{
		db:             global.DB(),
		DeviceTypeRepo: NewDeviceTypeRepo(),
		cache:          GetCacheManager().DeviceCache(), // 初始化设备缓存，默认10分钟过期
	}
}

type DeviceFilter struct {
	// 添加过滤字段
	Name         string
	DeviceID     string
	DeviceIDs    []string
	DeviceTypeID string
	Status       int32
	CompanyID    int64
}

func (p DeviceRepo) fmtFilter(f DeviceFilter) *gorm.DB {
	db := p.db
	// 添加条件
	if f.Name != "" {
		db = db.Where(Device{Name: f.Name})
	}
	if f.DeviceID != "" {
		db = db.Where(Device{DeviceID: f.DeviceID})
	}
	if len(f.DeviceIDs) > 0 {
		db = db.Where("device_id IN (?)", f.DeviceIDs)
	}
	if f.DeviceTypeID != "" {
		db = db.Where(Device{DeviceTypeID: f.DeviceTypeID})
	}
	if f.Status != 0 {
		db = db.Where(Device{Status: f.Status})
	}
	if f.CompanyID != 0 {
		db = db.Where(DeviceType{CompanyID: f.CompanyID})
	}
	return db
}

func (p DeviceRepo) Insert(data *Device) error {
	return p.db.Create(data).Error
}

func (p DeviceRepo) FindOneByFilter(f DeviceFilter) (*Device, error) {
	var result Device
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
func (p DeviceRepo) FindByFilter(f DeviceFilter, page *dto.PageInfo) ([]*Device, error) {
	var results []*Device
	db := p.fmtFilter(f).Model(&Device{})
	var err error
	if page != nil {
		db = page.ToGorm(db)
		err = db.Count(&page.Total).Error
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// 查询未关联设备类型
func (p DeviceRepo) FindNotDeviceTypeDevices(f DeviceFilter, page *dto.PageInfo) (results []*Device, err error) {
	db := p.fmtFilter(f).Where("device_type_id  = ''")
	if page != nil {
		db = page.ToGorm(db)
		err = db.Count(&page.Total).Error
		if err != nil {
			return nil, err
		}
	}
	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, err
}

func (p DeviceRepo) CountByFilter(f DeviceFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&Device{})
	err = db.Count(&size).Error
	return size, err
}

func (p DeviceRepo) Update(data *Device) error {
	err := p.db.Where(Device{DeviceID: data.DeviceID}).Updates(data).Error
	// 更新后清理相关缓存
	if err == nil && data.DeviceID != "" {
		p.CacheDelete(data.DeviceID)
	}
	return err
}

func (p DeviceRepo) DeleteByID(id string) error {
	err := p.db.Where(Device{DeviceID: id}).Delete(&Device{}).Error
	// 删除后清理缓存
	if err == nil {
		p.CacheDelete(id)
	}
	return err
}

func (p DeviceRepo) MultiInsert(data []*Device) error {
	err := p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&Device{}).Create(data).Error
	return err
}

// 设置在线
func (p DeviceRepo) SetOnline(deviceID string) error {
	one, err := p.FindOne(deviceID)
	if err != nil {
		return err
	}
	if one.ActiveOnline == Online {
		return nil
	}
	one.ActiveOnline = Online
	return p.Update(one)
}

// 设置设备离线
func (p DeviceRepo) SetOffline(deviceID string) error {
	one, err := p.FindOne(deviceID)
	if err != nil {
		return err
	}
	if one.ActiveOnline == Offline {
		return nil
	}
	one.ActiveOnline = Offline
	return p.Update(one)
}

// 设置设备离线 ByDeviceTypeID
func (p DeviceRepo) SetOfflineAll(deviceTypeID string) error {
	return p.db.Where(Device{DeviceTypeID: deviceTypeID}).Updates(Device{ActiveOnline: Offline}).Error
}

// 根据设备ID查询设备属性
func (p DeviceRepo) FindAttrsByDeviceID(deviceID string) (attr []*Attribute, err error) {
	dev, err := p.FindOne(deviceID)
	if err != nil {
		return nil, err
	}
	return p.DeviceTypeRepo.FindAttrsByDeviceTypeID(dev.DeviceTypeID)
}

// 根据设备ID查询设备事件
func (p DeviceRepo) FindEventsByDeviceID(deviceID string) (attr []*Event, err error) {
	dev, err := p.FindOne(deviceID)
	if err != nil {
		return nil, err
	}
	return p.DeviceTypeRepo.FindEventsByDeviceTypeID(dev.DeviceTypeID)
}

// 根据设备ID查询设备命令
func (p DeviceRepo) FindCommandsByDeviceID(deviceID string) (attr []*Command, err error) {
	dev, err := p.FindOne(deviceID)
	if err != nil {
		return nil, err
	}
	return p.DeviceTypeRepo.FindCommandsByDeviceTypeID(dev.DeviceTypeID)
}

// 根据设备ID查询设备类型
func (p DeviceRepo) FindDeviceTypeByDeviceID(deviceID string) (devType *DeviceType, err error) {
	dev, err := p.FindOne(deviceID)
	if err != nil {
		return nil, err
	}
	return p.DeviceTypeRepo.FindOne(dev.DeviceTypeID)
}

// ================== 缓存操作方法 ===========================

// CacheGet 从缓存获取设备数据
// key: 缓存键，通常使用设备ID或其他唯一标识
// 返回: 设备数据, 是否存在
func (p *DeviceRepo) CacheGet(key string) (*Device, bool) {
	return p.cache.Get(key)
}

// CacheSet 设置设备数据到缓存
// key: 缓存键，通常使用设备ID或其他唯一标识
// device: 要缓存的设备数据
func (p *DeviceRepo) CacheSet(key string, device *Device) {
	p.cache.Set(key, device)
}

// CacheDelete 删除缓存中的设备数据
// key: 要删除的缓存键
func (p *DeviceRepo) CacheDelete(key string) {
	p.cache.Delete(key)
}

// FindOneWithCache 带缓存的单个设备查询
// 优先从缓存获取，缓存未命中时从数据库查询并缓存结果
func (p *DeviceRepo) FindOne(deviceID string) (*Device, error) {
	// 先尝试从缓存获取
	if device, found := p.CacheGet(deviceID); found {
		return device, nil
	}

	// 缓存未命中，直接查询数据库（避免递归调用FindOne）
	var result Device
	err := p.db.Where(Device{DeviceID: deviceID}).First(&result).Error
	if err != nil {
		return nil, err
	}

	// 查询成功后缓存结果
	p.CacheSet(deviceID, &result)
	return &result, nil
}
