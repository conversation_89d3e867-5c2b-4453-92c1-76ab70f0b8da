package model

import (
	"time"

	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 设备表：一个设备有多个软件的时候，软件版本号就没那么好搞了啊
type Hardware struct {
	BaseModelCreateUpdate

	//产品信息
	ProductID string `gorm:"index;comment:产品ID" json:"product_id"` //产品ID，外键
	CompanyID int64  `gorm:"index;comment:公司ID" json:"company_id"` //所属公司ID， 外键

	// 硬件唯一ID
	HardwareID   string `gorm:"index;comment:硬件ID" json:"hardware_id"`    //硬件ID
	HardwareCode string `gorm:"index;comment:硬件唯一码" json:"hardware_code"` //硬件 Code

	//device 里面冗余了 product 的 model
	Model     string `gorm:"type:varchar(64);index;comment:产品型号" json:"model"` //设备型号，英文字母+数字表示，可以编码设备版本、型号、生产日期等。来自product
	Name      string `gorm:"type:varchar(255);comment:设备名字" json:"name"`       //默认为产品名字
	Desc      string `gorm:"type:varchar(255);comment:设备备注" json:"desc"`       //可手工备注信息
	FwVersion string `gorm:"index;comment:系统版本号" json:"fw_version"`            //x.y.z
	HwVersion string `gorm:"index;comment:硬件版本号" json:"hw_version"`            //x.y.z

	//硬件信息
	IMEI  string `gorm:"type:varchar(32);not null;index;unique;comment:设备IMEI" json:"imei"` //设备硬件ID
	ICCID string `gorm:"index;column:iccid;comment:设备ICCID" json:"iccid"`                   //仅使用 4G 通讯的才有 ICCID，网口的没有
	Lng   string `gorm:"type:varchar(16);comment:GPS经度" json:"lng"`
	Lat   string `gorm:"type:varchar(16);comment:GPS纬度" json:"lat"`

	//分组信息，用于 OTA
	Group     string `gorm:"type:varchar(64);index;default:release;comment:设备分组:release, dev, test" json:"group"` //测试组、正式组、开发组等。一个设备仅从属于一个分组。默认正式组
	IsAutoOTA bool   `gorm:"default:true;comment:是否自动升级" json:"is_auto_ota"`                                      //是否支持自动升级，默认true。如果支持，则设备握手的时候，服务器会主动下发最新可用固件

	//在线状态、上线离线时间
	Status        int        `gorm:"type:int;index;comment:设备状态" json:"status"` // 1:在线，2：离线
	LastOfflineAt *time.Time `gorm:"comment:最近离线时间" json:"last_offline_at"`
	LastOnlineAt  *time.Time `gorm:"comment:最近上线时间" json:"last_online_at"`
}

type HardwareRepo struct {
	db *gorm.DB
}

func NewHardwareRepo() *HardwareRepo {
	return &HardwareRepo{
		db: global.DB(),
	}
}

type HardwareFilter struct {
	CompanyID  int64
	ProductID  string
	Model      string
	Status     int
	HardwareID string
	FwVersion  string
	Group      string
	IMEI       string
	Name       string
}

// 格式化过滤条件
func (p HardwareRepo) fmtFilter(f HardwareFilter) *gorm.DB {
	db := p.db

	// 添加条件
	if f.Name != "" {
		db = db.Where("name LIKE ?", "%"+f.Name+"%")
	}
	if f.Model != "" {
		db = db.Where(Hardware{Model: f.Model})
	}
	if f.HardwareID != "" {
		db = db.Where(Hardware{HardwareID: f.HardwareID})
	}
	if f.ProductID != "" {
		db = db.Where(Hardware{ProductID: f.ProductID})
	}
	if f.CompanyID > 0 {
		db = db.Where(Hardware{CompanyID: f.CompanyID})
	}
	if f.Status > 0 {
		db = db.Where("status = ?", f.Status)
	}
	if f.FwVersion != "" {
		db = db.Where(Hardware{FwVersion: f.FwVersion})
	}
	if f.Group != "" {
		db = db.Where(Hardware{Group: f.Group})
	}
	if f.IMEI != "" {
		db = db.Where(Hardware{IMEI: f.IMEI})
	}

	return db
}

// 插入硬件设备
func (p HardwareRepo) Insert(data *Hardware) error {
	return p.db.Create(data).Error
}

// 批量插入硬件设备，支持冲突时更新
func (p HardwareRepo) MultiInsert(data []*Hardware) error {
	return p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&Hardware{}).Create(data).Error
}

// 根据过滤条件查找单个硬件设备
func (p HardwareRepo) FindOneByFilter(f HardwareFilter) (*Hardware, error) {
	var result Hardware
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据硬件ID查找硬件设备
func (p HardwareRepo) FindOne(hardwareID string) (*Hardware, error) {
	var result Hardware
	err := p.db.Where(Hardware{HardwareID: hardwareID}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据过滤条件查找多个硬件设备
func (p HardwareRepo) FindByFilter(f HardwareFilter, page *dto.PageInfo) ([]*Hardware, error) {
	var results []*Hardware
	db := p.fmtFilter(f).Model(&Hardware{})
	var err error

	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}

	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// 根据过滤条件统计硬件设备数量
func (p HardwareRepo) CountByFilter(f HardwareFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&Hardware{})
	err = db.Count(&size).Error
	return size, err
}

// 更新硬件设备
func (p HardwareRepo) Update(data *Hardware) error {
	return p.db.Where(Hardware{HardwareID: data.HardwareID}).Updates(data).Error
}

// 根据硬件ID删除硬件设备
func (p HardwareRepo) Delete(hardwareID string) error {
	return p.db.Where(Hardware{HardwareID: hardwareID}).Delete(&Hardware{}).Error
}
