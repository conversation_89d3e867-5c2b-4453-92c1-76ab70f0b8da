package model

import (
	"fmt"

	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/gorm"
)

// 实现接口的通用结构体
type TemplateRepo[T any] struct {
	DB *gorm.DB
}

func NewTemplateRepo[T any]() *TemplateRepo[T] {
	return &TemplateRepo[T]{DB: global.DB()}
}

func (h *TemplateRepo[T]) GetFirst(out *T, conditions map[string]interface{}) error {
	return h.DB.Where(conditions).First(out).Error
}

// 读取最新的一个
func (h *TemplateRepo[T]) GetLastOne(out *T) error {
	return h.DB.Order("id DESC").First(out).Error
}

// 插入
func (h *TemplateRepo[T]) Insert(data []T) error {
	if len(data) == 0 {
		return nil
	}

	// Use gorm's CreateInBatches for batch insertion
	err := h.DB.CreateInBatches(data, 50).Error
	if err != nil {
		return err
	}

	return nil
}

// 先清除再批量插入
func (h *TemplateRepo[T]) ClearAndInsert(data []T) error {
	if len(data) == 0 {
		return nil
	}
	// 清理表的数据
	table_name := GetTableName(data[0])
	err := h.DB.Exec(fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE", table_name)).Error
	if err != nil {
		return err
	}
	// Use gorm's CreateInBatches for batch insertion
	err = h.DB.CreateInBatches(data, 50).Error
	if err != nil {
		return err
	}

	return nil
}

// 查询：pi 是 nil 则不分页查询全部
func (h *TemplateRepo[T]) BatchRead(out *[]T, conditions map[string]interface{}, pi *dto.PageInfo) error {
	var session *gorm.DB

	if conditions == nil {
		session = h.DB.Model(new(T)).Order("id DESC")
	} else {
		session = h.DB.Model(new(T)).Where(conditions).Order("id DESC")
	}
	if pi == nil {
		return session.Find(out).Error
	} else {
		pi.CheckPage()
		return PagedFind(session, out, pi)
	}
}

// 更新：需前端限制某些字段不可以被更新
func (h *TemplateRepo[T]) Update(id int64, data map[string]interface{}) error {
	return h.DB.Model(new(T)).Where("id = ?", id).Updates(data).Error
}

// 使用对象的方式更新, 更新所有字段
func (h *TemplateRepo[T]) UpdateT(id int64, data *T) error {
	return h.DB.Model(new(T)).Where("id = ?", id).Save(data).Error
}

// 添加一个
func (h *TemplateRepo[T]) Create(data map[string]interface{}) error {
	return h.DB.Model(new(T)).Create(data).Error
}

// 按对象方式添加一个
func (h *TemplateRepo[T]) CreateT(data *T) error {
	return h.DB.Model(new(T)).Create(data).Error
}

func (h *TemplateRepo[T]) CreateOrUpdate(data *T) error {
	return h.DB.Model(new(T)).Create(data).Error
}

// 删除
func (h *TemplateRepo[T]) Delete(id int64) error {
	return h.DB.Where("id = ?", id).Delete(new(T)).Error
}

// 事物
func (h *TemplateRepo[T]) WithTransaction(txFunc func(tx *gorm.DB) error) error {
	return h.DB.Transaction(txFunc)
}

// 复杂查询
func (h *TemplateRepo[T]) ComplexQuery(out *[]T, queryFunc func(db *gorm.DB) *gorm.DB) error {
	return queryFunc(h.DB).Find(out).Error
}
