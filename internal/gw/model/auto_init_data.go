package model

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"time"

	"bs.com/app/global"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"bs.com/app/internal/gw/dto"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func InitDBData() {
	// 初始化必要的数据
	xlog.Info("init db data....")

	// 读取目录内容
	initTbList := []any{
		Area{},
		Menu{},
	}

	for _, one := range initTbList {
		initDBErr(ImportTableFromJson(global.DB(), one), "area and menu")
	}

	// 初始化用户表数据
	xlog.Info("init user data...")
	initDBErr(InitUserTable(), "user table")

	// 初始化公司表数据
	xlog.Info("init company data...")
	initDBErr(initCompanyTable(), "company data")

	// 初始化一些配置表
	initDBErr(initSysConfigTable(), "sysconfig")

	// 初始化接入点
	initDBErr(initAccessPoint(), "access point")

	// 初始化设备类型
	initDBErr(InitDeviceType(), "device type")

	xlog.Info("init db data success")
}

func initAccessPoint() error {
	repo := NewAccessPointRepo()

	// dm 服务，作为 mqtt 标准协议接入点
	one := &AccessPoint{
		AccessPointID:   "20250705.default.dm",             //固定，方便调试
		AccessPointCode: "20250705.default.secret.123safe", //固定，方便调试
		CompanyID:       0,                                 // 公司id为0是公共接入点
		Name:            "标准协议",
		ConnProtocol:    "mqtt",
		MsgProtocol:     "json",
		Host:            "127.0.0.1",
		Port:            1883,
		Status:          1,
		ExtendInfo:      datatypes.JSONMap{
			// "mqtt": map[string]interface{}{
			// 	"host": "127.0.0.1",
			// 	"port": 1883,
			// },
		},
	}
	return repo.Insert(one)
}

// ishell 调用
func InitDeviceType() error {
	repo := NewDeviceTypeRepo()
	one := &DeviceType{
		CompanyID:      0,
		DeviceTypeID:   "KF38HDDeXO",    //VdMqtt使用
		DeviceTypeCode: "8WsH7xMAv8tN1", //VdMqtt 鉴权使用
		Name:           "VdMqtt",
		Icon:           "",
		AuthType:       1,    // 一型一密，不需要 deviceCode
		ConnType:       "4G", // 4G 网络
		AccessType:     1,    // 直连设备
		TemplateKind:   1,
		TemplateLibID:  "",
		Desc:           "VdMqtt设备类型",
		ExtendInfo:     datatypes.JSONSlice[dto.ExtendInfo]{},
		ActiveConfig:   datatypes.JSONMap{},
		Tags:           datatypes.JSONSlice[map[string]any]{},
		Status:         1,
	}

	return repo.Insert(one)
}

func initSysConfigTable() error {
	frontendCofigMap := make(map[string]interface{})
	frontendCofigMap["api"] = "http://nuc.beithing.com:53006" // 前端的api
	frontendCofigMap["version"] = "0.1"
	frontendConfig := &SysConfig{
		Key:    FrontendKey,
		Config: datatypes.JSONMap(frontendCofigMap),
	}
	if err := global.G.DB.Create(frontendConfig).Error; err != nil {
		xlog.Error("init frontend config data error:", err.Error())
		return err
	}
	return nil
}

// 初始化阶段，出错即退出
func initDBErr(err error, msg string) {
	if err != nil {
		xlog.Error("init db data error", "err", err.Error(), "msg", msg)

		// 初始化失败就退出
		// 方便 docker logs 查看容器日志
		fmt.Println("init db data error:", err.Error())
		time.Sleep(3 * time.Second)
		os.Exit(1)
	} else {
		xlog.Info("init db data ok", "msg", msg)
	}
}

func initCompanyTable() error {
	cdata := &Company{
		Name:       "北盛信息",
		FullName:   "深圳市北盛信息科技有限公司",
		Desc:       "深圳市北盛信息科技有限公司",
		UUID:       "cqi1rjrbknay",
		ParentID:   0,
		ProvinceID: 10001,
		CityID:     0,
		DistrictID: 0,
		IsDevMan:   true,
	}
	cdata.ID = 1
	if err := global.G.DB.Create(cdata).Error; err != nil {
		xlog.Error("init company data error:", err.Error())
		return err
	}
	return nil
}

func InitUserTable() error {
	udata := &User{
		Username:   "shujun",
		Password:   "shujun123",
		Sex:        1,
		Phone:      "18665991286",
		ProvinceID: 10001,
		CityID:     0,
		DistrictID: 0,
		CompanyID:  1,
		Status:     1,
	}
	udata.ID = 1

	if err := global.G.DB.Create(udata).Error; err != nil {
		xlog.Error("init user data error:", err.Error())
		return err
	}
	return nil
}

// ==================================================================================================
const (
	initDbPath = "./deploy/dbjson" // 数据表json存放路径
)

func ExportTable2Json(db *gorm.DB, one any, pathAndfileName string) error {
	// 获取表名
	tableName := GetTableName(one)

	// 如果传入的是指针类型，获取它指向的元素类型
	var modelType reflect.Type
	if reflect.TypeOf(one).Kind() == reflect.Ptr {
		modelType = reflect.TypeOf(one).Elem()
	} else {
		modelType = reflect.TypeOf(one)
	}

	// 动态生成对应的切片
	slicePtr := reflect.New(reflect.SliceOf(modelType)).Interface()

	// 查询数据
	if err := db.Table(tableName).Order("id ASC").Find(slicePtr).Error; err != nil {
		return fmt.Errorf("failed to query table %s: %w", tableName, err)
	}
	// 遍历 slicePtr 指向的数组，一个个的用 json 序列化，然后写入文件
	sliceValue := reflect.ValueOf(slicePtr).Elem()
	file, err := os.Create(pathAndfileName)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", tableName, err)
	}
	defer file.Close()

	if _, err := file.WriteString("[\n"); err != nil {
		return fmt.Errorf("failed to write opening bracket: %w", err)
	}

	for i := 0; i < sliceValue.Len(); i++ {
		item := sliceValue.Index(i).Interface()
		itemJson, err := json.Marshal(item)
		if err != nil {
			return fmt.Errorf("failed to marshal item: %w", err)
		}

		if _, err := file.Write(itemJson); err != nil {
			return fmt.Errorf("failed to write item to file: %w", err)
		}

		// 如果不是最后一个元素，添加逗号和换行符
		if i < sliceValue.Len()-1 {
			if _, err := file.WriteString(",\n"); err != nil {
				return fmt.Errorf("failed to write comma: %w", err)
			}
		}
	}

	if _, err := file.WriteString("\n]"); err != nil {
		return fmt.Errorf("failed to write closing bracket: %w", err)
	}

	fmt.Printf("Data exported to %s successfully!\n", pathAndfileName)
	return nil
}

// 导出函数，支持不同表结构
func ExportTable2JsonByLine(db *gorm.DB, one any) error {
	//检查文件目录，创建必须的目录
	if err := xutils.MkDirIfNotExist(initDbPath); err != nil {
		fmt.Println("check and create dbjson err")
		os.Exit(1)
	}
	// 获取表名
	tableName := GetTableName(one)
	fileName := filepath.Join(initDbPath, fmt.Sprintf("%s.json", tableName))
	return ExportTable2Json(db, one, fileName)
}

func ImportTableFromJson(db *gorm.DB, one any) error {
	// 获取表名
	tableName := GetTableName(one)
	// 读取 JSON 文件
	fileName := filepath.Join(initDbPath, fmt.Sprintf("%s.json", tableName))

	// 检查路径是否存在
	if !xutils.IsExist(fileName) {
		return fmt.Errorf("file %s not exist", fileName)
	}

	// 先清空表的数据
	if err := db.Exec(fmt.Sprintf("TRUNCATE TABLE %s RESTART IDENTITY CASCADE", tableName)).Error; err != nil {
		return fmt.Errorf("failed to truncate table %s: %w", tableName, err)
	}

	// 导入数据
	// 如果传入的是指针类型，获取它指向的元素类型
	var modelType reflect.Type
	if reflect.TypeOf(one).Kind() == reflect.Ptr {
		modelType = reflect.TypeOf(one).Elem()
	} else {
		modelType = reflect.TypeOf(one)
	}

	jsonData, err := os.ReadFile(fileName)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %w", fileName, err)
	}

	// 动态生成对应的切片
	slicePtr := reflect.New(reflect.SliceOf(modelType)).Interface()

	// 反序列化 JSON 数据
	if err := json.Unmarshal(jsonData, slicePtr); err != nil {
		return fmt.Errorf("failed to unmarshal data: %w", err)
	}

	// 将数据插入数据库
	if err := db.Create(slicePtr).Error; err != nil {
		return fmt.Errorf("failed to insert data into table %s: %w", tableName, err)
	}

	fmt.Printf("Data imported from %s successfully!\n", fileName)
	return nil
}
