package model

import (
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// product 和 deviceType 完全解耦。
// product 只负责设备的注册、升级、配置等操作。
// deviceType 只负责设备的业务抽象，不关心这个设备是我们自己的，还是其他公司的。

// 产品表
type Product struct {
	BaseModelCreateUpdate
	CompanyID   int64             `gorm:"index;comment:公司ID" json:"company_id"`                             //公司 ID，外键
	ProductID   string            `gorm:"type:varchar(64);index;unique;comment:产品ID" json:"product_id"`     //产品唯一表示码
	ProductCode string            `gorm:"type:varchar(64);index;unique;comment:产品Code" json:"product_code"` //产品鉴权 Code
	Model       string            `gorm:"type:varchar(64);index;unique;comment:产品型号" json:"model"`          //设备型号，英文字母+数字表示
	Name        string            `gorm:"type:varchar(255);comment:产品名字" json:"name"`                       //产品名字
	Meta        datatypes.JSONMap `gorm:"comment:产品其他信息" json:"meta"`                                       //产品其他信息，比如硬件方案，硬件版本，软件,通讯协议，配网方式
	Desc        string            `gorm:"type:varchar(255);comment:产品描述信息" json:"desc"`                     //描述
	IsDiff      bool              `gorm:"default:false;comment:是否支持差分" json:"is_diff"`                      //是否支持差分，默认 false
}

type ProductRepo struct {
	db *gorm.DB
}

func NewProductRepo() *ProductRepo {
	return &ProductRepo{
		db: global.DB(),
	}
}

type ProductFilter struct {
	Name      string
	Model     string
	ProductID string
	Status    int32
	CompanyID int64
}

// 格式化过滤条件
func (p ProductRepo) fmtFilter(f ProductFilter) *gorm.DB {
	db := p.db

	// 添加条件
	if f.Name != "" {
		db = db.Where("name LIKE ?", "%"+f.Name+"%")
	}
	if f.Model != "" {
		db = db.Where(Product{Model: f.Model})
	}
	if f.ProductID != "" {
		db = db.Where(Product{ProductID: f.ProductID})
	}
	if f.CompanyID > 0 {
		db = db.Where(Product{CompanyID: f.CompanyID})
	}
	if f.Status > 0 {
		db = db.Where("status = ?", f.Status)
	}

	return db
}

// 插入产品
func (p ProductRepo) Insert(data *Product) error {
	return p.db.Create(data).Error
}

// 批量插入产品
func (p ProductRepo) MultiInsert(data []*Product) error {
	return p.db.Clauses(clause.OnConflict{UpdateAll: true}).Model(&Product{}).Create(data).Error
}

// 根据过滤条件查找单个产品
func (p ProductRepo) FindOneByFilter(f ProductFilter) (*Product, error) {
	var result Product
	db := p.fmtFilter(f)
	err := db.First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据ID查找产品
func (p ProductRepo) FindOne(productID string) (*Product, error) {
	var result Product
	err := p.db.Where(Product{ProductID: productID}).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// 根据过滤条件查找多个产品
func (p ProductRepo) FindByFilter(f ProductFilter, page *dto.PageInfo) ([]*Product, error) {
	var results []*Product
	db := p.fmtFilter(f).Model(&Product{})
	var err error

	if page != nil {
		db = page.ToGorm(db)
		page.Total, err = p.CountByFilter(f)
		if err != nil {
			return nil, err
		}
	}

	err = db.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// 根据过滤条件统计产品数量
func (p ProductRepo) CountByFilter(f ProductFilter) (size int64, err error) {
	db := p.fmtFilter(f).Model(&Product{})
	err = db.Count(&size).Error
	return size, err
}

// 更新产品
func (p ProductRepo) Update(data *Product) error {
	return p.db.Where(Product{ProductID: data.ProductID}).Updates(data).Error
}

// 根据ID删除产品
func (p ProductRepo) Delete(productID string) error {
	return p.db.Where(Product{ProductID: productID}).Delete(&Product{}).Error
}
