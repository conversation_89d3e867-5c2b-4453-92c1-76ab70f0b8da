package model

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
)

// 公司表
type Company struct {
	BaseModelCreateUpdate

	Name     string `gorm:"type:varchar(255);index;comment:公司名字" json:"name"`                     //公司名字
	FullName string `gorm:"column:fullname;type:varchar(255);index;comment:公司名字" json:"fullname"` //公司全称
	Logo     string `gorm:"comment:公司logo" json:"logo"`                                           //公司Logo
	Desc     string `gorm:"type:varchar(255);comment:公司备注" json:"desc"`                           //公司备注
	UUID     string `gorm:"type:varchar(64);index;unique;comment:公司唯一码" json:"uuid"`              //公司唯一码
	ParentID int64  `gorm:"index;comment:父级公司ID" json:"parent_id"`                                //父级公司ID，代理公司的parent_id 为0。
	IsDevMan bool   `gorm:"default:false;comment:是否设备管理员" json:"is_dev_man"`                      //true：是设备管理员

	ProvinceID int64 `gorm:"index;comment:省ID"   json:"province_id"` //省ID
	CityID     int64 `gorm:"index;comment:城市ID"     json:"city_id"`  //市ID
	DistrictID int64 `gorm:"index;comment:区县ID" json:"district_id"`  //区县ID

	Title      string            `gorm:"-" json:"title"`                  //公司 title，登录之后显示在左上角的公司名字，等于当前公司名字，或者父级公司名字
	Children   []*Company        `gorm:"-" json:"children"`               //子公司
	Roles      []*Role           `gorm:"-" json:"roles,omitempty"`        //公司相关的角色，用于权限管理
	ExtendInfo datatypes.JSONMap `gorm:"comment:其他信息" json:"extend_info"` //公司其他信息，比如承建商公司的话，会有承建商的编码信息
}

func (c *Company) String() string {
	return fmt.Sprintf("%d:%s", c.ID, c.Name)
}

// company --- role --- map
// 预定义几个角色，创建公司的时候，建立与公司的绑定。
// 仅超级用户可以编辑角色。
// idx_comp_role_map 联合唯一索引
type CompanyRoleMap struct {
	BaseModelCreate

	CompanyID int64 `gorm:"uniqueIndex:idx_comp_role_map;comment:公司ID" json:"company_id"` //公司ID
	RoleID    int64 `gorm:"uniqueIndex:idx_comp_role_map;comment:角色ID" json:"role_id"`    //角色ID
}

// 为某个 company 添加 role 映射
func (d *IDao) AddCompanyRoleMap(company_id int64, roleIds []int64) error {
	var arr []CompanyRoleMap

	for _, one := range roleIds {
		arr = append(arr, CompanyRoleMap{
			CompanyID: company_id,
			RoleID:    one,
		})
	}
	//如果已经存在，则啥也不干，否则插入
	return d.db.Model(&CompanyRoleMap{}).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "company_id"}, {Name: "role_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"id"}),
	}).Create(&arr).Error
}

// 删除某个company 全部的 role
func (d *IDao) DelCompanyRoleMap(company_id int64) error {
	return d.db.Where("company_id = ?", company_id).Delete(&CompanyRoleMap{}).Error
}

func (d *IDao) GetCompanyByRoleID(rid int64) ([]int64, error) {
	var ids []int64
	err := d.db.Model(&CompanyRoleMap{}).Where("role_id = ?", rid).Select("company_id").Find(&ids).Error
	return ids, err
}

// 所有用户都能得到自己公司下面的角色。也可以添加角色。自己属于什么角色不在此列。
func (d *IDao) GetRoleIdsByCompanyID(company_id int64) ([]int64, error) {
	var ids []int64
	err := d.db.Model(&CompanyRoleMap{}).Where("company_id = ?", company_id).Select("role_id").Find(&ids).Error
	return ids, err
}

// 查询当前公司绑定的角色
func (d *IDao) GetRolesByCompanyID(company_id int64) ([]*Role, error) {
	ids, err := d.GetRoleIdsByCompanyID(company_id)
	if err != nil {
		return nil, err
	}
	var arr []*Role
	if ids == nil || len(ids) == 0 {
		//如果没有角色 id 列表，则返回空
		return arr, nil
	}
	err = d.db.Model(&Role{}).Where("id in ? ", ids).Find(&arr).Order("sort_num ASC").Error
	return arr, err
}

// 缓存公司树，则先缓存
// 对公司有增删改的话，缓存失效。
var mCompanyTree map[int64]*Company

func (d *IDao) rebuildCompanyTree() error {
	var arr []*Company
	// 无法确保 id 是递增的
	if err := d.db.Model(&Company{}).Order("parent_id ASC").Find(&arr).Error; err != nil {
		return err
	}
	mCompanyTree = make(map[int64]*Company)
	for _, one := range arr {
		mCompanyTree[one.ID] = one
		if one.ParentID == 0 {
			continue
		}
		if parent, ok := mCompanyTree[one.ParentID]; ok {
			parent.Children = append(parent.Children, one)
		} else {
			xlog.Warn("rebuild comapny map err,invalid parentID :", one.ParentID)
		}
	}

	return nil
}

// company 添加的时候，必须添加一个用户，作为公司的管理员
func (d *IDao) AddCompany(parent_id int64, req *dto.ReqCompany) error {
	mCompanyTree = nil

	return d.db.Transaction(func(tx *gorm.DB) error {
		//添加公司
		obj := &Company{
			ParentID:   parent_id,
			Name:       req.Name,
			FullName:   req.FullName,
			Logo:       req.Logo,
			Desc:       req.Desc,
			ProvinceID: req.ProvinceID,
			CityID:     req.CityID,
			DistrictID: req.DistrictID,
			IsDevMan:   req.IsDevMan,

			UUID: xutils.UUIDShort2(),
		}
		if err := tx.Create(obj).Error; err != nil {
			return err
		}

		//添加公司-角色-map
		// dao := NewIDao(tx)

		//company - role -map：角色公司映射
		if len(req.Roles) > 0 {
			arr, err := string2Arr(req.Roles)
			if err != nil {
				return err
			}
			xlog.Debug("----------- add company:", req.Roles, "arr", arr)
			if err = d.AddCompanyRoleMap(obj.ID, arr); err != nil {
				return err
			}
		}
		return nil
	})
}

func string2Arr(line string) ([]int64, error) {
	arr1 := strings.Split(line, ",")
	var arr2 []int64
	for _, one := range arr1 {
		t, err := strconv.Atoi(one)
		if err != nil {
			return nil, err
		}
		arr2 = append(arr2, int64(t))
	}

	return arr2, nil
}

func (d *IDao) CompanyFillUUID() error {
	var arr []*Company
	err := d.db.Model(&Company{}).Where("id > 0").Find(&arr).Error
	if err != nil {
		return err
	}

	xlog.Info("arr len : ", len(arr))
	for _, one := range arr {
		if one.UUID != "" {
			continue
		}
		err = d.db.Model(&Company{}).Where("id = ?", one.ID).Update("uuid", xutils.UUIDShort2()).Error
		if err != nil {
			break
		}
	}
	return err
}

// 查询某公司所有子公司相关角色：可能重复
func (d *IDao) GetRoleIdsBySubCompany(company_id int64) ([]int64, error) {
	if mCompanyTree == nil {
		err := d.rebuildCompanyTree()
		if err != nil {
			return nil, err
		}
	}
	parent, ok := mCompanyTree[company_id]
	if !ok {
		return nil, errors.New("invalid company id")
	}

	var arr []int64
	children := parent.Children
	if len(children) == 0 {
		return nil, nil
	}
	//遍历子公司角色
	for _, one := range children {
		role_id_arr1, err := d.GetRoleIdsByCompanyID(one.ID)
		if err != nil {
			return nil, err
		}
		arr = append(arr, role_id_arr1...)

		//递归
		role_id_arr2, err := d.GetRoleIdsBySubCompany(one.ID)
		if err != nil {
			return nil, err
		}
		arr = append(arr, role_id_arr2...)
	}

	return arr, nil
}

// FIXME:理论上不支持操作
func (d *IDao) DelCompany(id int64) error {
	mCompanyTree = nil

	return d.db.Transaction(func(tx *gorm.DB) error {
		// 公司的角色要解除绑定
		if err := tx.Where("company_id = ?", id).Delete(&CompanyRoleMap{}).Error; err != nil {
			return err
		}
		// 公司的用户要删除
		if err := tx.Where("company_id = ?", id).Delete(&User{}).Error; err != nil {
			return err
		}

		//查询公司下的用户分组ID
		var groupIds []int64
		if err := tx.Model(&UserGroup{}).Where("company_id = ?", id).Select("id").Find(&groupIds).Error; err != nil {
			return err
		}

		if groupIds != nil || len(groupIds) == 0 {
			//查不到的时候，具体哪个满足？
			xlog.Debug("groupIds :", groupIds)
		} else {
			// 删除此公司的用户分组
			if err := tx.Where("company_id = ?", id).Delete(&UserGroup{}).Error; err != nil {
				return err
			}
			// 删除此公司的用户-分组映射
			if err := tx.Where("group_id in ?", groupIds).Delete(&UserGroupMap{}).Error; err != nil {
				return err
			}
		}

		// 删除公司
		if err := tx.Where("id = ?", id).Delete(&Company{}).Error; err != nil {
			return err
		}
		return nil
	})
}

// 更新
// TODO：如果修改了角色，还要考虑对 company 下所有的角色的影响
func (d *IDao) UpdateCompany(req *dto.ReqCompany) error {
	mCompanyTree = nil

	return d.db.Transaction(func(tx *gorm.DB) error {
		// dao := NewIDaoWithTx(tx)

		//删除公司-角色映射
		if err := d.DelCompanyRoleMap(req.ID); err != nil {
			return err
		}

		//添加公司-角色映射
		arr, err := string2Arr(req.Roles)
		if err != nil {
			return err
		}
		if err := d.AddCompanyRoleMap(req.ID, arr); err != nil {
			return err
		}

		//更新公司其他字段
		data := StructToMap(req)
		delete(data, "roles")
		delete(data, "logo_filename")
		return tx.Model(&Company{}).Where("id = ? ", req.ID).Updates(data).Error
	})

}

// 递归获取子公司
func findChildren(compID int64) []*Company {
	var result []*Company

	company, exists := mCompanyTree[compID]
	if !exists {
		return result
	}
	result = append(result, company)

	for _, child := range company.Children {
		arr := findChildren(child.ID)
		result = append(result, arr...)
	}
	return result
}

// 平铺的方式返回所有有权限的公司
func (d *IDao) AuthedCompany(parent_id int64) ([]*Company, error) {
	if mCompanyTree == nil {
		err := d.rebuildCompanyTree()
		if err != nil {
			return nil, err
		}
	}

	arr := findChildren(parent_id)
	if len(arr) == 0 {
		return nil, errors.New("empty")
	}

	// 拷贝
	var result []*Company
	for _, one := range arr {
		result = append(result, one)
	}

	return result, nil
}

// 列出所有下级 company,tree
func (d *IDao) TreeCompany(parent_id int64) ([]*Company, error) {
	if mCompanyTree == nil {
		err := d.rebuildCompanyTree()
		if err != nil {
			return nil, err
		}
	}

	one, ok := mCompanyTree[parent_id]
	if !ok {
		return nil, errors.New("invalid company id")
	}

	return one.Children, nil
}

// 查询所有子公司（包括子公司的子公司），分页，按照ID排序。
func (d *IDao) ListCompany(parent_id int64, pi *dto.PageInfo) ([]*Company, error) {
	var arr []*Company

	var session *gorm.DB
	if parent_id == global.COMPANY_ID_BEITHING {
		session = d.db.Model(&Company{}).Order("id ASC") //查询全部
	} else {
		session = d.db.Model(&Company{}).Where("id = ?", parent_id).Or("parent_id = ?", parent_id).Order("id ASC")
	}

	var err error
	if pi == nil {
		err = session.Find(&arr).Error
	} else {
		err = PagedFind(session, &arr, pi)
	}
	if err != nil {
		return nil, err
	}

	return arr, nil
}

func (d *IDao) GetCompanyByID(company_id int64) (*Company, error) {
	var one Company
	err := d.db.Model(&Company{}).Where("id = ?", company_id).First(&one).Error
	if err != nil {
		return nil, err
	}

	return &one, nil
}

func (d *IDao) GetCompanyByUUID(uuid string) (*Company, error) {
	var one Company
	err := d.db.Model(&Company{}).Where("uuid = ?", uuid).First(&one).Error
	return &one, err
}

// 先模糊搜索，然后判断是否合法的下级公司
func (d *IDao) QueryCompany(company_id int64, name string) ([]*Company, error) {
	var arr []*Company
	err := d.db.Model(&Company{}).Where("name like ?", "%"+name+"%").Find(&arr).Error
	if err != nil {
		return nil, err
	}

	var ret []*Company
	for _, one := range arr {
		ok, _ := d.IsParentChild(company_id, one.ID)
		if ok {
			ret = append(ret, one)
		}
	}

	return ret, nil
}

// 是不是我有权限切换的公司：原公司或者儿子公司
func (d *IDao) IsParentChild(parent_id, child_id int64) (bool, error) {
	if parent_id == 0 {
		return false, errors.New("invalid parent company ID")
	}

	//切换回去自己的原公司
	if parent_id == child_id {
		return true, nil
	}
	if mCompanyTree == nil {
		//重建公司树
		err := d.rebuildCompanyTree()
		if err != nil {
			return false, err
		}
	}
	child, ok := mCompanyTree[child_id]
	if !ok {
		return false, errors.New("invalid child company ID")
	}
	for {
		//是爸爸
		if child.ParentID == parent_id {
			return true, nil
		}
		//查到根了都没查到
		if child.ParentID == 0 {
			return false, nil
		}
		//再查询爷爷
		child = mCompanyTree[child.ParentID]
	}
}

// 查询当前公司拥有的菜单权限
func (d *IDao) GetMenusByCompanyID(company_id int64) ([]*Menu, error) {
	//查询角色
	rolesArr, err := d.GetRolesByCompanyID(company_id)
	if err != nil {
		return nil, err
	}
	role_ids := []int64{}
	for _, one := range rolesArr {
		role_ids = append(role_ids, one.ID)
	}
	return d.GetMenusByRoles(role_ids)
}

// 查询当前公司拥有的菜单权限
func (d *IDao) GetMenusByRoles(role_ids []int64) ([]*Menu, error) {

	//聚合每个角色的资源，注意去重
	var idsArr []int64
	m := make(map[int64]bool) //用于去重
	for _, one := range role_ids {
		tmpArr, err := d.GetMenuIdsByRoleID(one) // d.GetMenusByRoleID(one)
		if err != nil {
			return nil, err
		}
		//遍历，利用 map 去重，将资源ID存入 数组
		for _, id := range tmpArr {
			_, ok := m[id]
			if ok {
				continue
			}
			idsArr = append(idsArr, id)
			m[id] = true
		}
	}

	return d.ListMenu(idsArr)
}

func (d *IDao) GetCompanyArea(comp_id int64) (province_id int64, city_id int64, district_id int64, err error) {

	var one *Company
	one, err = d.GetCompanyByID(comp_id)
	if err != nil {
		return
	}
	province_id = one.ProvinceID
	city_id = one.CityID
	district_id = one.DistrictID

	return
}
