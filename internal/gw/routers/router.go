package routers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nanmu42/gzip"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	//nolint: goimports
	// import swagger handler
	// _ "github.com/go-eagle/eagle/api/http" // docs is generated by Swag CLI, you have to import it.

	"bs.com/app/internal/gw/handler"
	deviceHandler "bs.com/app/internal/gw/handler/device"
	jt808Handler "bs.com/app/internal/gw/handler/jt808"
	mqttHandler "bs.com/app/internal/gw/handler/mqtt"
	productHandler "bs.com/app/internal/gw/handler/product"
	systemHandler "bs.com/app/internal/gw/handler/system"
	"bs.com/app/internal/gw/middleware"
)

// Load loads the middlewares, routes, handlers.
func InitHttpHandler() http.Handler {
	gin.SetMode(gin.ReleaseMode)

	engine := gin.New()
	// 使用中间件
	engine.Use(gin.Recovery())
	//engine.Use(gzip.Gzip(gzip.DefaultCompression, gzip.WithExcludedExtensions([]string{".png", ".jpg"})))
	engine.Use(gzip.DefaultHandler().Gin)

	engine.Use(middleware.Logger())

	engine.Use(middleware.GinCors())

	// ----------------------------------------------------------------------------------
	// 未登陆的api,需要放jwt前面
	notLoginApiRouter(engine)

	// mqtt auth，不需要 jwt 鉴权
	mqttHandler.NewMqttGroup().Router(engine)

	// 下面的接口需要 jwt 鉴权
	engine.Use(middleware.Jwt())

	engine.Use(middleware.NoCache)
	engine.Use(middleware.Options)
	// engine.Use(middleware.Secure)
	engine.Use(middleware.RequestID())

	// 404 Handler.
	engine.NoRoute(func(context *gin.Context) {
		context.JSON(http.StatusNotFound, gin.H{"code": 404, "message": "not found"})
	})

	// swagger api docs
	// engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	// pprof router 性能分析路由
	// 默认关闭，开发环境下可以打开
	// 访问方式: HOST/debug/pprof
	// 通过 HOST/debug/pprof/profile 生成profile
	// 查看分析图 go tool pprof -http=:5000 profile
	// see: https://github.com/gin-contrib/pprof
	// pprof.Register(g)

	// HealthCheck 健康检查路由
	engine.GET("/health", healthCheck)
	// metrics router 可以在 prometheus 中进行监控
	// 通过 grafana 可视化查看 prometheus 的监控数据，使用插件6671查看
	engine.GET("/metrics", gin.WrapH(promhttp.Handler()))

	//静态文件服务
	// cfg := config.Get()
	// engine.Static("/cdn/", cfg.Misc.CDNDir)

	// app下载引导页、apk文件
	engine.Static("/cdn/app", "deploy/app")
	// 导出下载文件
	engine.Static("/cdn/export", "cdn/export")
	// 图片
	engine.Static("/cdn/images", "cdn/images")
	// firmware
	engine.Static("/cdn/firmware", "cdn/firmware")
	// tmp，一些需要下载的csv文件
	engine.Static("/cdn/tmp", "cdn/tmp")

	// pprofRouter(engine) // 调试功能，关闭

	apiRouter(engine)

	return engine
}

func notLoginApiRouter(router *gin.Engine) {
	handler.NewGroupNotLogin().Router(router)
}

func apiRouter(router *gin.Engine) {
	//路由

	// websocket
	handler.NewWebsocketGroup().Router(router)
	// user
	systemHandler.NewUserGroup().Router(router)
	// menu
	systemHandler.NewMenuGroup().Router(router)

	// role
	systemHandler.NewRoleGroup().Router(router)

	// system
	systemHandler.NewSystemGroup().Router(router)

	// device
	deviceHandler.NewDeviceGroup().Router(router)

	// jt808
	jt808Handler.NewJT808Group().Router(router)

	// product
	productHandler.NewProductGroup().Router(router)

	// buca
	// bucaHandler.NewBucaGroup().Router(router)

	// 大屏
	// handler.NewBigScreenGroup().Router(router)

	// // 项目管理
	// handler.NewProjectGroup().Router(router)

}
