package tasks

import (
	"context"
	"time"

	"bs.com/app/internal/gw/model"

	"bs.com/app/pkg/crontask"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

// robfig/cron不仅兼容了Linux标准的Crontab格式，而且可以扩展到秒级(使用option)。
type CronService struct {
	tm crontask.Timer
	model.IDao
}

// 单例
var cronInstance *CronService

// 列出所有cron 任务
func ListAllCronTask() []*crontask.TaskEntry {
	return cronInstance.tm.List()
}

// 添加cron 任务
func AddCronTaskJob(name, spec string, job interface{ Run() }) error {
	_, err := cronInstance.tm.AddTaskByJob(name, spec, job)
	return err
}

// 添加带参数cron func
func AddCronTaskFunc(name, spec string, task func(...interface{}), args ...interface{}) error {
	_, err := cronInstance.tm.AddTaskByFunc(name, spec, task, args...)
	return err
}

// 更新任务
// 1. 先删除之前的任务，2.再添加新的任务
func UpdateCronTaskJob(name, spec string, job interface{ Run() }) error {
	err := cronInstance.tm.Remove(name)
	if err != nil {
		return err
	}
	_, err = cronInstance.tm.AddTaskByJob(name, spec, job)
	return err
}

// 删除任务
func RemoveCronTask(name string) error {
	return cronInstance.tm.Remove(name)
}

func NewCronService() xwg.IService {
	cronInstance = &CronService{
		tm:   crontask.NewTimerTask(),
		IDao: *model.NewIDao(),
	}
	return cronInstance
}

func (s *CronService) Name() string {
	return "cron"
}

func (s *CronService) Run(ctx context.Context) error {

	xlog.Debug("run cronInstance")

	var err error
	var spec string

	spec = "40 * * * *" //每个小时的40分钟
	_, err = s.tm.AddTaskByFunc("test-everyh40m", spec, func(args ...interface{}) {
		// xlog.Info("every hour@40 ... ", "time_now", time.Now().Format("2006-01-02 15:04:05"))
	})
	if err != nil {
		return err
	}

	spec = "*/5 * * * * " //每隔5分钟执行一次任务
	_, err = s.tm.AddTaskByFunc("test-5m", spec, func(args ...interface{}) {
		xlog.Debug("task test every 5m ... ", "time_now", time.Now().Format("2006-01-02 15:04:05"))
		// 测试
		// s.cleanOldPacketLog()
	})
	if err != nil {
		return err
	}

	spec = "0 */2 * * * " //每隔2小时执行一次任务
	_, err = s.tm.AddTaskByFunc("test-2h", spec, func(args ...interface{}) {
		xlog.Info("every 2h ... ", "time_now", time.Now().Format("2006-01-02 15:04:05"))
	})
	if err != nil {
		return err
	}

	spec = "0 0 * * * " //每天 0 点执行一次任务
	_, err = s.tm.AddTaskByFunc("test-everyday", spec, func(args ...interface{}) {
		xlog.Info("every day@23h ... ", "time_now", time.Now().Format("2006-01-02 15:04:05"))
	})
	if err != nil {
		return err
	}

	spec = "0 0 * * * " //每天 0 点执行一次sim卡查询
	_, err = s.tm.AddTaskByFunc("get-simcard-status-everyday", spec, func(args ...interface{}) {
		// 获取sim卡的是否正常
		// xlog.Info("get sim card status every day@23h ... ", time.Now().Format("2006-01-02 15:04:05"))
		// 仅检查已经激活、但是离线的设备
		// s.CheckSimStatus()
		xlog.Info("get sim card status every ............... NO") //先不执行流量卡任务
	})
	if err != nil {
		return err
	}

	// 启动定时任务器
	s.tm.Start()

	<-ctx.Done()

	s.tm.Close() //close all cron task

	return nil
}
