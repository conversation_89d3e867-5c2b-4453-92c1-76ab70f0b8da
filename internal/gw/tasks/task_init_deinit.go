package tasks

import (
	"context"

	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

// gw 进程开启需要执行的 pre-init 和 结束之前需要执行的 post-deinit
type InitDeinit struct {
	*model.IDao
}

func NewInitDeinit() xwg.IService {
	return &InitDeinit{
		IDao: model.NewIDao(),
	}
}

func (i *InitDeinit) Name() string {
	return "init-deinit"
}

// 开机初始化执行
func (i *InitDeinit) Run(ctx context.Context) error {
	xlog.Debug("----------------------------------- do pre-init")
	// pre-init
	// 初始化区域缓存
	if err := i.InitAreaTree(); err != nil {
		xlog.Warn("init area tree", "err", err)
	}

	<-ctx.Done()

	xlog.Debug("----------------------------------- do post-deinit")

	xlog.Debug("----------------------------------- deinit done")

	return nil
}
