package tasks

import (
	"context"
	"time"

	"bs.com/app/pkg/xlog"
	"github.com/hibiken/asynq"
)

const (
	TypeAsynqExample = "asynq.raw"
)

func StartTaskExample() error {
	//return asynqMan.NewTask(TypeAsynqExample, []byte(time.Now().String()), asynq.Timeout(time.Minute*10))
	return asynqMan.NewTask(TypeAsynqExample, []byte(time.Now().String()))
}

func HandleExample(ctx context.Context, t *asynq.Task) error {
	xlog.Info("asynq: raw")
	xlog.Info("task payload : ", string(t.Payload()))
	//other
	return nil
}
