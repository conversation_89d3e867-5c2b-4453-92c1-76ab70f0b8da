package tasks

import (
	"context"

	"bs.com/app/global"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"bs.com/app/pkg/xwg"
)

type eventHandler struct {
	*model.IDao
}

func NewEventHandler() xwg.IService {
	return &eventHandler{
		IDao: model.NewIDao(),
	}
}

func (e *eventHandler) Name() string {
	return "event-handler"
}

func (e *eventHandler) Run(ctx context.Context) error {
	xlog.Debug("run event handler")

	//添加event handler
	global.G.Ebus.Subscribe("test", func(value interface{}) {
		xlog.Info("test handler by event bus,value: ", xutils.JSONString(value))
	})

	<-ctx.Done()

	return nil
}
