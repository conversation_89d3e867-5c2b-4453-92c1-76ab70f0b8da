package tasks

import (
	"context"
	"fmt"
	"time"

	"bs.com/app/config"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
	"github.com/hibiken/asynq"
)

type AsynqMan struct {
	server    *asynq.Server
	client    *asynq.Client
	scheduler *asynq.Scheduler

	mux           *asynq.ServeMux
	config        asynq.Config
	redisOpt      asynq.RedisClientOpt
	schedulerOpts *asynq.SchedulerOpts

	model.IDao
}

// TODO:可以通过二级路由的方案，支持动态添加异步任务，好处就是代码组织更好看
var asynqMan *AsynqMan

func NewAsynqMan() xwg.IService {
	xlog.Debug("NewAsynqMan")
	redisAddr, password := config.GetRedisConfig()
	srv := &AsynqMan{
		redisOpt: asynq.RedisClientOpt{
			Addr:     redisAddr,
			Password: password,
			DB:       5,
		},
		config: asynq.Config{
			Concurrency: 10,
			// Optionally specify multiple queues with different priority.
			Queues: map[string]int{
				"critical": 6,
				"default":  3,
				"low":      1,
			},
			LogLevel: asynq.WarnLevel,
			RetryDelayFunc: func(n int, e error, t *asynq.Task) time.Duration {
				return 10 * time.Second
			},
		},
		schedulerOpts: &asynq.SchedulerOpts{},
		mux:           asynq.NewServeMux(),
	}

	srv.scheduler = asynq.NewScheduler(srv.redisOpt, srv.schedulerOpts)
	srv.client = asynq.NewClient(srv.redisOpt)
	srv.server = asynq.NewServer(srv.redisOpt, srv.config)

	srv.IDao = *model.NewIDao()
	//单例变量
	asynqMan = srv

	return srv
}

func (s *AsynqMan) Name() string {
	return "asynq-man"
}

// NewTask enqueue a new task
func (s *AsynqMan) NewTask(typeName string, payload []byte, opts ...asynq.Option) error {
	xlog.Infof("new task: %s, %s", typeName, string(payload))
	task := asynq.NewTask(typeName, payload)

	//使用 critical 队列
	arr := []asynq.Option{
		asynq.Queue("critical"),
	}
	arr = append(arr, opts...)
	info, err := s.client.Enqueue(task, arr...)
	if err != nil {
		xlog.Errorf("[asynq] [%s] Enqueue failed: %s", typeName, err.Error())
		return err
	}
	xlog.Infof("[asynq] enqueued task: id=%s queue=%s", info.ID, info.Queue)

	return nil
}

// NewPeriodicTask enqueue a new crontab task
func (s *AsynqMan) NewPeriodicTask(cronSpec, typeName string, payload []byte, opts ...asynq.Option) error {
	task := asynq.NewTask(typeName, payload)

	entryID, err := s.scheduler.Register(cronSpec, task, opts...)
	if err != nil {
		xlog.Errorf("[asynq] [%s] Enqueue failed: %s", typeName, err.Error())
		return err
	}
	xlog.Debugf("[asynq] registered an entry: id=%q", entryID)

	return nil
}

// AsynqLoggingMiddleware 记录任务日志中间件
func (s *AsynqMan) loggingMiddleware(h asynq.Handler) asynq.Handler {
	return asynq.HandlerFunc(func(ctx context.Context, t *asynq.Task) error {
		start := time.Now()
		xlog.Info("asynq start: " + t.Type())
		err := h.ProcessTask(ctx, t)
		if err != nil {
			_ = s.MessageCreateErr("任务执行失败", fmt.Sprintf("任务名字：%s，错误提示：%s，耗时：%s", t.Type(), err.Error(), time.Since(start)))
			return err
		}
		xlog.Infof("asynq end : %s, Elapsed Time = %s", t.Type(), time.Since(start))
		return nil
	})
}

func (s *AsynqMan) Run(ctx context.Context) error {
	var err error

	//添加 ota 相关异步任务
	// mux maps a type to a handler
	s.mux = asynq.NewServeMux()
	s.mux.Use(s.loggingMiddleware)

	s.mux.HandleFunc(TypeAsynqExample, HandleExample)

	if err = s.server.Start(s.mux); err != nil {
		xlog.Errorf("[asynq] asynq server run failed: %s", err.Error())
		return err
	}

	if err = s.scheduler.Start(); err != nil {
		xlog.Errorf("[asynq] asynq scheduler start failed: %s", err.Error())
		return err
	}

	<-ctx.Done()

	_ = s.Stop() //停止

	return nil
}

func (s *AsynqMan) Stop() error {
	xlog.Info("[asynq] server stopping")
	if s.client != nil {
		_ = s.client.Close()
		s.client = nil
	}

	if s.server != nil {
		s.server.Stop()
		s.server.Shutdown()
		s.server = nil
	}

	if s.scheduler != nil {
		s.scheduler.Shutdown()
		s.scheduler = nil
	}

	return nil
}
