package product

import (
	"errors"
	"fmt"
	"log/slog"
	"os"

	"bs.com/app/config"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 添加firmware
type reqFirmwareCreate struct {
	ProductID string `json:"product_id"        form:"product_id"  binding:"required"` // 产品id
	Version   string `json:"version"           form:"version"     binding:"required"` // 版本，格式的版本号，x.y.z
	Group     string `json:"group"             form:"group"       binding:"required"` // 分组
	Filename  string `json:"filename"          form:"filename"`                       // 文件名
	Size      int64  `json:"size"              form:"size"        binding:"required"` // size
	Desc      string `json:"desc"              form:"desc"`                           // 描述
}

// 生成差分文件数据库表：
type reqFirmwareDiffCreate struct {
	FirmwareID string `json:"firmware_id"       form:"firmware_id"     binding:"required"` // 全量包的软件id
	NewVersion string `json:"new_version"       form:"new_version"     binding:"required"` // semVer 格式的版本号，x.y.z
	OldVersion string `json:"old_version"       form:"old_version"     binding:"required"` // semVer 格式的版本号，x.y.z

	//文件信息：文件大小、文件保存路径、文件下载地址、文件签名、文件描述
	FileID    string `json:"file_id"         form:"-"` //软件uuid:设备固件升级到时候，统一传递数字ID？？？
	Size      int64  `json:"size"            form:"-"` //文件大小
	LocalPath string `json:"local_path"      form:"-"` //本地路径： host/pid-version.suffix
	HttpPath  string `json:"http_path"       form:"-"` //oss 下载路径
	MD5       string `json:"md5"             form:"-"` //文件校验码
	CRC16     uint16 `json:"crc16"           form:"-"` //文件校验码
	Desc      string `json:"desc"            form:"-"` //描述
}

func (g *GroupProduct) addFirmware(c *gin.Context) {
	req := reqFirmwareCreate{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	//参数检查
	prod, err := g.ProductRepo.FindOne(req.ProductID)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_INVALID_PRODUCT, err.Error())
		return
	}

	//查看最新固件版本:产品、分类，针对某个产品的某个类型的软件，查找最新版本
	fw, err := g.FirmwareQueryLatest(req.ProductID)
	if err != nil || fw == nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			xlog.Info("not found, first firmware.....")
		} else {
			g.ResponseError(c, xcode.ERRCODE_DAO, "query latest failed")
			return
		}
	} else {
		//如果已经有对应的软件（匹配 categoryID），则版本必须递增
		ok, e1 := xutils.VersionLeftLessThanRight(req.Version, fw.Version)
		if e1 != nil {
			g.ResponseError(c, xcode.ERRCODE_FIRMWARE_INVALID_VERSION, "version compare failed")
			return
		}
		if ok {
			g.ResponseError(c, xcode.ERRCODE_FIRMWARE_LOWWER, "version lower")
			return
		}
	}
	// 检查size, 小于 500M
	if req.Size > 500*1024*1024 {
		xlog.Warn("firmware size > 500 MB")
		g.ResponseError(c, xcode.ERRCODE_FIRMWARE_OVER_SIZE, "firmware size > 500 MB")
		return
	}

	//保存上传的文件  (前端仅填充 filename，size)
	swFile, err := c.FormFile("file")
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, "file upload failed")
		return
	}

	//保存为新的文件名字:pid-version-filename
	//newName := fmt.Sprintf("sw-%d-%d-%s-%s", req.ProductID, req.CategoryID, req.Version, req.Filename)
	// uuid := fmt.Sprintf("%d-%s", req.ProductID, req.Version) //软件唯一ID
	fileID := xutils.UUIDSnowFlake()
	newName := fmt.Sprintf("full-%s.bin", fileID)
	swPath := config.Get().Misc.PathFirmware + "/" + newName

	xlog.Info("new firmware file save path: ", swPath)
	// save file
	err = c.SaveUploadedFile(swFile, swPath)
	if err != nil {
		xlog.Error("save file err:", err.Error())
		g.ResponseError(c, xcode.ERRCODE_IO, "save file failed")
		return
	}

	// 文件 size
	fStat, err := os.Stat(swPath)
	if err != nil {
		xlog.Error("stat file failed:" + swPath)
		g.ResponseError(c, xcode.ERRCODE_IO, "stat file failed")
		return
	}
	req.Size = fStat.Size()

	//check file
	var md5 string
	md5, err = xutils.FileMd5(swPath)
	if err != nil {
		xlog.Error("file md5 failed:" + swPath)
		g.ResponseError(c, xcode.ERRCODE_IO, "file md5 failed")
		return
	}
	//crc16
	var crc16 uint16
	crc16, err = xutils.FileCRC16(swPath)
	if err != nil {
		xlog.Error("file crc16 failed:" + swPath)
		g.ResponseError(c, xcode.ERRCODE_IO, "file crc16 failed")
		return
	}

	//把固件上传到OSS
	uid := g.GetUID(c)
	http_url, err := g.OssUpload(uid, swPath)
	if err != nil {
		xlog.Warn("file upload to OSS failed:", slog.Any("err", err))
	}

	//文件信息
	file := model.File{
		FileID:   fileID,
		Name:     req.Filename,
		Size:     req.Size,
		HttpPath: http_url,
		MD5:      md5,
		CRC16:    crc16,
		Desc:     req.Desc,
	}

	// 没用事务：先存文件，再存固件
	err = g.FileRepo.Insert(&file)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}

	//保存新固件信息
	err = g.FirmwareRepo.Insert(&model.Firmware{
		FirmwareID: xutils.UUIDSnowFlake(),
		ProductID:  req.ProductID,
		Version:    req.Version,
		Group:      req.Group,
		FileID:     fileID,
		Desc:       req.Desc,
	})
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}

	company_id := g.GetCompanyID(c)
	_ = g.AddNewLog(company_id, uid, "添加固件", fmt.Sprintf("product :%d, group : %s, %s", req.ProductID, req.Group, newName))

	if prod.IsDiff {
		// TODO:生成差分包的异步任务
	}
	g.ResponseOK(c)
}

func (g *GroupProduct) updateFirmware(c *gin.Context) {

	type reqFirmwareUpdate struct {
		FirmwareID string `json:"firmware_id"     form:"firmware_id" binding:"required"` //固件ID
		Group      string `json:"group"           form:"group"`
		Desc       string `json:"desc"            form:"desc"`
		Status     bool   `json:"status"          form:"status"`
	}

	req := reqFirmwareUpdate{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, "")
		return
	}

	//更新固件
	err := g.FirmwareRepo.Update(&model.Firmware{
		FirmwareID: req.FirmwareID,
		Group:      req.Group,
		Desc:       req.Desc,
		Status:     req.Status,
	})
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseOK(c)
}

func (g *GroupProduct) deleteFirmware(c *gin.Context) {
	type reqFirmwareDelete struct {
		FirmwareID string `json:"firmware_id"     form:"firmware_id" binding:"required"` //固件ID
	}

	req := reqFirmwareDelete{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, "")
		return
	}

	//删除固件
	err := g.FirmwareRepo.DeleteByFilter(model.FirmwareFilter{
		FirmwareID: req.FirmwareID,
	})
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseOK(c)
}

func (g *GroupProduct) getFirmwareOne(c *gin.Context) {
	type reqFirmwareOne struct {
		FirmwareID string `json:"firmware_id"     form:"firmware_id" binding:"required"` //固件ID
	}

	req := reqFirmwareOne{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, "")
		return
	}
	//查找固件
	fw, err := g.FirmwareRepo.FindOne(req.FirmwareID)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseData(c, fw)
}

func (g *GroupProduct) listFirmware(c *gin.Context) {
	req := struct {
		dto.PageInfo
		ProductID string `json:"product_id"     form:"product_id"` // 产品id
		Group     string `json:"group"          form:"group"`
		Status    bool   `json:"status"         form:"status"`
	}{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, "")
		return
	}
	//查找固件
	filter := model.FirmwareFilter{
		ProductID: req.ProductID,
		Group:     req.Group,
		Status:    req.Status,
	}

	fw, err := g.FirmwareRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseData(c, fw)
}

func (g *GroupProduct) listFirmwareDiff(c *gin.Context) {
	req := struct {
		dto.PageInfo
		FirmwareID string `json:"firmware_id"     form:"firmware_id" binding:"required"` //固件ID
	}{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, "")
		return
	}
	//查找固件
	filter := model.FirmwareDiffFilter{
		FirmwareID: req.FirmwareID,
	}
	fw, err := g.FirmwareDiffRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseData(c, fw)
}

// 提供 hardwareI，查询此硬件的最新可用固件
func (g *GroupProduct) listFirmwareForHardware(c *gin.Context) {
	req := struct {
		HardwareID string `json:"hardware_id"     form:"hardware_id" binding:"required"` //硬件ID
	}{
		HardwareID: c.Query("hardware_id"),
	}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, "")
		return
	}
	hw, err := g.HardwareRepo.FindOne(req.HardwareID)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "invalid hardware")
		return
	}
	//查找固件
	filter := model.FirmwareFilter{
		ProductID: hw.ProductID,
		Group:     hw.Group,
		Status:    true,
	}
	fw, err := g.FirmwareRepo.FindOneByFilter(filter)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseData(c, fw)
}
