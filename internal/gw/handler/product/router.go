package product

import (
	"bs.com/app/internal/dm"
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/model"
	"github.com/gin-gonic/gin"
)

type GroupProduct struct {
	base.BaseController
	model.IDao

	// 产品管理
	model.ProductRepo
	model.FirmwareRepo
	model.FirmwareDiffRepo
	model.HardwareRepo
	model.FileRepo

	// dm
	dman *dm.DeviceMan
}

func NewProductGroup() *GroupProduct {
	return &GroupProduct{
		BaseController: base.BaseController{},
		IDao:           *model.NewIDao(),
		// 产品管理
		ProductRepo:      *model.NewProductRepo(),
		FirmwareRepo:     *model.NewFirmwareRepo(),
		FirmwareDiffRepo: *model.NewFirmwareDiffRepo(),
		HardwareRepo:     *model.NewHardwareRepo(),

		// 文件管理
		FileRepo: *model.NewFileRepo(),

		// dm
		dman: dm.OneDM,
	}
}

func (g *GroupProduct) Router(r *gin.Engine) {
	dev := r.Group("/api/product")
	{
		// product
		dev.GET("/product_list", g.listProducts)
		dev.GET("/product_info", g.productInfo)
		dev.POST("/product_add", g.addProduct)
		dev.POST("/product_update", g.updateProduct)
		dev.GET("/product_delete", g.deleteProduct)

		// firmware
		dev.POST("/firmware_add", g.addFirmware)                     // 添加一个固件
		dev.POST("/firmware_update", g.updateFirmware)               // 更新一个固件
		dev.GET("/firmware_delete", g.deleteFirmware)                // 删除一个固件
		dev.GET("/firmware_one", g.getFirmwareOne)                   // 查询一个固件详细信息
		dev.GET("/firmware_list", g.listFirmware)                    // 查询固件列表，分页
		dev.GET("/firmware_list_diff", g.listFirmwareDiff)           //	查询固件差分文件列表
		dev.GET("/firmware_for_hardware", g.listFirmwareForHardware) // 提供 hardwareID，返回可用的最新固件
	}
}
