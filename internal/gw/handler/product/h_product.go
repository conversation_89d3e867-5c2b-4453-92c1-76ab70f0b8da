package product

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
)

// 统一所有更新的操作，都是用表内唯一ID
type reqProductCreate struct {
	Model  string         `form:"model"           json:"model"            binding:"required"`
	Name   string         `form:"name"            json:"name"             binding:"required"`
	Desc   string         `form:"desc"            json:"desc"`
	Meta   map[string]any `form:"meta"            json:"meta"`
	IsDiff bool           `form:"is_diff"         json:"is_diff"`
}

type reqProductUpdate struct {
	ProductID string         `form:"product_id"    json:"product_id"     binding:"required"`
	Name      string         `form:"name"          json:"name,omitempty"`
	Desc      string         `form:"desc"          json:"desc,omitempty"`
	Meta      map[string]any `form:"meta"          json:"meta"`
	IsDiff    bool           `form:"is_diff"       json:"is_diff"`
}

// 查询所有产品
func (g *GroupProduct) listProducts(c *gin.Context) {
	req := struct {
		dto.PageInfo
		Name      string `json:"name"`
		ProductID string `json:"product_id"`
		Model     string `json:"model"`
	}{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()
	companyID := int64(0)
	if !g.IsSuper(c) {
		companyID = g.GetCompanyID(c)
	}

	filter := model.ProductFilter{
		Name:      req.Name,
		ProductID: req.ProductID,
		Model:     req.Model,
		CompanyID: companyID,
	}
	productList, err := g.ProductRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	g.ResponsePage(c, &req.PageInfo, productList, len(productList))
}

// 根据 product ID 查询
func (g *GroupProduct) productInfo(c *gin.Context) {
	req := struct {
		ProductID string `form:"product_id" json:"product_id" binding:"required"`
	}{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	companyID := int64(0)
	if !g.IsSuper(c) {
		companyID = g.GetCompanyID(c)
	}

	product, err := g.ProductRepo.FindOne(req.ProductID)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	// 如果不是超级管理员，需要验证产品所属公司
	if !g.IsSuper(c) && product.CompanyID != companyID {
		g.ResponseError(c, xcode.ERRCODE_NOT_PERMISSION, "无权访问该产品")
		return
	}

	g.ResponseData(c, product)
}

func (g *GroupProduct) addProduct(c *gin.Context) {
	req := reqProductCreate{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	// 生成产品ID和产品鉴权码
	productID := xutils.RandString(10)
	productCode := xutils.RandString(16)

	// 创建产品
	product := &model.Product{
		CompanyID:   g.GetCompanyID(c),
		ProductID:   productID,
		ProductCode: productCode,
		Model:       req.Model,
		Name:        req.Name,
		Desc:        req.Desc,
		Meta:        req.Meta,
		IsDiff:      req.IsDiff,
	}

	// 保存到数据库
	err := g.ProductRepo.Insert(product)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	g.ResponseData(c, product)
}

func (g *GroupProduct) updateProduct(c *gin.Context) {
	req := reqProductUpdate{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	// 验证产品是否存在
	product, err := g.ProductRepo.FindOne(req.ProductID)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	// 如果不是超级管理员，需要验证产品所属公司
	companyID := g.GetCompanyID(c)
	if !g.IsSuper(c) && product.CompanyID != companyID {
		g.ResponseError(c, xcode.ERRCODE_NOT_PERMISSION, "无权更新该产品")
		return
	}

	// 更新产品信息
	updateProduct := &model.Product{
		ProductID: req.ProductID,
		Name:      req.Name,
		Desc:      req.Desc,
		Meta:      req.Meta,
		IsDiff:    req.IsDiff,
	}

	err = g.ProductRepo.Update(updateProduct)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	g.ResponseOK(c)
}

func (g *GroupProduct) deleteProduct(c *gin.Context) {
	req := struct {
		ProductID string `form:"product_id" json:"product_id" binding:"required"`
	}{}
	if err := c.ShouldBind(&req); err != nil {
		g.ResponseError(c, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	// 验证产品是否存在
	product, err := g.ProductRepo.FindOne(req.ProductID)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	// 如果不是超级管理员，需要验证产品所属公司
	companyID := g.GetCompanyID(c)
	if !g.IsSuper(c) && product.CompanyID != companyID {
		g.ResponseError(c, xcode.ERRCODE_NOT_PERMISSION, "无权删除该产品")
		return
	}

	// 删除产品前，可以添加检查是否有关联的硬件设备
	hardwareFilter := model.HardwareFilter{
		ProductID: req.ProductID,
	}
	count, err := g.HardwareRepo.CountByFilter(hardwareFilter)
	if err != nil {
		xlog.Error("查询产品关联硬件失败: %v", err)
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	if count > 0 {
		g.ResponseError(c, xcode.ERRCODE_CAN_NOT_DEL, "该产品下存在硬件设备，无法删除")
		return
	}

	// 删除产品
	err = g.ProductRepo.Delete(req.ProductID)
	if err != nil {
		g.ResponseError(c, xcode.ERRCODE_DAO, err.Error())
		return
	}

	g.ResponseOK(c)
}
