package handler

import (
	"fmt"
	"testing"

	"bs.com/app/pkg/xutils"
)

// app_key :  d1tg4jnpw8cn
// nonce :  QI87mQSURlaIBkC7
// timestamp :  1735785551
// sign :  8453c0ffe16bdd2c1d5392d1bbfb3656ef75b8acc3705481185fab686f4c6846

func Test_north_auth(t *testing.T) {
	appKey := "d1tg4jnpw8cn"
	appSecret := "FW4rDB8vr0ofHXvWsXTeO8LxAPDCwZvz"
	nonce := "QI87mQSURlaIBkC7"
	timestamp := "1735785551"
	sign := "8453c0ffe16bdd2c1d5392d1bbfb3656ef75b8acc3705481185fab686f4c6846"

	// 检查签名
	content := fmt.Sprintf("appkey=%s,nonce=%s,timestamp=%s", appKey, nonce, timestamp)
	result := xutils.HmacSha256Hex(content, []byte(appSecret))

	if sign == result {
		fmt.Println("sign ok")
	} else {
		fmt.Println("sign error")
	}
}
