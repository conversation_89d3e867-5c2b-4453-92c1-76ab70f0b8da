package base

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
)

//按照分层设计的原则，接口的处理分为三层：controller、service、dao。
//前期为了简化，仅实现 controller 和 dao层，后期如果需要封装RPC接口，再拆解出service。

type BaseController struct {
}

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Payload interface{} `json:"payload,omitempty"`
}

func (b *BaseController) GetRemoteIP(ctx *gin.Context) string {
	ip := ctx.Request.Header.Get("X-Real-IP")
	if ip == "" {
		ip = ctx.Request.Header.Get("X-Forwarded-For")
	}
	return ip
}
func (b *BaseController) GetUID(ctx *gin.Context) int64 {
	return ctx.GetInt64("uid")
}
func (b *BaseController) GetCompanyID(ctx *gin.Context) int64 {
	return ctx.GetInt64("company_id")
}

func (b *BaseController) GetPhone(ctx *gin.Context) string {
	return ctx.GetString("phone")
}
func (b *BaseController) IsSuper(ctx *gin.Context) bool {
	phone := ctx.GetString("phone")
	return phone == "18665991286"
}

func (b *BaseController) GetAreaID(ctx *gin.Context) (int64, int64, int64) {
	return ctx.GetInt64("province_id"), ctx.GetInt64("city_id"), ctx.GetInt64("district_id")
}

// 普通返回
func (b *BaseController) ResponseOK(ctx *gin.Context) {
	resp := &Response{
		Code:    xcode.Success.Code(),
		Message: "success",
	}

	ctx.JSON(http.StatusOK, resp)
}

// 普通返回
func (b *BaseController) ResponseData(ctx *gin.Context, data interface{}) {
	resp := &Response{
		Code:    xcode.Success.Code(),
		Message: "success",
		Payload: data,
	}

	ctx.JSON(http.StatusOK, resp)
}

// 分页查找的返回值
type pageResult struct {
	*dto.PageInfo
	List interface{} `json:"list"`
	Len  int         `json:"len"`
}

// 可以通过反射，获取最后这一页的 list 的长度。
func (b *BaseController) ResponsePage(ctx *gin.Context, page *dto.PageInfo, arr interface{}, length int) {
	data := pageResult{
		PageInfo: page,
		List:     arr,
		Len:      length,
	}
	if length == 0 {
		data.List = make([]string, 0)
	}
	resp := &Response{
		Code:    xcode.Success.Code(), // 默认错误码
		Payload: data,
	}
	ctx.JSON(http.StatusOK, resp)
}

// 返回列表，不分页
type listResult struct {
	List interface{} `json:"list"`
	Len  int         `json:"len"`
}

func (b *BaseController) ResponseList(ctx *gin.Context, arr interface{}, length int) {
	data := listResult{
		List: arr,
		Len:  length,
	}
	if length == 0 {
		data.List = make([]string, 0)
	}
	resp := &Response{
		Code:    xcode.Success.Code(), // 默认错误码
		Payload: data,
	}
	ctx.JSON(http.StatusOK, resp)
}

// 错误返回
func (b *BaseController) ResponseErrorCode(ctx *gin.Context, code *xcode.ErrCode) {
	resp := &Response{
		Code:    code.Code(),
		Message: code.Msg(),
	}
	//xlog.Errorf("code:%d, message: %s", code, resp.Message)
	ctx.JSON(http.StatusOK, resp)
}

// 错误返回
// ResponseError(ctx, error_code)
// ResponseError(ctx, error_code, error)
// ResponseError(ctx, error_code, message)
func (b *BaseController) ResponseError(ctx *gin.Context, code *xcode.ErrCode, errMsg string) {
	resp := &Response{
		Code:    code.Code(),
		Message: code.Msg(),
		Payload: nil,
	}

	if errMsg != "" {
		resp.Message = resp.Message + ": " + errMsg
	}
	xlog.Errorf("code:%d, message: %s", resp.Code, resp.Message)
	ctx.JSON(http.StatusOK, resp)
}
