package handler

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"github.com/gin-gonic/gin"
)

type GroupRole struct {
	base.BaseController
	model.IDao
}

func NewRoleGroup() *GroupRole {
	return &GroupRole{
		BaseController: base.BaseController{},
		IDao:           *model.NewIDao(),
	}
}

func (g *GroupRole) Router(r *gin.Engine) {
	role := r.Group("/api/role")
	{
		role.GET("/get_role_list", g.getRoleList)
		role.POST("/add_role", g.addRole)
		role.POST("/update_role", g.updateRole)
		role.POST("/delete_role", g.deleteRole)

		//
		role.GET("/get_role_menu_list", g.getRoleMenuList)
	}
}

func (g *GroupRole) getRoleList(ctx *gin.Context) {
	req := struct {
		dto.PageInfo

		Title string `form:"title"      json:"title"`
	}{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()

	companyID := g.GetCompanyID(ctx)
	roleList, err := g.QueryRole(companyID, req.Title)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	xlog.Debug("role list:", roleList)
	g.ResponsePage(ctx, &req.PageInfo, roleList, len(roleList))
}

func (g *GroupRole) addRole(ctx *gin.Context) {
	req := dto.ReqRole{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	companyID := g.GetCompanyID(ctx)
	xlog.Debug("compan id:", "id", companyID)
	err = g.AddUpdateRole(companyID, &req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupRole) updateRole(ctx *gin.Context) {
	req := dto.ReqRole{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	companyID := g.GetCompanyID(ctx)
	err = g.AddUpdateRole(companyID, &req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupRole) deleteRole(ctx *gin.Context) {
	req := struct {
		ID int64 `form:"id"  json:"id"  binding:"required"`
	}{}

	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	// 当角色有其他公司绑定的时候，拒绝删除
	comArr, err := g.GetCompanyByRoleID(req.ID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	if len(comArr) > 1 {
		//只可关联创建者公司
		g.ResponseError(ctx, xcode.ERRCODE_CAN_NOT_DEL, "")
		return
	}

	//数据库中删除role
	err = g.DelRole(req.ID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseOK(ctx)
}

func (g *GroupRole) getRoleMenuList(ctx *gin.Context) {
	req := struct {
		RoleID int64 `form:"id"  json:"id"  binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}
	result, err := g.GetMenusByRoles([]int64{req.RoleID})
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseList(ctx, result, len(result))
}
