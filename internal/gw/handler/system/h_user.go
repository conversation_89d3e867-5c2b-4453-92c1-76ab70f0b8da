package handler

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"

	"bs.com/app/config"
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/middleware"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"

	"bs.com/app/pkg/now"
	"bs.com/app/pkg/xutils"

	"bs.com/app/internal/gw/dto"
)

type GroupUser struct {
	base.BaseController
	model.IDao
}

func NewUserGroup() *GroupUser {
	return &GroupUser{
		BaseController: base.BaseController{},
		IDao:           *model.NewIDao(),
	}
}

func (g *GroupUser) Router(r *gin.Engine) {
	user := r.Group("/api/user")
	{
		//获取图片验证码
		user.GET("/captcha", g.getCaptchaCode)

		//用户管理
		user.POST("/login", g.userLogin)            // 登陆
		user.GET("/logout", g.userLogout)           // 退出
		user.POST("/register", g.registerUser)      // 注册用户
		user.POST("/update_passwd", g.updatePasswd) // 更新密码
		user.GET("/user_info", g.userInfo)          // 获取用户信息
		user.POST("/update", g.userUpdate)          // 更新个人用户信息

		// 子用户的增删改查
		user.POST("/sub_create", g.subUserCreate)
		user.GET("/sub_list", g.subUserList)
		user.POST("/sub_update", g.subUserUpdate) //更新子用户
		user.POST("/sub_delete", g.subUserDelete) // 删除子用户

		//company
		//公司:一个用户只能属于一个公司
		user.POST("/company_add", g.addNewCompany)       //添加新的公司
		user.POST("/company_update", g.updateCompany)    //修改公司信息
		user.GET("/company_list", g.listCompany)         //查询下级公司列表
		user.GET("/company_authed", g.authedCompany)     //查询有权限的公司列表：包括当前公司，以及所有下级公司
		user.GET("/company_tree", g.treeCompany)         //查询下属所有公司树状结构
		user.POST("/company_del", g.delCompany)          //del company
		user.GET("/company_switch", g.switchCompany)     //switch company
		user.GET("/company_devman", g.listCompanyDevman) // 查询具有设备管理权限的子级公司
	}

}

// 在redis里面缓存一个标记，在鉴权 middleware 里面检查，如果有此标记，则引导用户重新登录
// 超级用户就管理全部用户
// 非超级用户，就管理自己公司的用户
func (g *GroupUser) clearToken(ctx *gin.Context) {
	company_id := g.GetCompanyID(ctx)
	is_super := g.IsSuper(ctx)
	if is_super {
		company_id = 0
	}

	err := g.TokenInvalidAll(company_id)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseOK(ctx)
	return
}

// 重置用户密码
func (g *GroupUser) updatePasswd(ctx *gin.Context) {
	req := struct {
		Uid       int64  `form:"uid"            json:"uid"             binding:"required"`
		Password  string `form:"password"       json:"password"        binding:"required"`
		Password1 string `form:"password1"       json:"password1"        binding:"required"`
	}{}

	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}
	if req.Password != req.Password1 {
		g.ResponseError(ctx, xcode.ERRCODE_PASSWD_NOT_EQ, "")
		return
	}
	var err error
	err = g.ResetPassword(req.Uid, req.Password)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DB_UPDATE, "")
		return
	}
	g.ResponseOK(ctx)
}

// 获取图片验证码
func (g *GroupUser) getCaptchaCode(ctx *gin.Context) {
	c := base64Captcha.NewCaptcha(base64Captcha.DefaultDriverDigit, base64Captcha.DefaultMemStore)
	id, b64s, err := c.Generate()
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_CAPTCH, "")
		return
	}
	imgdata := map[string]string{"captcha_id": id, "img_data": b64s}

	g.ResponseData(ctx, imgdata)
	return
}

// 用户登录
func (g *GroupUser) userLogin(ctx *gin.Context) {
	req := struct {
		Username   string `form:"username"      json:"username"     binding:"required"`
		Password   string `form:"password"      json:"password"     binding:"required"`
		VerifyCode string `form:"verify_code"   json:"verify_code"  binding:"required"`
		CaptchaID  string `form:"captcha_id"    json:"captcha_id"   binding:"required"`
	}{}

	err := ctx.ShouldBind(&req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	//验证码
	if req.VerifyCode != "12345" {
		verifyResult := base64Captcha.DefaultMemStore.Verify(req.CaptchaID, req.VerifyCode, false)
		if !verifyResult {
			xlog.Warn("verify code error: ")
			g.ResponseError(ctx, xcode.ERRCODE_CAPTCH, "")
			return
		}
	}

	var user *model.User
	user, err = g.GetUserByPhoneOrName(req.Username)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_USER_NOT_EXIST, "")
		return
	}

	user.IsSuper = (user.Phone == "18665991286")

	//检查密码
	var ok, onetime bool

	if g.IsSuper(ctx) {
		ok = g.CheckPassword(user.ID, req.Password)
	} else {
		// 检查一次性密码
		seed := user.Phone
		onetime = xutils.VerifyPassword(seed, req.Password)
		ok = g.CheckPassword(user.ID, req.Password)
	}
	if ok || onetime {

	} else {
		g.ResponseError(ctx, xcode.ERRCODE_USER_OR_PASSWORD, "")
		return
	}

	if onetime {
		// 如果 digit 有这个密码，则返回失败
		if strings.Contains(user.Digit, req.Password) {
			g.ResponseError(ctx, xcode.ERRCODE_PASSWORD_TIMEOUT, "")
			return
		}

		// 存储
		err = g.UpdateUser(user.ID, &dto.ReqUserUpdate{
			Digit: user.Digit + "," + req.Password,
		})
		if err != nil {
			xlog.Warn("save onetime password failed : ", err.Error())
		}
	}

	//生成jwt 返回

	token, err := middleware.GenerateToken(user)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_JWT, "")
		return
	}

	// err = g.fillUserInfo(ctx, user)
	// if err != nil {
	// 	g.ResponseError(ctx, xcode.ERRCODE_JWT, "用户fillUserInfo错误")
	// 	return
	// }

	//如果之前有invalid token 标记，则清除
	_ = g.TokenValid(user.ID)

	//记录登录日志
	ipaddr := g.GetRemoteIP(ctx)
	region := model.ParseReginByIP(ipaddr)
	content := fmt.Sprintf("登录时间 %s, 登录IP %s, 登录城市：%s", now.GetTimeStr(), ipaddr, region)
	_ = g.AddNewLog(user.CompanyID, user.ID, "登录", content)

	xlog.Debug("user login", "user", user)
	//返回值
	g.ResponseData(ctx, gin.H{
		"user":        user,
		"accessToken": token,
	})
	return
}

// 用户注册
// 为简化设计，不要求短信验证码
func (g *GroupUser) registerUser(ctx *gin.Context) {
	req := struct {
		Username string `form:"username"       json:"username"`
		Password string `form:"password"       json:"password"        binding:"required"`
		// Phone     string `form:"phone"          json:"phone"           binding:"required"`
		// PhoneCode string `form:"phone_code"     json:"phone_code"      binding:"required"` //短信验证码
		CaptchaID  string `form:"captcha_id"     json:"captcha_id"   binding:"required"` //captchaID
		VerifyCode string `form:"verify_code"    json:"verify_code"  binding:"required"` //captcha 验证码
	}{}

	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	verifyResult := base64Captcha.DefaultMemStore.Verify(req.CaptchaID, req.VerifyCode, false)
	if !verifyResult {
		xlog.Error("verify code error: ")
		g.ResponseError(ctx, xcode.ERRCODE_CAPTCH, "")
		return
	}

	var err error
	//检查用户是否已经被添加，未被添加的用户不可以注册。
	var u *model.User
	u, err = g.GetUserByPhoneOrName(req.Username)
	if err != nil {
		xlog.Error("invalid user phone")
		g.ResponseError(ctx, xcode.ERRCODE_USER_NOT_EXIST, "不支持开放注册，请联系管理员授权")
		return
	}

	// 短信验证码是否正确
	// if !g.CheckSmsCode(req.Phone, req.PhoneCode) {
	// 	g.ResponseError(ctx, xcode.ERRCODE_SMS_CODE, "")
	// 	return
	// }

	err = g.UpdateUserFirst(u.ID, req.Username, req.Password)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DB_UPDATE, "")
		return
	}
	g.ResponseOK(ctx)
	return
}

// 用户登出
func (g *GroupUser) userLogout(ctx *gin.Context) {
	//前端删除token，后端暂不作处理
	uid := g.GetUID(ctx)
	_ = g.TokenInvalid(uid)

	g.ResponseOK(ctx)
	return
}

// 用户更新自己的信息
func (g *GroupUser) userUpdate(ctx *gin.Context) {
	req := dto.ReqUserUpdate{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	// if req.Phone != "" {
	// 	// 短信验证码是否正确
	// 	if g.CheckSmsCode(req.Phone, req.PhoneCode) == false {
	// 		g.ResponseError(ctx, xcode.ERRCODE_SMS_CODE, "")
	// 		return
	// 	}
	// }

	uid := g.GetUID(ctx)

	if req.OldPassword != "" {
		ok := g.CheckPassword(uid, req.OldPassword)
		if !ok {
			g.ResponseError(ctx, xcode.ERRCODE_PASSWORD_ERR, "")
			return
		}
	}
	if req.AvatarFilename != "" {
		//保存上传的文件  (前端仅填充 filename，size)
		avatarFile, err := ctx.FormFile("file")
		if err != nil {
			xlog.Error("form file err:", err)
			g.ResponseError(ctx, xcode.ERRCODE_UPLOAD_FILE, "")
			return
		}

		uuid := xutils.UUID()
		fileSuffix := xutils.FileType(req.AvatarFilename)
		filePath := fmt.Sprintf("%s/%s.%s", config.Get().Misc.PathTmp, uuid, fileSuffix)

		xlog.Debug("avatar file path: ", filePath)
		err = ctx.SaveUploadedFile(avatarFile, filePath)
		if err != nil {
			xlog.Error("save file err:", err)
			g.ResponseError(ctx, xcode.ERRCODE_IO, "")
			return
		}
		req.Avatar, err = g.OssUpload(uid, filePath)
		if err != nil {
			xlog.Warn("upload avatar failed:", err)
		}
	}

	err := g.UpdateUser(uid, &req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseOK(ctx)
	return
}

// 用户信息获取
func (g *GroupUser) userInfo(ctx *gin.Context) {
	uid := g.GetUID(ctx)
	user, err := g.GetUserByUID(uid)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DB_NOT_FOUND, "")
		return
	}
	user.IsSuper = g.IsSuper(ctx)
	xlog.Info("user profile", "uid", user.ID, "companyid", user.CompanyID, "name", user.Username)
	err = g.fillUserInfo(ctx, user)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseData(ctx, user)
	return
}

// 用户信息填充
func (g *GroupUser) fillUserInfo(ctx *gin.Context, user *model.User) error {
	//所属角色列表
	roles, e6 := g.GetRolesByUID(user.ID)
	if e6 != nil {
		return e6
	}
	user.Roles = roles

	//所属 company
	comp, e7 := g.GetCompanyByID(user.CompanyID)
	if e7 != nil {
		return e7
	}
	user.Company = comp
	user.Company.Title = comp.Name
	//如果此公司没有自己的 logo，则公司名和公司的 logo 都使用上级公司的。
	if user.Company.Logo == "" {
		if comp.ParentID == 0 {
			// 如果使用parentID = 0 ，,去查询companyid = 0 的，是没有结果的。因为没有companyid=0的公司
			comp.ParentID = 1
		}
		parent, e9 := g.GetCompanyByID(comp.ParentID)
		if e9 != nil {
			return e9
		}
		user.Company.Logo = parent.Logo
		user.Company.Title = parent.Name
	}

	//所属分组
	groups, e8 := g.GetUserGroupByUID(user.ID)
	if e8 != nil {
		return e8
	}
	user.Groups = groups

	return nil
}

// 子用户添加
func (g *GroupUser) subUserCreate(ctx *gin.Context) {
	req := dto.ReqSubUser{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	company_id := g.GetCompanyID(ctx)

	//如果没有区域信息，则默认当前公司的区域
	if req.ProvinceID == 0 {
		company, err := g.GetCompanyByID(company_id)
		if err != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
			return
		}
		req.ProvinceID = company.ProvinceID
		req.CityID = company.CityID
		req.DistrictID = company.DistrictID
	}

	err := g.AddSubUser(company_id, &req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	//添加日志
	uid := g.GetUID(ctx)
	_ = g.AddNewLog(company_id, uid, "添加子用户", fmt.Sprintf("添加 %s 子用户", req.UserName))

	g.ResponseOK(ctx)

	return
}

// --------------------------------------------------
// 查询我所在的公司的所有人员列表
func (g *GroupUser) subUserList(ctx *gin.Context) {
	req := dto.ReqSubUserQuery{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	req.CheckPage()

	company_id := g.GetCompanyID(ctx)
	arr, err := g.GetUsersByCompanyIDAndQuery(company_id, &req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	//填充user的 roles 和 company
	for _, one := range arr {
		err = g.fillUserInfo(ctx, one)
		if err != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
			return
		}
	}
	g.ResponsePage(ctx, &req.PageInfo, arr, len(arr))
	return
}

// 子用户更新:可更新name desc role,area
func (g *GroupUser) subUserUpdate(ctx *gin.Context) {
	req := dto.ReqSubUser{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	err := g.UpdateSubUser(&req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseOK(ctx)
	return
}

// 子用户删除
func (g *GroupUser) subUserDelete(ctx *gin.Context) {
	req := struct {
		UID int64 `form:"uid" json:"uid"    binding:"required"`
	}{}

	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	err := g.DeleteSubUser(req.UID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	//添加日志
	uid := g.GetUID(ctx)
	company_id := g.GetCompanyID(ctx)
	_ = g.AddNewLog(company_id, uid, "删除子用户", fmt.Sprintf("删除子用户:  %d", req.UID))
	g.ResponseOK(ctx)
	return
}
