package handler

import (
	"fmt"

	"bs.com/app/config"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/middleware"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
)

func (g *GroupUser) addNewCompany(ctx *gin.Context) {
	req := dto.ReqCompany{}

	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	if req.ProvinceID == 0 {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	uid := g.GetUID(ctx)

	if req.LogoFilename != "" {
		//如果有上传logo 文件，则保存到文件存储
		logoFile, err := ctx.FormFile("file")
		if err != nil {
			xlog.Error("form file err:", err)
			g.ResponseError(ctx, xcode.ERRCODE_UPLOAD_FILE, "")
			return
		}

		uuid := xutils.UUID()
		fileSuffix := xutils.FileType(req.LogoFilename)
		filePath := fmt.Sprintf("%s/%s.%s", config.Get().Misc.PathTmp, uuid, fileSuffix)

		xlog.Debug("logo file path: ", filePath)
		err = ctx.SaveUploadedFile(logoFile, filePath)
		if err != nil {
			xlog.Error("save file err:", err.Error())
			g.ResponseError(ctx, xcode.ERRCODE_IO, "")
			return
		}
		req.Logo, err = g.OssUpload(uid, filePath)
		if err != nil {
			xlog.Warn("upload avatar failed:", err)
		}
	}
	if req.FullName == "" {
		req.FullName = req.Name
	}
	company_id := g.GetCompanyID(ctx)
	err := g.AddCompany(company_id, &req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseOK(ctx)
}

func (g *GroupUser) updateCompany(ctx *gin.Context) {
	req := dto.ReqCompany{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	uid := g.GetUID(ctx)
	if req.LogoFilename != "" {
		//如果有上传logo 文件，则保存到文件存储
		avatarFile, err := ctx.FormFile("file")
		if err != nil {
			xlog.Error("form file err:", err)
			g.ResponseError(ctx, xcode.ERRCODE_UPLOAD_FILE, "")
			return
		}

		uuid := xutils.UUID()
		fileSuffix := xutils.FileType(req.LogoFilename)
		filePath := fmt.Sprintf("%s/%s.%s", config.Get().Misc.PathTmp, uuid, fileSuffix)

		xlog.Debug("logo file path: ", filePath)
		err = ctx.SaveUploadedFile(avatarFile, filePath)
		if err != nil {
			xlog.Error("save file err:", err.Error())
			g.ResponseError(ctx, xcode.ERRCODE_IO, "")
			return
		}
		req.Logo, err = g.OssUpload(uid, filePath)
		if err != nil {
			xlog.Warn("upload avatar failed:", err)
		}
	}

	err := g.UpdateCompany(&req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseOK(ctx)
}

func (g *GroupUser) treeCompany(ctx *gin.Context) {
	req := struct {
		UID  int64  `json:"uid"  form:"uid"`  //显式给出uid，则返回此uid真实company_tree, 否则解析token中的 company_id，返回其当前所在公司的 company_tree
		Name string `json:"name" form:"name"` //公司名字模糊搜搜
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	//显式给出 uid，则返回此uid真实 company_id, 否则解析 token 中的 company_id
	var company_id int64

	var user *model.User
	var err error
	if req.UID != 0 {
		user, err = g.GetUserByUID(req.UID)
		if err != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
			return
		}
		company_id = user.CompanyID
	} else {
		company_id = g.GetCompanyID(ctx)
	}

	//先模糊搜索
	if req.Name != "" {
		var e error
		ret, e := g.QueryCompany(company_id, req.Name)
		if e != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
			return
		}
		g.ResponseList(ctx, ret, len(ret))
		return
	}

	//查询company_tree
	arr, err := g.TreeCompany(company_id)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseList(ctx, arr, len(arr))
}

func (g *GroupUser) delCompany(ctx *gin.Context) {
	req := struct {
		ID int64 `json:"id"  form:"id"     binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	// 公司有子公司，不可以删除
	arrSub, err := g.ListCompany(req.ID, nil)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	if len(arrSub) == 0 {
		g.ResponseError(ctx, xcode.ERRCODE_CAN_NOT_DEL, "此公司不存在")
		return
	}

	if len(arrSub) > 1 {
		g.ResponseError(ctx, xcode.ERRCODE_CAN_NOT_DEL, "此公司有子公司，不可以删除")
		return
	}

	// 公司有测站或者设备，不可以删除
	// has, err := g.CheckCompany(req.ID)
	// if err != nil {
	// 	g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
	// 	return
	// }
	// if has {
	// 	g.ResponseError(ctx, xcode.ERRCODE_CAN_NOT_DEL, "此公司有测站，不可以删除")
	// 	return
	// }

	err = g.DelCompany(req.ID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseOK(ctx)
}

// 返回公司列表
// 填充company的 role 字段
func (g *GroupUser) fillCompanyRoles(one *model.Company) error {
	roles, err := g.GetRolesByCompanyID(one.ID)
	if err != nil {
		return err
	}
	one.Roles = roles
	return nil
}

func (g *GroupUser) listCompanyDevman(ctx *gin.Context) {
	company_id := g.GetCompanyID(ctx)
	arr, err := g.AuthedCompany(company_id)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	result := []*model.Company{}
	for _, one := range arr {
		if one.ID == company_id {
			// 不返回当前公司
			continue
		}
		if one.IsDevMan == false {
			// 只返回有设备管理权限的子公司
			continue
		}

		result = append(result, one)
	}
	g.ResponseList(ctx, result, len(result))
}

func (g *GroupUser) authedCompany(ctx *gin.Context) {

	uid := g.GetUID(ctx)
	uInfo, err := g.GetUserByUID(uid)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	company_id := uInfo.CompanyID
	// company_id := g.GetCompanyID(ctx) // 从 token 中拿到 company_id 不行

	arr, err := g.AuthedCompany(company_id)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseList(ctx, arr, len(arr))
}

// parent_id 不传，则分页返回当前公司所有的下级公司。按照id 排序。
// parent_id 传递某个 company_id，则分页返回此 company_id 的下一级公司。
func (g *GroupUser) listCompany(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		ParentID int64 `json:"parent_id"  form:"parent_id"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}
	req.CheckPage()

	company_id := req.ParentID
	if req.ParentID == 0 {
		company_id = g.GetCompanyID(ctx)
	}

	arr, err := g.ListCompany(company_id, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	//填充 roles
	for _, one := range arr {
		_ = g.fillCompanyRoles(one)
	}

	g.ResponsePage(ctx, &req.PageInfo, arr, len(arr))
}

// 返回用户信息和新的token
// 返回值类似 login 的接口
func (g *GroupUser) switchCompany(ctx *gin.Context) {
	req := struct {
		CompanyID int64 `json:"company_id" form:"company_id"           binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	//是否存在此 company
	destComp, err := g.GetCompanyByID(req.CompanyID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	destComp.Title = destComp.Name

	//如果此公司没有自己的 logo，则公司名和公司的 logo 都使用上级公司的。
	if destComp.Logo == "" && destComp.ParentID != 0 {
		parent, err := g.GetCompanyByID(destComp.ParentID)
		if err != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, "查找父级失败")
			return
		}
		destComp.Logo = parent.Logo
		destComp.Title = parent.Name
	}

	uid := g.GetUID(ctx)
	user, err := g.GetUserByUID(uid)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	//获取菜单列表
	var resArr []*model.Menu
	// 真实的公司 ID 和打算切换的公司 ID，要么相等，要么是父子关系
	ok, e1 := g.IsParentChild(user.CompanyID, req.CompanyID)
	if e1 != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	//没有权限切换company
	if !ok {
		g.ResponseError(ctx, xcode.ERRCODE_NOT_PERMISSION, "没有权限")
		return
	}

	// 如果是自己所在公司，则返回用户自己的权限
	if user.CompanyID == req.CompanyID {
		if g.IsSuper(ctx) {
			//	如果是超级用户，且切回到自己的公司，则返回全部。
			xlog.Debug("is super")
			resArr, err = g.GetAllMenus()
		} else {
			// 查询此用户自己的路由表
			xlog.Debug("by uid")
			resArr, err = g.GetMenusByUID(uid) //  GetResourceByUID(uid)
		}
	} else {
		// 如果切换到子公司，则返回子公司的权限：假设切换公司的人都是有整个公司的权限的。
		xlog.Debug("by company")
		resArr, err = g.GetMenusByCompanyID(req.CompanyID) //
	}

	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	//把 resource  转换为树状，需要 2 次遍历，同时标注是否有权限
	// mTree := make(map[int64]*model.Menu, 0)
	// rootArr := make([]*model.Menu, 0)

	xlog.Debug("res arr:", "arr", resArr)

	rootArr := g.BuildMenuTree(resArr)
	// //第一次遍历，标记checked，建立 resource map，填充 rootArr用于返回
	// for _, one := range resArr {
	// 	mTree[one.MenuID] = one
	// 	//根目录的 parent_id 为 0
	// 	if one.ParentID == 0 {
	// 		rootArr = append(rootArr, one)
	// 	}
	// }

	// //第二次遍历，填充 children
	// for _, one := range resArr {
	// 	if parent, ok := mTree[one.ParentID]; ok {
	// 		parent.Children = append(parent.Children, one)
	// 	}
	// }

	// //根据 sort_num 排序：递归调用
	// sort.Stable(model.MenuArr(rootArr))
	// for _, one := range rootArr {
	// 	one.SortChildren()
	// }

	//使用公司的省市区的 ID，作为 user 当前的省市区 ID
	user.ProvinceID = destComp.ProvinceID
	user.CityID = destComp.CityID
	user.DistrictID = destComp.DistrictID

	//用新的 companyID，生成新的token 返回
	user.CompanyID = req.CompanyID

	// 查找 projectCode
	// var cur_project_code string
	// prjArr, _ := model.NewProjectRepo().FindByFilter(context.Background(), model.ProejctFilter{CompanyID: req.CompanyID}, nil)
	// if len(prjArr) > 0 {
	// 	cur_project_code = prjArr[0].Code
	// }
	token, err := middleware.GenerateToken(user) // , cur_project_code)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_JWT, "")
		return
	}
	g.ResponseData(ctx, gin.H{
		"company": destComp,
		"user":    user,
		"token":   token,
		"menus":   rootArr,
	})
	return
}
