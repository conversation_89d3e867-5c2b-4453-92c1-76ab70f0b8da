package handler

import (
	"encoding/json"
	"os"
	"path/filepath"

	"bs.com/app/config"
	"bs.com/app/global"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"

	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"github.com/gin-gonic/gin"
)

type GroupMenu struct {
	base.BaseController
	model.IDao
}

func NewMenuGroup() *GroupMenu {
	return &GroupMenu{
		BaseController: base.BaseController{},
		IDao:           *model.NewIDao(),
	}
}

func (g *GroupMenu) Router(r *gin.Engine) {
	menu := r.Group("/api/menu")
	{
		//获取图片验证码
		menu.GET("/get_menu_list", g.getMenuList)
		menu.POST("/add_menu", g.addMenu)
		menu.POST("/update_menu", g.updateMenu)
		menu.POST("/delete_menu", g.deleteMenu)
		menu.GET("/export_menu", g.exportMenu)  // 导出菜单
		menu.POST("/import_menu", g.importMenu) // 导入菜单
	}
}

func (d *GroupMenu) addMenu(ctx *gin.Context) {
	req := dto.AddUpdateMenuReq{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		d.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	if req.Path == nil || req.Component == nil {
		d.ResponseError(ctx, xcode.ERRCODE_PARAM, "path == nil or component == nil")
		return
	}
	err = d.AddMenu(&req)
	if err != nil {
		d.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	d.ResponseOK(ctx)
}

func (d *GroupMenu) updateMenu(ctx *gin.Context) {
	req := dto.AddUpdateMenuReq{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		d.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	err = d.UpdateMenu(&req)
	if err != nil {
		d.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	d.ResponseOK(ctx)
}

func (d *GroupMenu) deleteMenu(ctx *gin.Context) {
	req := struct {
		MenuID int64 `form:"menu_id"  json:"menu_id" binding:"required"`
	}{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		d.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}
	err = d.DeleteMenu(req.MenuID)
	if err != nil {
		d.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	d.ResponseOK(ctx)
}

func (d *GroupMenu) getMenuList(ctx *gin.Context) {
	uid := d.GetUID(ctx)

	//可用菜单
	var menus []*model.Menu
	var err error
	if d.IsSuper(ctx) {
		menus, err = d.GetAllMenus()
		if err != nil {
			d.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
			return
		}
	} else {
		menus, err = d.GetMenusByUID(uid)
		if err != nil {
			d.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
			return
		}
	}
	respMenu := d.BuildMenuTree(menus)
	d.ResponseData(ctx, respMenu)
}

// 导出菜单
func (g *GroupMenu) exportMenu(ctx *gin.Context) {
	filename := "menus.json"
	cfg := config.Get()
	exportFilename := filepath.Join(cfg.Misc.PathExport, filename)
	db := global.DB()
	err := model.ExportTable2Json(db, model.Menu{}, exportFilename)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseData(ctx, gin.H{"export": exportFilename, "filename": filename})
}

// 导入菜单
func (g *GroupMenu) importMenu(ctx *gin.Context) {
	req := struct {
		Filename string `form:"filename"      json:"filename"         binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		xlog.Errorf("params error: %v", err)
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}
	//保存上传的文件  (前端仅填充 filename，size)
	ssfile, err := ctx.FormFile("file")
	if err != nil {
		xlog.Error("form file err:", err)
		g.ResponseError(ctx, xcode.ERRCODE_UPLOAD_FILE, "")
		return
	}
	fullFilePath := filepath.Join(config.Get().Misc.PathTmp, req.Filename)
	err = ctx.SaveUploadedFile(ssfile, fullFilePath)
	if err != nil {
		xlog.Errorf("save file err: %v", err)
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	jsonData, err := os.ReadFile(fullFilePath)
	if err != nil {
		xlog.Errorf("read resource json file error:%s", err.Error())
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	// 将文件转为Resource对象
	resList := []*model.Menu{}
	err = json.Unmarshal(jsonData, &resList)
	if err != nil {
		xlog.Errorf(" jsonData to  Resource error:%s", err.Error())
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	g.ImportMenuToDB(resList)
	g.ResponseOK(ctx)
}
