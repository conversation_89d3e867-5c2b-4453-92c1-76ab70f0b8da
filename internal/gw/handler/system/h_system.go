package handler

import (
	"fmt"

	"github.com/gin-gonic/gin"

	"bs.com/app/config"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
)

type GroupSystem struct {
	base.BaseController
	model.IDao
}

func NewSystemGroup() *GroupSystem {
	return &GroupSystem{
		BaseController: base.BaseController{},
		IDao:           *model.NewIDao(),
	}
}

func (g *GroupSystem) Router(r *gin.Engine) {
	sys := r.Group("/api/system")
	{

		//area
		sys.GET("/area_children_list", g.areaChildrenList) //给定parentID，查询子区域列表
		sys.GET("/area_info", g.areaInfo)                  //给定 ID，查询区域信息
		sys.GET("/area_tree", g.areaTree)                  //给定 ID，查询子区域树

		//log
		sys.GET("/list_log", g.listLog) //查询操作日志

		// 公共上传文件
		sys.POST("/upload_file", g.uploadFile)
	}
}

// 根据parentID 返回下一级area
func (g *GroupSystem) areaChildrenList(ctx *gin.Context) {
	req := struct {
		ParentID int64 `form:"parent_id"  json:"parent_id"`
	}{}

	var err error
	if err = ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	var areaList []*model.Area
	if req.ParentID == 0 {
		//默认返回省列表
		areaList, err = g.GetAreaListByLevel(model.AreaLevel_Province)
	} else {
		areaList, err = g.GetAreaListByParentID(req.ParentID)
	}
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseList(ctx, areaList, len(areaList))
	return
}

// 根据parentID 返回下一级area
func (g *GroupSystem) areaTree(ctx *gin.Context) {
	req := struct {
		ID int64 `form:"id"    json:"id"    binding:"required"`
	}{}

	var err error
	if err = ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	var tree *model.Area
	tree, err = g.GetAreaTreeByID(req.ID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	//g.ResponseData(ctx, areaList)
	g.ResponseData(ctx, tree)
	return
}

func (g *GroupSystem) areaInfo(ctx *gin.Context) {
	req := struct {
		ID int64 `form:"id"    json:"id"    binding:"required"`
	}{}

	var err error
	if err = ctx.ShouldBind(&req); err != nil {
		xlog.Error("err:", err.Error())
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	var one *model.Area
	one, err = g.GetAreaByID(req.ID)
	if err != nil {
		xlog.Error("get area by id", "err", err.Error())
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponseData(ctx, one)
	return
}

func (g *GroupSystem) listLog(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		Username string `form:"username" json:"username"`
		Title    string `form:"title"    json:"title"`
	}{}

	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}

	req.CheckPage()

	company_id := g.GetCompanyID(ctx)
	is_super := g.IsSuper(ctx)
	if is_super {
		company_id = 0
	}
	ret, err := g.FindLogList(&req.PageInfo, company_id, req.Username, req.Title)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}

	g.ResponsePage(ctx, &req.PageInfo, ret, len(ret))
}

// 公共上传文件，然后返回地址url
func (g *GroupSystem) uploadFile(ctx *gin.Context) {
	req := struct {
		Filename string `form:"filename"     json:"filename"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}
	// fmt.Println("fielanem:", req.Filename)
	// 上传文件
	if req.Filename == "" {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "文件名错误")
		return
	}
	uid := g.GetUID(ctx)
	//保存上传的文件  (前端仅填充 filename，size)
	ssfile, err := ctx.FormFile("file")
	if err != nil {
		xlog.Error("form file err:", err)
		g.ResponseError(ctx, xcode.ERRCODE_UPLOAD_FILE, "")
		return
	}
	fullFilePath := fmt.Sprintf("%s/%s", config.Get().Misc.PathTmp, req.Filename)
	err = ctx.SaveUploadedFile(ssfile, fullFilePath)
	if err != nil {
		xlog.Error("save file err:", err)
		g.ResponseError(ctx, xcode.ERRCODE_IO, "")
		return
	}
	fileUrl, err := g.OssUpload(uid, fullFilePath)
	if err != nil {
		xlog.Warn("upload app failed:", err)
	}
	// req.ProtocFileUrl = fileUrl
	type Resp struct {
		URL string `json:"url"`
	}
	resp := Resp{URL: fileUrl}
	g.ResponseData(ctx, resp)
}
