package jt808

import (
	"encoding/json"
	"time"

	"bs.com/app/internal/gw/dto"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"github.com/gin-gonic/gin"
)

type retData struct {
	Ts       int64              `json:"ts"`
	Position *dto.PositionInfo2 `json:"position"`
}

// 历史定位数据
func (g *GroupJT808) historyPositionList(ctx *gin.Context) {
	req := struct {
		DeviceID   string `form:"device_id"         json:"device_id"    binding:"required"` //设备 ID
		Identifier string `form:"identifier"        json:"identifier"   binding:"required"` //使用属性 identifier
		Start      int64  `form:"start"             json:"start"`                           //时间戳，单位 ms
		End        int64  `form:"end"               json:"end"`                             //时间戳，单位 ms
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	result := []*retData{}
	if req.DeviceID == "00000000041010430267" {
		// 测试数据
		result = gendata()
		xlog.Debug("test data", "len", len(result))
		// g.ResponseData(ctx, result, len(result))
		g.ResponseData(ctx, gin.H{
			"device_id": req.DeviceID,
			"name":      "假数据",
			"list":      result,
			"len":       len(result),
		})
		return
	} else {
		if req.Start == 0 {
			req.Start = time.Now().Add(time.Hour * -24).UnixMilli() //默认一天前
		}

		if req.End == 0 || req.End < req.Start || req.End > time.Now().UnixMilli() {
			req.End = time.Now().UnixMilli() //默认现在
		}
		// device_id、identifier 是 tag,start, end 是时间范围
		data, err := g.QueryDataAttr(req.DeviceID, req.Identifier, req.Start, req.End, nil, nil)
		if err != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
			return
		}

		// g.ResponseList(ctx, data, len(data))
		g.ResponseData(ctx, gin.H{
			"device_id": req.DeviceID,
			"name":      "虚拟设备01",
			"list":      data,
			"len":       len(data),
		})
		return
	}
}

func gendata() []*retData {

	result := []dto.PositionInfo{}
	err := json.Unmarshal([]byte(datastr), &result)
	if err != nil {
		xlog.Error("unmarshal error", "err", err)
		return nil
	}

	arr := []*retData{}
	for _, v := range result {
		one, ts, err := v.ToPositionInfo2()
		if err != nil {
			xlog.Error("to position info error", "err", err)
			continue
		}

		arr = append(arr, &retData{
			Ts:       ts,
			Position: one,
		})
	}

	return arr
}
