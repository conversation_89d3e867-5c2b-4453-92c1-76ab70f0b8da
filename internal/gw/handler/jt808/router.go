package jt808

import (
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/model"
	"bs.com/app/internal/tsdb"
	"github.com/gin-gonic/gin"
)

type GroupJT808 struct {
	base.BaseController
	model.IDao
	model.DeviceRepo
	model.DeviceGroupRepo
	tsdb.ITsDBRepo
}

func NewJT808Group() *GroupJT808 {
	return &GroupJT808{
		BaseController: base.BaseController{},
		IDao:           *model.NewIDao(),

		DeviceRepo:      *model.NewDeviceRepo(),
		DeviceGroupRepo: *model.NewDeviceGroupRepo(),
		ITsDBRepo:       tsdb.NewInfluxdbRepo(),
	}
}

func (g *GroupJT808) Router(r *gin.Engine) {
	dev := r.Group("/api/jt808")
	{
		dev.GET("/history/position/list", g.historyPositionList)
	}
}
