package mqtt

import (
	"net/http"
	"strings"

	"bs.com/app/internal/dm"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"github.com/gin-gonic/gin"
)

// 兼容 EMQX 和 Comqtt
type mqttAuthResp struct {
	Result string `json:"result"`
}

func (g *GroupMqtt) mqttAuthConnect(ctx *gin.Context) {
	req := struct {
		Username string `json:"username"`  // username
		Password string `json:"password"`  // password
		ClientID string `json:"client_id"` // clientID
		PeerHost string `json:"peerhost"`  // remote address
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	// 鉴权：一型一密 和 一机一密不同的流程
	// TODO：通过 peerhost 判断是否本地的服务
	xlog.Debug("mqtt auth connect", "username", req.Username, "password", req.Password, "clientID", req.ClientID, "peerhost", req.PeerHost)
	// g.ResponseOK(ctx)
	if g.authPassword(req.ClientID, req.Username, req.Password) {
		// ctx.String(200, "1")
		resp := mqttAuthResp{Result: "allow"}
		ctx.JSON(http.StatusOK, resp)
	} else {
		// ctx.String(200, "0")
		resp := mqttAuthResp{Result: "deny"}
		ctx.JSON(http.StatusOK, resp)
	}
}

func (g *GroupMqtt) mqttAuthAcl(ctx *gin.Context) {
	req := struct {
		Username string `json:"username"`  // username
		ClientID string `json:"client_id"` // clientID
		Action   string `json:"action"`    // action: publish, subscribe
		PeerHost string `json:"peer_host"` // remote address
		Topic    string `json:"topic"`     // topic
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	// 鉴权：需修改 comqtt 的逻辑
	xlog.Debug("mqtt auth acl", "username", req.Username, "clientID", req.ClientID, "action", req.Action, "peerhost", req.PeerHost, "topic", req.Topic)

	if g.authTopic(req.ClientID, req.Topic, req.Username) {
		resp := mqttAuthResp{Result: "allow"}
		ctx.JSON(http.StatusOK, resp)
	} else {
		resp := mqttAuthResp{Result: "deny"}
		ctx.JSON(http.StatusOK, resp)
	}
}

// 通过解析对比 clientID 和 topic，判断是否匹配
func (g *GroupMqtt) authTopic(clientID, topic, username string) bool {
	if dm.IsMqttWhitelist(clientID, username, topic) {
		// 白名单，直接返回 true
		return true
	}

	idInfo, err := bean.GetClientIDInfo(clientID)
	if err != nil {
		return false
	}
	topicInfo, err := bean.GetTopicInfo(topic)
	if err != nil {
		return false
	}

	// deviceTypeID
	if topicInfo.DeviceTypeID != idInfo.DeviceTypeID {
		xlog.Error("Auth topic error: deviceTypeID not match", "topicInfo.DeviceTypeID", topicInfo.DeviceTypeID, "idInfo.DeviceTypeID", idInfo.DeviceTypeID)
		return false
	}
	if strings.HasPrefix(username, "ap.") {
		// 接入点仅匹配 deviceTypeID
		return true
	}

	// deviceID
	if topicInfo.DeviceID != idInfo.DeviceID {
		xlog.Error("Auth topic error: deviceID not match", topicInfo.DeviceID, "idInfo.DeviceID", idInfo.DeviceID)
		return false
	}

	return true
}

// 设备连接鉴权
func (g *GroupMqtt) authPassword(clientID string, username string, password string) bool {
	if dm.IsMqttWhitelist(clientID, username, password) {
		return true
	}

	info, err := bean.GetClientIDInfo(clientID)
	if err != nil {
		xlog.Error("parse client id info error", "GetClientIDInfo", err)
		return false
	}
	valiInfo := &bean.AuthConnectInfo{
		ClientID: clientID,
		UserName: username,
		Password: password,
	}

	// TODO: 是否非设备的 mqtt client
	if strings.HasPrefix(username, "ap.") {
		// 是接入点
		accessPointID := info.DeviceID
		xlog.Debug("auth type is accessPoint", "accessPointID", accessPointID)
		ap, err := g.AccessPointRepo.FindOne(accessPointID)
		if err != nil {
			xlog.Error("find accessPoint error", "FindOne", err, "accessPointID", accessPointID)
			return false
		}
		valiInfo.Secret = ap.AccessPointCode
	} else if strings.HasPrefix(username, "hardware.") {
		// 是硬件
		productID := info.DeviceTypeID
		xlog.Debug("auth type is hardware", "productID", productID)
		hardware, err := g.ProductRepo.FindOne(productID)
		if err != nil {
			xlog.Error("find hardware error", "FindOne", err, "productID", productID)
			return false
		}
		// 只支持一型一密
		valiInfo.Secret = hardware.ProductCode
	} else {
		// 是设备
		deviceType, err := g.DeviceTypeRepo.FindOne(info.DeviceTypeID)
		if err != nil {
			xlog.Error("find device type error", "FindOne", err, "deviceTypeID", info.DeviceTypeID)
			return false
		}

		if deviceType.AuthType == model.TypeAuthType {
			// 一型一密
			xlog.Debug("auth type is device", "deviceTypeID", deviceType.DeviceTypeID)
			valiInfo.Secret = deviceType.DeviceTypeCode
		} else {
			// 一机一密
			xlog.Debug("auth type is device", "deviceID", info.DeviceID)
			device, err := g.DeviceRepo.FindOne(info.DeviceID)
			if err != nil {
				xlog.Error("Auth find device error", "FindOne", err)
				return false
			}
			valiInfo.Secret = device.DeviceCode
		}
	}
	return bean.ValidateMqttAuthInfo(valiInfo)
}
