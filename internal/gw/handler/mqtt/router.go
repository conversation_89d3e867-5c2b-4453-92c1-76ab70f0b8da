package mqtt

import (
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/model"
	"github.com/gin-gonic/gin"
)

type GroupMqtt struct {
	base.BaseController
	model.DeviceRepo
	model.DeviceTypeRepo
	model.AccessPointRepo

	model.ProductRepo
}

func NewMqttGroup() *GroupMqtt {
	return &GroupMqtt{
		BaseController:  base.BaseController{},
		DeviceRepo:      *model.NewDeviceRepo(),
		DeviceTypeRepo:  *model.NewDeviceTypeRepo(),
		AccessPointRepo: *model.NewAccessPointRepo(),
		ProductRepo:     *model.NewProductRepo(),
	}
}

func (g *GroupMqtt) Router(r *gin.Engine) {
	mqt := r.Group("/api/mqtt")
	{
		mqt.POST("/auth", g.mqttAuthConnect)
		mqt.POST("/acl", g.mqttAuthAcl)
	}
}
