### mqtt鉴权

运行 comqtt：

```shell
./comqtt  -conf ./config/single.yml
```

comqtt 配置文件修改

```diff
diff --git a/cmd/config/auth-http.yml b/cmd/config/auth-http.yml
index f9141ac..044842f 100644
--- a/cmd/config/auth-http.yml
+++ b/cmd/config/auth-http.yml
@@ -1,9 +1,9 @@
-auth-mode: 1  # 0 Anonymous, 1 Username, 2 ClientID
+auth-mode: 2  # 0 Anonymous, 1 Username, 2 ClientID
 acl-mode: 2  # 0 Anonymous, 1 Username, 2 ClientID
 tls-enable: false #true(https) or false(http)
 tls-cert:
 tls-key:
 method: post  #get or post
 content-type: application/json  # application/json、 application/x-www-form-urlencoded
-auth-url: http://localhost:8080/comqtt/auth
-acl-url: http://localhost:8080/comqtt/acl
\ No newline at end of file
+auth-url: http://localhost:8200/api/mqtt/auth
+acl-url: http://localhost:8200/api/mqtt/acl
\ No newline at end of file
diff --git a/cmd/config/single.yml b/cmd/config/single.yml
index 96b279e..dce59c9 100644
--- a/cmd/config/single.yml
+++ b/cmd/config/single.yml
@@ -5,9 +5,9 @@ bridge-path: ./config/bridge-kafka.yml  #The bridge config file path
 pprof-enable: false #Whether to enable the performance analysis tool http://ip:6060

 auth:
-  way: 0  #Authentication way: 0 anonymous, 1 username and password, 2 clientid
-  datasource: 1   #Optional items:0 free、1 redis、2 mysql、3 postgresql、4 http ...
-  conf-path: ./config/auth-redis.yml  #The config file path should correspond to the auth-datasource
+  way: 1  #Authentication way: 0 anonymous, 1 username and password, 2 clientid
+  datasource: 4  #Optional items:0 free、1 redis、2 mysql、3 postgresql、4 http ...
+  conf-path: ./config/auth-http.yml  #The config file path should correspond to the auth-datasource

 mqtt:
   tcp: :1883
```

## 一型一密


一型一密的设备，无法为每一个设备配置 username 和 password，只能统一使用 deviceType 的 ID 和 code，作为 username 和 password。
鉴权的时候，对比一下 username 和 password 即可