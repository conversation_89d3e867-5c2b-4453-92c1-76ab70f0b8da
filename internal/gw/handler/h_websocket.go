package handler

import (
	"context"
	"net/http"
	"strconv"

	"bs.com/app/config"
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xjwt"
	"bs.com/app/pkg/xlog"
	xws "bs.com/app/pkg/xwebsocket"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// 字典管理
type WebsocketGroup struct {
	base.BaseController
	Ctx context.Context
	Ws  *xws.Server
}

func NewWebsocketGroup() *WebsocketGroup {
	cfg := config.Get()
	portStr := strconv.FormatInt(int64(cfg.ServiceApp.HttpPort), 10)
	xws.StartWsDp(true) // 初始化websocket调度器
	return &WebsocketGroup{
		Ctx: context.Background(),
		Ws:  xws.NewServer("ws", nil, xws.Port(portStr)),
	}
}
func (g *WebsocketGroup) Router(r *gin.Engine) {

	r.GET("/api/ws", g.WebSocketHandler)
}

func (g *WebsocketGroup) WebSocketHandler(ctx *gin.Context) {

	token := ctx.Query("token")
	// 检验token是否正确
	uInfo := new(xjwt.CustomJwtClaims)
	var err error
	if uInfo, err = xjwt.ParseToken(token); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_TOKEN, "")
		return
	}

	var upgrader = websocket.Upgrader{
		// 读取存储空间大小
		ReadBufferSize: 1024,
		// 写入存储空间大小
		WriteBufferSize: 1024,
		// 允许跨域
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	//ws连接失败
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_SYSTEM, "ws连接失败")
		xlog.Error("[ws]连接失败", "RemoteAddr:", ctx.Request.RemoteAddr, "err", err)
		return
	}

	g.InitWebsocketConn(ctx.Request, conn, uInfo)
}

func (g *WebsocketGroup) InitWebsocketConn(r *http.Request, conn *websocket.Conn, userInfo *xjwt.CustomJwtClaims) {
	wsClient := xws.NewConn(g.Ctx, g.Ws, r, conn, userInfo)
	if wsClient == nil {
		xlog.Error("new websocket error")
	}
	go wsClient.StartRead()
	go wsClient.StartWrite()
}
