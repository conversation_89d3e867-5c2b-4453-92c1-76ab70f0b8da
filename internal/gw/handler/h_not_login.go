package handler

import (
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
)

type GroupNotLogin struct {
	base.BaseController
	model.IDao
}

func NewGroupNotLogin() *GroupNotLogin {
	return &GroupNotLogin{
		BaseController: base.BaseController{},
		IDao:           *model.NewIDao(),
	}
}

func (g *GroupNotLogin) Router(r *gin.Engine) {
	sys := r.Group("/api/system")
	{
		// 获取系统配置，前端配置
		sys.GET("/get_frontend_config", g.getFrontendConfig) // 获取前端配置
		sys.POST("/set_frontend_config", g.setFrontendConfig)

	}
}

func (g *GroupNotLogin) getFrontendConfig(ctx *gin.Context) {
	key := model.FrontendKey
	syscfg, err := g.QuerySystemConfig(key)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}

	g.ResponseData(ctx, syscfg.Config)
}

func (g *GroupNotLogin) setFrontendConfig(ctx *gin.Context) {

	req := struct {
		Config map[string]interface{} `json:"config"`
	}{}
	err := ctx.ShouldBind(&req)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "")
		return
	}
	key := model.FrontendKey

	err = g.UpdateSystemConfig(key, datatypes.JSONMap(req.Config))
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, "")
		return
	}
	g.ResponseOK(ctx)
}
