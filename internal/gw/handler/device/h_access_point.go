package device

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
)

func (g *GroupDevice) listAccessPoints(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		AccessPointID string `form:"access_point_id"  json:"access_point_id"`
		Name          string `form:"name"  json:"name"`
		ConnProtocol  string `form:"conn_protocol"  json:"conn_protocol"`
		MsgProtocol   string `form:"msg_protocol"  json:"msg_protocol"`
		Status        int32  `form:"status"  json:"status"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()
	xlog.Debug("params:", "req", req)
	filter := model.AccessPointFilter{
		CompanyID:     g.GetCompanyID(ctx),
		Name:          req.Name,
		AccessPointID: req.AccessPointID,
		ConnProtocol:  req.ConnProtocol,
		MsgProtocol:   req.MsgProtocol,
		Status:        req.Status,
	}
	result, err := g.AccessPointRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	req.PageInfo.Total, _ = g.AccessPointRepo.CountByFilter(filter)
	g.ResponsePage(ctx, &req.PageInfo, result, len(result))
}

func (g *GroupDevice) addAccessPoint(ctx *gin.Context) {
	req := dto.AddAccessPointReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	companyID := int64(0)
	isSuper := g.IsSuper(ctx)
	if !isSuper {
		// 如果是超级用户，则创建的接入点为公共接入点。否则则是用户自己的接入点
		companyID = g.GetCompanyID(ctx)
	}
	one := &model.AccessPoint{
		AccessPointID:   xutils.RandString(13),
		AccessPointCode: xutils.RandString(13),
		CompanyID:       companyID,
		Name:            req.Name,
		ConnProtocol:    req.ConnProtocol,
		MsgProtocol:     req.MsgProtocol,
		Host:            req.Host,
		Port:            req.Port,
		ExtendInfo:      req.ExtendInfo,
		Status:          1,
	}
	err := g.AccessPointRepo.Insert(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseData(ctx, one)
}

func (g *GroupDevice) updateAccessPoint(ctx *gin.Context) {
	req := dto.UpdateAccessPointReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	one, err := g.AccessPointRepo.FindOne(req.AccessPointID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DB_NOT_FOUND, "")
		return
	}
	err = g.AccessPointRepo.Update(&model.AccessPoint{
		AccessPointID: req.AccessPointID,
		Name:          req.Name,
		CompanyID:     one.CompanyID,
		ConnProtocol:  req.ConnProtocol,
		MsgProtocol:   req.MsgProtocol,
		Host:          req.Host,
		Port:          req.Port,
		Status:        req.Status,
		ExtendInfo:    req.ExtendInfo,
	})
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) deleteAccessPoint(ctx *gin.Context) {
	req := struct {
		AccessPointID string `json:"access_point_id"  binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.AccessPointRepo.Delete(req.AccessPointID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}
