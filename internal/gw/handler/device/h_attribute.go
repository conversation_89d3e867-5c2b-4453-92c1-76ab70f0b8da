package device

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
)

func (g *GroupDevice) listDeviceTypeAttributes(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		Name         string `form:"name"           json:"name"`
		AttrID       string `form:"attr_id"        json:"attr_id"`
		DeviceTypeID string `form:"device_type_id" json:"device_type_id"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()

	filter := model.AttributeFilter{
		Name:         req.Name,
		AttrID:       req.AttrID,
		DeviceTypeID: req.DeviceTypeID,
	}
	xlog.Debug("attr handle filter: ", filter)
	result, err := g.AttributeRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponsePage(ctx, &req.PageInfo, result, len(result))
}

func (g *GroupDevice) addDeviceTypeAttribute(ctx *gin.Context) {
	req := dto.AddAttributeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	one := &model.Attribute{
		DeviceTypeID: req.DeviceTypeID,
		AttrID:       xutils.GetRandomNumCode(13),
		Name:         req.Name,
		Identifier:   req.Identifier,
		AttrType:     req.AttrType,
		DataType:     req.DataType,
		DataOptions:  req.DataOptions,
		Desc:         req.Desc,
	}
	err := g.AttributeRepo.Insert(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseData(ctx, one)
}

func (g *GroupDevice) updateDeviceTypeAttribute(ctx *gin.Context) {
	req := dto.UpdateAttributeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.AttributeRepo.Update(&model.Attribute{
		DeviceTypeID: req.DeviceTypeID,
		AttrID:       req.AttrID,
		Name:         req.Name,
		Identifier:   req.Identifier,
		AttrType:     req.AttrType,
		DataType:     req.DataType,
		DataOptions:  req.DataOptions,
		Desc:         req.Desc,
	})
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) deleteDeviceTypeAttribute(ctx *gin.Context) {
	req := struct {
		AttrID string `json:"attr_id"  binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.AttributeRepo.Delete(req.AttrID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) listAttributeHistory(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		AttrID   string `form:"attr_id"  json:"attr_id" binding:"required"`
		DeviceID string `form:"device_id" json:"device_id"  binding:"required"`
		dto.ReqTime
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()
	req.CheckTime()
	attr, err := g.AttributeRepo.FindOne(req.AttrID)
	if err != nil {
		xlog.Error("find attr error: ", "err", err)
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}

	dataList, err := g.InfluxdbRepo.QueryDataAttr(req.DeviceID, attr.Identifier, req.Start, req.End, nil, &req.PageInfo)
	if err != nil {
		// g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		xlog.Error("query ts data error: ", "err", err)
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	xlog.Debug("query ts data success", "len", len(dataList))
	g.ResponsePage(ctx, &req.PageInfo, dataList, len(dataList))
}
