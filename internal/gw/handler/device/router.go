package device

import (
	"bs.com/app/internal/dm"
	"bs.com/app/internal/gw/handler/base"
	"bs.com/app/internal/gw/model"
	"bs.com/app/internal/tsdb"
	"github.com/gin-gonic/gin"
)

type GroupDevice struct {
	base.BaseController
	model.IDao
	model.DeviceTypeRepo
	model.AccessPointRepo
	model.AttributeRepo
	model.EventRepo
	model.CommandRepo
	//
	model.DeviceRepo
	model.DeviceGroupRepo

	//
	tsdb.InfluxdbRepo

	// 产品管理
	model.ProductRepo
	model.FirmwareRepo
	model.FirmwareDiffRepo
	model.HardwareRepo
	model.FileRepo

	// dm
	dman *dm.DeviceMan
}

func NewDeviceGroup() *GroupDevice {
	return &GroupDevice{
		BaseController:  base.BaseController{},
		IDao:            *model.NewIDao(),
		DeviceTypeRepo:  *model.NewDeviceTypeRepo(),
		AccessPointRepo: *model.NewAccessPointRepo(),
		AttributeRepo:   *model.NewAttributeRepo(),
		EventRepo:       *model.NewEventRepo(),
		CommandRepo:     *model.NewCommandRepo(),
		//
		DeviceRepo:      *model.NewDeviceRepo(),
		DeviceGroupRepo: *model.NewDeviceGroupRepo(),

		// 时序数据库，获取历史值
		InfluxdbRepo: *tsdb.NewInfluxdbRepo(),

		// 产品管理
		ProductRepo:      *model.NewProductRepo(),
		FirmwareRepo:     *model.NewFirmwareRepo(),
		FirmwareDiffRepo: *model.NewFirmwareDiffRepo(),
		HardwareRepo:     *model.NewHardwareRepo(),

		// 文件管理
		FileRepo: *model.NewFileRepo(),

		// dm
		dman: dm.OneDM,
	}
}

func (g *GroupDevice) Router(r *gin.Engine) {
	dev := r.Group("/api/device")
	{
		// 设备 device 管理
		dev.GET("/list", g.listDevices)
		dev.POST("/add", g.addDevice)
		dev.POST("/update", g.updateDevice)
		dev.POST("/delete", g.deleteDevice)

		dev.POST("/down/attribute", g.downAttribute) // 下发属性
		dev.POST("/down/command", g.downCommand)     // 下发命令

		// 导出信息
		dev.POST("/export", g.exportDevices) //

		// 设备连接信息
		dev.GET("/connect/list", g.listConnectInfo)

		// device type设备类型
		dev.GET("/type/list", g.listDeviceTypes)
		dev.GET("/type/one", g.getDeviceType)
		dev.POST("/type/add", g.addDeviceType)
		dev.POST("/type/update", g.updateDeviceType)
		dev.POST("/type/delete", g.deleteDeviceType)

		// 设备类型关联列表
		dev.GET("/type/device/list", g.listDeviceTypeDevices)

		// 设备类型属性管理
		dev.GET("/type/attribute/list", g.listDeviceTypeAttributes)
		dev.POST("/type/attribute/add", g.addDeviceTypeAttribute)
		dev.POST("/type/attribute/update", g.updateDeviceTypeAttribute)
		dev.POST("/type/attribute/delete", g.deleteDeviceTypeAttribute)

		dev.GET("/attribute/history", g.listAttributeHistory)

		// 设备类型事件管理
		dev.GET("/type/event/list", g.listDeviceTypeEvents)
		dev.POST("/type/event/add", g.addDeviceTypeEvent)
		dev.POST("/type/event/update", g.updateDeviceTypeEvent)
		dev.POST("/type/event/delete", g.deleteDeviceTypeEvent)

		dev.GET("/event/history", g.listEventHistory)

		// 设备类型命令管理
		dev.GET("/type/command/list", g.listDeviceTypeCommands)
		dev.POST("/type/command/add", g.addDeviceTypeCommand)
		dev.POST("/type/command/update", g.updateDeviceTypeCommand)
		dev.POST("/type/command/delete", g.deleteDeviceTypeCommand)

		dev.GET("/command/history", g.listCommandHistory)

		// access point接入点
		dev.GET("/access_point/list", g.listAccessPoints)
		dev.POST("/access_point/add", g.addAccessPoint)
		dev.POST("/access_point/update", g.updateAccessPoint)
		dev.POST("/access_point/delete", g.deleteAccessPoint)
	}
}
