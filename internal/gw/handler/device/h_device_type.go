package device

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
)

func (g *GroupDevice) addDeviceType(ctx *gin.Context) {
	req := dto.AddDeviceTypeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	componyId := g.GetCompanyID(ctx)
	one := &model.DeviceType{
		CompanyID:      componyId,
		DeviceTypeID:   xutils.RandString(10),
		DeviceTypeCode: xutils.RandString(13),
		Name:           req.Name,
		Icon:           req.Icon,
		AuthType:       req.AuthType,
		ConnType:       req.ConnType,
		AccessType:     req.AccessType,
		AccessPointIDs: req.AccessPointIDs,
		TemplateKind:   req.TemplateKind,
		TemplateLibID:  req.TemplateLibID,
		Tags:           req.Tags,
		Desc:           req.Desc,
		Status:         model.Enable, // 默认启用
	}
	err := g.DeviceTypeRepo.Insert(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) listDeviceTypes(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		Name         string `form:"name"  json:"name"`
		DeviceTypeID string `form:"device_type_id"  json:"device_type_id"`
		Status       int32  `form:"status" json:"status"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()
	xlog.Debug("req:", req)
	repo := g.DeviceTypeRepo
	f := model.DeviceTypeFilter{
		CompanyID:    g.GetCompanyID(ctx),
		DeviceTypeID: req.DeviceTypeID,
		Name:         req.Name,
		Status:       req.Status,
	}
	devTypes, err := repo.FindByFilter(f, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	req.PageInfo.Total, err = repo.CountByFilter(f)
	if err != nil {
		xlog.Error("search count error", "err", err)
	}
	g.ResponsePage(ctx, &req.PageInfo, devTypes, len(devTypes))
}

func (g *GroupDevice) getDeviceType(ctx *gin.Context) {
	req := struct {
		DeviceTypeID string `form:"device_type_id"  json:"device_type_id" binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	xlog.Debug("req", "req", req)
	repo := g.DeviceTypeRepo

	companyID := g.GetCompanyID(ctx)
	if g.IsSuper(ctx) {
		companyID = 0
	}
	f := model.DeviceTypeFilter{
		CompanyID:    companyID,
		DeviceTypeID: req.DeviceTypeID,
	}
	devType, err := repo.FindOneByFilter(f)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseData(ctx, devType)
}

func (g *GroupDevice) updateDeviceType(ctx *gin.Context) {
	req := dto.UpdateDeviceTypeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	one, err := g.DeviceTypeRepo.FindOne(req.DeviceTypeID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	one.Name = req.Name
	one.Icon = req.Icon
	one.ConnType = req.ConnType
	one.AuthType = req.AuthType
	one.AccessType = req.AccessType
	one.AccessPointIDs = req.AccessPointIDs
	one.Desc = req.Desc
	one.Tags = req.Tags
	one.ExtendInfo = req.ExtendInfo
	one.Status = req.Status
	err = g.DeviceTypeRepo.Update(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) listDeviceTypeDevices(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		DeviceTypeID string `form:"device_type_id" json:"device_type_id" binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()
	filter := model.DeviceFilter{DeviceTypeID: req.DeviceTypeID}
	devices, err := g.DeviceRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponsePage(ctx, &req.PageInfo, devices, len(devices))
}

func (g *GroupDevice) listNotDevTypeDevices(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()
	filter := model.DeviceFilter{CompanyID: g.GetCompanyID(ctx)}
	devices, err := g.DeviceRepo.FindNotDeviceTypeDevices(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponsePage(ctx, &req.PageInfo, devices, len(devices))
}
