package device

import (
	"path/filepath"

	"bs.com/app/config"
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/export"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
)

func (g *GroupDevice) listDevices(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		Name         string `json:"name"`
		DeviceID     string `json:"device_id"`
		DeviceTypeID string `json:"device_type_id"`
		Status       int32  `json:"status"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()
	companyID := int64(0)
	if !g.<PERSON>upe<PERSON>(ctx) {
		companyID = g.GetCompanyID(ctx)
	}

	filter := model.DeviceFilter{
		Name:         req.Name,
		DeviceID:     req.DeviceID,
		DeviceTypeID: req.DeviceTypeID,
		Status:       req.Status,
		CompanyID:    companyID,
	}
	devList, err := g.DeviceRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	for _, dev := range devList {
		dev.DeviceTypeInfo, err = g.DeviceTypeRepo.FindOne(dev.DeviceTypeID)
		if err != nil {
			xlog.Error("find device type info error: %v", err)
		}
		dev.GroupInfo, err = g.DeviceGroupRepo.FindByFilter(model.DeviceGroupFilter{GroupIDs: dev.Groups}, nil)
		if err != nil {
			xlog.Error("find device group info error: %v", err)
		}
	}
	req.PageInfo.Total, err = g.DeviceRepo.CountByFilter(filter)
	if err != nil {
		// g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		xlog.Error("count error", "err", err)
	}
	g.ResponsePage(ctx, &req.PageInfo, devList, len(devList))
}

func (g *GroupDevice) addDevice(ctx *gin.Context) {
	req := dto.AddDeviceReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	devID := req.DeviceID
	if devID == "" {
		devID = xutils.UUIDSnowFlake()
	}

	devType, err := g.DeviceTypeRepo.FindOne(req.DeviceTypeID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	if devType == nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, "设备类型不存在")
		return
	}

	defaultDevCode := xutils.RandString(13)
	if devType.AuthType == model.TypeAuthType {
		// 如果是一型一密
		defaultDevCode = devType.DeviceTypeCode
	}

	one := &model.Device{
		CompanyID:    g.GetCompanyID(ctx),
		DeviceTypeID: req.DeviceTypeID,
		DeviceID:     devID,
		Name:         req.Name,
		Status:       model.Enable, // 默认启用
		Desc:         req.Desc,
		Tags:         req.Tags,
		DeviceCode:   defaultDevCode,
		ActiveOnline: model.Offline, // 默认不在线
	}
	err = g.DeviceRepo.Insert(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseData(ctx, one)
}

func (g *GroupDevice) updateDevice(ctx *gin.Context) {
	req := dto.UpdateDeviceReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	err := g.DeviceRepo.Update(&model.Device{
		CompanyID:    g.GetCompanyID(ctx),
		DeviceTypeID: req.DeviceTypeID,
		DeviceID:     req.DeviceID,
		Name:         req.Name,
		Status:       req.Status,
		Desc:         req.Desc,
		ExtendInfo:   req.ExtendInfo,
		Alarm:        req.Alarm,
		Tags:         req.Tags,
	})
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) deleteDevice(ctx *gin.Context) {
	req := struct {
		DeviceID string `json:"device_id"  binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.DeviceRepo.DeleteByID(req.DeviceID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) deleteDeviceType(ctx *gin.Context) {
	req := struct {
		DeviceTypeID string `json:"device_type_id"  binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.DeviceTypeRepo.DeleteByID(req.DeviceTypeID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	// 删除设备，同时删除设备属性，事件，命令对应的
	err = g.AttributeRepo.DeleteByDeviceTypeID(req.DeviceTypeID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	err = g.EventRepo.DeleteByDeviceTypeID(req.DeviceTypeID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}

	err = g.CommandRepo.DeleteByDeviceTypeID(req.DeviceTypeID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) deviceAuthInfo(ctx *gin.Context) {
	req := struct {
		DeviceID string `form:"device_id"      json:"device_id"       binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	dev, err := g.DeviceRepo.FindOne(req.DeviceID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	clientID, username, password := bean.GenMqttAuthInfo(dev.DeviceTypeID, dev.DeviceTypeID, dev.DeviceCode)
	devTypeAuthInfo := dto.AuthInfoResp{
		ClientID: clientID,
		Username: username,
		Password: password,
	}
	g.ResponseData(ctx, devTypeAuthInfo)
}

/*
TODO：这里要修改
当设备直接使用标准 mqtt 协议的时候，才会显示接入信息。
当设备通过接入点接入的时候，仅显示接入点信息。接入点如何对设备接入，是他们之间的约定，和我们平台没有关系。

接入点查看的时候，需显示接入点的连接信息。包括接入点 ID 和接入点 Code。每一个接入点在接入 broker 的时候鉴权用得到这些信息
*/
func (g *GroupDevice) listConnectInfo(ctx *gin.Context) {
	req := struct {
		DeviceID string `form:"device_id"     json:"device_id"    binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	dev, err := g.DeviceRepo.FindOne(req.DeviceID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}

	dev.DeviceTypeInfo, err = g.DeviceTypeRepo.FindOne(dev.DeviceTypeID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}

	ids := xutils.JSONMapToStringSlice(dev.DeviceTypeInfo.AccessPointIDs)
	connInfoList := g.AccessPointRepo.GenConnectInfo(dev, ids)
	g.ResponseList(ctx, connInfoList, len(connInfoList))
}

// 下发属性
func (g *GroupDevice) downAttribute(ctx *gin.Context) {
	req := struct {
		DeviceID string         `form:"device_id" json:"device_id" binding:"required"`
		Data     map[string]any `form:"data"      json:"data"      binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	err := g.dman.MessageDownToDeviceAsync(bean.MqttTypeAttri, req.DeviceID, bean.AttriSet, req.Data)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_ATTRI_ERROR, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

// 下发命令
func (g *GroupDevice) downCommand(ctx *gin.Context) {
	req := struct {
		DeviceID string         `form:"device_id" json:"device_id" binding:"required"` // device id
		Command  string         `form:"command" json:"command" binding:"required"`     // command identifier
		Data     map[string]any `form:"data" json:"data" binding:"required"`           // command params
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	// TODO：参数检查：action 是否合法、参数检查

	// 等待返回，10 秒超时
	resp, err := g.dman.MessageDownToDeviceSync(bean.MqttTypeAction, req.DeviceID, req.Command, req.Data, 10)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_COMMAND_TIMEOUT, err.Error())
		return
	}

	g.ResponseData(ctx, resp)
}

func (g *GroupDevice) exportDevices(ctx *gin.Context) {
	req := struct {
		DeviceIDs    []string `form:"device_ids" json:"device_ids" ` // device id
		DeviceTypeID string   `form:"device_type_id"  json:"device_type_id"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	companyID := g.GetCompanyID(ctx)
	// 如果两个都没有，那就是导出全部
	filter := model.DeviceFilter{
		CompanyID:    companyID,
		DeviceIDs:    req.DeviceIDs,
		DeviceTypeID: req.DeviceTypeID,
	}
	devices, err := g.DeviceRepo.FindByFilter(filter, nil)
	if err != nil {
		xlog.Error("export device find by filter error", "err", err)
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	xlog.Debug("export devices:", "devs", devices)
	filename := xutils.GetTimeRandNum() + "devices.xlsx"
	cfg := config.Get()
	cdnFileName := filepath.Join(cfg.Misc.PathExport, filename)
	err = export.ExportToExcel(devices, cdnFileName, "")
	if err != nil {
		xlog.Error("export device export to excel error", "err", err)
		g.ResponseError(ctx, xcode.ERRCODE_SYSTEM, err.Error())
		return
	}
	g.ResponseData(ctx, gin.H{"export": cdnFileName, "filename": filename})
}
