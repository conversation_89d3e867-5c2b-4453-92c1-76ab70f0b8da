
## 关于 gw 服务

gw服务是我们物联网平台的主服务。

- 负责用户、菜单、角色管理，也通过 company 的概念支持多租户、多项目的管理。
- 负责设备的管理、数据的接收、预警、时序数据的存储、数据的查询、报表、可视化等工作。
- 负责网络组件的管理。

网关服务对于设备端的接入，仅支持标准协议（mqtt+json）。
如果其他设备希望接入到gw 服务


## 设备管理

设备管理分为两大类，一类是我们自己的设备，一类是其他厂家的硬件设备。
- 我们自己的设备，定义了 product  deviceType  device 这三个主要概念
- 其他厂家的设备，定义了 deviceType  device 这两个概念

其中：

- product 是我们自己的硬件设备。我们自己的设备，多了远程运维功能：固件升级、远程配置设备参数、远程调试、故障管理
- deviceType 和具体业务相关的，使用物模型的方式来抽象管理（属性、方法、事件）。


## 关于物模型

- 物模型是一个 json 描述信息。参考 thingscloud 的实现来做。
- 物模型和 mqtt+json 的标准协议对应。
- 前端和 app 通过物模型，可以确定此产品的功能、数据、操作、事件等。


## 关于 access point

access point 是接入服务，在 jetlinks 平台，叫做网络组件。一般是一个独立的进程，通过标准协议（mqtt + json）与网关服务通信。

所有的设备，只要不是直接支持我们的标准协议的，都需要实现对应的 接入服务。

```
比如我们如果希望支持水利 SL651 协议，就需要支持一个 sl651 的接入服务。这个服务启动一个 tcp server，在设备和 gw 服务之间，作为桥梁。设备上行的消息需要转换成 json 上报。下行的 json 消息，也要转换成 sl651 协议下发。
```

接入服务需要实现的功能：

- 符合 gw 服务对于接入服务的要求。需要主动注册自身信息到 gw 服务，提供标准的 rpc 接口，比如设备鉴权信息获取。
- 支持特定协议的设备接入，包括接入鉴权逻辑。
- 作为设备和 gw 服务之间的桥梁，做协议的转换作用
    - 上行消息转换成 mqtt+json，比如属性上报、事件上报
    - 下行消息转换成 特定协议，比如设备支持的方法。
    - 设备在线状态的管理

我们应该提供一个 sdk，叫做 access point sdk，来帮助接入服务的开发。sdk 中定义好了一些标准化的东西，比如与 gw 服务之间的 mqtt+json 的 通讯，比如接入服务注册，比如 rpc 相关框架（基于 mqtt 封装的同步操作即可，不使用 gRPC）。

