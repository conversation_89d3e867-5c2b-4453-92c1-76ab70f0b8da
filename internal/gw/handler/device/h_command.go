package device

import (
	"encoding/json"

	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
)

func (g *GroupDevice) listDeviceTypeCommands(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		Name         string `form:"name"           json:"name"`
		CommandID    string `form:"command_id"     json:"command_id"`
		DeviceTypeID string `form:"device_type_id" json:"device_type_id"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()

	filter := model.CommandFilter{
		Name:         req.Name,
		CommandID:    req.CommandID,
		DeviceTypeID: req.DeviceTypeID,
	}
	result, err := g.CommandRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponsePage(ctx, &req.PageInfo, result, len(result))
}

func (g *GroupDevice) addDeviceTypeCommand(ctx *gin.Context) {
	req := dto.AddCommandReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	one := &model.Command{
		DeviceTypeID:       req.DeviceTypeID,
		CommandID:          xutils.GetRandomNumCode(13),
		Name:               req.Name,
		Identifier:         req.Identifier,
		Desc:               req.Desc,
		SendParams:         datatypes.NewJSONSlice(req.SendParams),
		SendParamsDefault:  datatypes.JSONMap(req.SendParamsDefault),
		ReplyParams:        datatypes.NewJSONSlice(req.ReplyParams),
		ReplyParamsDefault: datatypes.JSONMap(req.ReplyParamsDefault),
	}
	err := g.CommandRepo.Insert(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseData(ctx, one)
}

func (g *GroupDevice) updateDeviceTypeCommand(ctx *gin.Context) {
	req := dto.UpdateCommandReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.CommandRepo.Update(&model.Command{
		DeviceTypeID:       req.DeviceTypeID,
		CommandID:          req.CommandID,
		Name:               req.Name,
		Identifier:         req.Identifier,
		Desc:               req.Desc,
		SendParams:         datatypes.NewJSONSlice(req.SendParams),
		SendParamsDefault:  datatypes.JSONMap(req.SendParamsDefault),
		ReplyParams:        datatypes.NewJSONSlice(req.ReplyParams),
		ReplyParamsDefault: datatypes.JSONMap(req.ReplyParamsDefault),
	})
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) deleteDeviceTypeCommand(ctx *gin.Context) {
	req := struct {
		CommandID string `form:"command_id" json:"command_id"  binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.CommandRepo.Delete(req.CommandID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) listCommandHistory(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		dto.ReqTime
		DeviceID  string `form:"device_id" json:"device_id"  binding:"required"`
		CommandID string `form:"command_id"    json:"command_id" `
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()

	identifier := ""
	if req.CommandID != "" {
		cmd, err := g.CommandRepo.FindOne(req.CommandID)
		if err != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
			return
		}
		identifier = cmd.Identifier
	}

	dataList, err := g.InfluxdbRepo.QueryDataCommand(req.DeviceID, identifier, req.Start, req.End, nil, &req.PageInfo)
	if err != nil {
		xlog.Error("query data error: ", "err", err)
	}
	// xlog.Debug("-------command history dataList ", "dataList", dataList)
	cmdHistoryList := make([]*dto.CommandResp, 0)
	for _, v := range dataList {
		for key, value := range v {
			if key == "ts" || key == dto.SendTs || key == dto.ReplyTs {
				continue
			}
			mData, ok := value.(map[string]any)
			if !ok {
				xlog.Error("mData invalid data type")
				continue
			}
			identifier := mData["identifier"].(string)
			cmd, err := g.CommandRepo.FindOneByFilter(model.CommandFilter{Identifier: identifier})
			if err != nil {
				xlog.Error("find command error: ", "err", err)
				continue
			}

			// xlog.Debug("------- cmd value ", "v", value)
			sendData := map[string]any{}
			if mData[dto.CommandSend] != nil {
				err := json.Unmarshal([]byte(mData[dto.CommandSend].(string)), &sendData)
				if err != nil {
					xlog.Error("get send data error:", "err", err)
				}
			}
			replyData := map[string]any{}
			if mData[dto.CommandReply] != nil {
				err := json.Unmarshal([]byte(mData[dto.CommandReply].(string)), &replyData)
				if err != nil {
					xlog.Error("get reply data error:", "err", err)
				}
			}
			sendTime := int64(0)
			sendTs, ok := mData[dto.SendTs].(int64)
			if ok {
				sendTime = sendTs
			}
			replyTime := int64(0)
			replyTs, ok := mData[dto.ReplyTs].(int64)
			if ok {
				replyTime = replyTs
			}
			cmdHistoryList = append(cmdHistoryList, &dto.CommandResp{
				Name:        cmd.Name,
				Identifier:  cmd.Identifier,
				Desc:        cmd.Desc,
				SendParams:  sendData,
				ReplyParams: replyData,
				SendTs:      sendTime,
				ReplyTs:     replyTime,
			})
		}
	}
	g.ResponsePage(ctx, &req.PageInfo, cmdHistoryList, len(cmdHistoryList))

}
