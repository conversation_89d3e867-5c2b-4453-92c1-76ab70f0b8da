package device

import (
	"bs.com/app/internal/gw/dto"
	"bs.com/app/internal/gw/model"
	"bs.com/app/pkg/xcode"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
)

func (g *GroupDevice) listDeviceTypeEvents(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		Name         string `form:"name"            json:"name"`
		EventID      string `form:"event_id"        json:"event_id"`
		DeviceTypeID string `form:"device_type_id"  json:"device_type_id"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()

	filter := model.EventFilter{
		Name:         req.Name,
		EventID:      req.EventID,
		DeviceTypeID: req.DeviceTypeID,
	}
	result, err := g.EventRepo.FindByFilter(filter, &req.PageInfo)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponsePage(ctx, &req.PageInfo, result, len(result))
}

func (g *GroupDevice) addDeviceTypeEvent(ctx *gin.Context) {
	req := dto.AddEventReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}

	one := &model.Event{
		DeviceTypeID: req.DeviceTypeID,
		EventID:      xutils.GetRandomNumCode(13),
		Name:         req.Name,
		Identifier:   req.Identifier,
		Desc:         req.Desc,
		Params:       datatypes.NewJSONSlice(req.Params),
	}
	err := g.EventRepo.Insert(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseData(ctx, one)
}

func (g *GroupDevice) updateDeviceTypeEvent(ctx *gin.Context) {
	req := dto.UpdateEventReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	one, err := g.EventRepo.FindOne(req.EventID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	one.DeviceTypeID = req.DeviceTypeID
	one.Name = req.Name
	one.Identifier = req.Identifier
	one.Desc = req.Desc
	one.Params = datatypes.NewJSONSlice(req.Params)
	err = g.EventRepo.Update(one)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) deleteDeviceTypeEvent(ctx *gin.Context) {
	req := struct {
		EventID string `form:"event_id" json:"event_id"  binding:"required"`
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	err := g.EventRepo.Delete(req.EventID)
	if err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
		return
	}
	g.ResponseOK(ctx)
}

func (g *GroupDevice) listEventHistory(ctx *gin.Context) {
	req := struct {
		dto.PageInfo
		dto.ReqTime
		DeviceID string `form:"device_id" json:"device_id"  binding:"required"`
		EventID  string `form:"event_id"    json:"event_id" `
	}{}
	if err := ctx.ShouldBind(&req); err != nil {
		g.ResponseError(ctx, xcode.ERRCODE_PARAM, err.Error())
		return
	}
	req.CheckPage()

	identifier := ""
	if req.EventID != "" {
		evt, err := g.EventRepo.FindOne(req.EventID)
		if err != nil {
			g.ResponseError(ctx, xcode.ERRCODE_DAO, err.Error())
			return
		}
		identifier = evt.Identifier
	}
	dataList, err := g.InfluxdbRepo.QueryDataEvent(req.DeviceID, identifier, req.Start, req.End, nil, &req.PageInfo)
	if err != nil {
		xlog.Error("query data error: ", "err", err)
	}

	eventHistoryList := make([]*dto.EventResp, 0)
	for _, v := range dataList {
		tsInt, ok := v["ts"].(int64)
		if !ok {
			tsInt = 0
		}
		for key, value := range v {
			if key == "ts" {
				continue
			}
			evt, err := g.EventRepo.FindOneByFilter(model.EventFilter{Identifier: key})
			if err != nil {
				xlog.Error("find event error: ", "err", err)
				continue
			}
			// xlog.Debug("event history: ", "data", value)
			mData, ok := value.(map[string]any)
			if !ok {
				xlog.Error("invalid data type")
				continue
			}
			paramsData := make(map[string]any)
			for _, param := range evt.Params {
				paramsData[param.Identifier] = mData[param.Identifier]
			}
			eventHistoryList = append(eventHistoryList, &dto.EventResp{
				Name:       evt.Name,
				Identifier: evt.Identifier,
				Desc:       evt.Desc,
				ParamsData: paramsData,
				Ts:         tsInt,
			})
		}
	}
	g.ResponsePage(ctx, &req.PageInfo, eventHistoryList, len(eventHistoryList))
}
