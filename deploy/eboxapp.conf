
# Default server configuration
server {
	listen 53006;
	#listen [::]:80 default_server;
	listen [::]:53006;


	server_name *************;


	gzip on;
	gzip_min_length 1k;
	gzip_buffers 4 16k;
	gzip_comp_level 2;
	gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
	gzip_vary off;
	gzip_disable "MSIE [1-6]\.";

	#root /home/<USER>/test/dist;

	# Add index.php to the list if you are using PHP
	index index.html index.htm index.nginx-debian.html;


    # mqtt 配置wss支持
	location /mqtt {
		
		    proxy_pass http://app-gw:53006; # 使用容器名字
                
                # proxy_read_timeout 300s;
                # proxy_send_timeout 300s;
                # proxy_set_header  Host $http_host;
                # proxy_set_header  X-Real-IP  $remote_addr;
                # proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
                # proxy_set_header  X-Forwarded-Proto $scheme;
                # proxy_http_version 1.1;
                # proxy_set_header Upgrade $http_upgrade;
                # proxy_set_header Connection $connection_upgrade;

                # proxy_pass http://**********:58080;
                #proxy_pass http://nats:8083;  # emqx 的websocket端口是8083
                proxy_http_version 1.1;
                # proxy_set_header X-Client-IP $remote_addr;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                # proxy_read_timeout 300s; # 默认是60秒，可设置

	}
    
    # 正则匹配的方式实现
    # location ~ ^/api/buca/(.*)$ {
    #     proxy_pass http://*************:8180/$1;
    #     proxy_set_header  X-Real-IP  $remote_addr;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";
    # }
	 

    location /api {
        proxy_pass        http://app-gw:53006;
        proxy_set_header  X-Real-IP  $remote_addr;

	proxy_set_header Host $host;
        proxy_read_timeout 3600s;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;

	add_header 'Access-Control-Allow-Origin' $http_origin;
	add_header 'Access-Control-Allow-Credentials' 'true';
	add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
	add_header 'Access-Control-Allow-Headers' 'DNT,web-token,app-token,Authorization,Accept,Origin,Keep-Alive,User-Agent,X-Mx-ReqToken,X-Data-Type,X-Auth-Token,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
	add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';

    }


    location /cdn {
	proxy_pass    http://app-gw:53006;
        proxy_set_header  X-Real-IP  $remote_addr;
    }

    # for zlm
    location /rtp {
        proxy_pass http://zlmedia:55080/rtp;
        add_header Access-Control-Allow-Origin * always;
        add_header 'Access-Control-Allow-Methods' 'GET,POST,OPTIONS,PUT,DELETE' always;

        proxy_set_header  X-Real-IP  $remote_addr;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }


    location /public {
        alias /var/www/beithing.com/public;
    }

    location / {
        #root /home/<USER>/data/work/vue/eboxweb/dist;
        #root /usr/share/nginx/html/beithing.com/dist;
        # root /var/www/beithing.com/dist;
        
       ######### 
	    root /var/www/beithing.com/dist/eboxapp;
        try_files $uri $uri/ @router;
        index  index.html;
    }

    location @router{
        rewrite ^.*$ /index.html last;
    }
}
