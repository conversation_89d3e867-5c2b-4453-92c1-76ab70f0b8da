services:
  gw:
    image: alpine:3.16
    container_name: app-gw
    restart: always
    environment:
      #- LocalHostDev=dev
      - PATH=$PATH:/app/deploy
    #ports:
      #- 53006:53006
    volumes:
      - "$PWD/data:/app"
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - $PWD/data/deploy/dmidecode:/usr/sbin/dmidecode
      - type: bind
        source: /dev/mem
        target: /dev/mem
    deploy:
      resources:
        ## 限制使用资源大小
        limits:
           cpus: '0.50'
           memory: 800M
    privileged: true
    working_dir: /app
    command: ./app_linux.bin gw
    networks:
      hunan2:
        ipv4_address: **************

networks:
  hunan2:
    external: true   
