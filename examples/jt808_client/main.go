package main

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/cuteLittleDevil/go-jt808/protocol/model"
	"github.com/cuteLittleDevil/go-jt808/shared/consts"
	"github.com/cuteLittleDevil/go-jt808/terminal"
)

func init() {
	logHandler := slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	})
	slog.SetDefault(slog.New(logHandler))
}

type jt808Client struct {
	jt808   *terminal.Terminal
	address string
	ctx     context.Context
	cancel  context.CancelFunc
}

func main() {
	// 创建终端模拟器
	t := terminal.New(
		// 设置协议版本和终端手机号
		// terminal.WithHeader(consts.JT808Protocol2013, "12345678901"),
		terminal.WithHeader(consts.JT808Protocol2019, "18012340001"),
	)

	client := &jt808Client{
		jt808:   t,
		address: "*************:60808",
		// address:   "127.0.0.1:60808",
	}

	// 监听信号
	go func() {
		// 创建一个通道用于接收终止信号
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
		// 等待终止信号
		<-sigCh
		os.Exit(0)
	}()

	// 进入循环，支持重连
	for {
		slog.Info("开启模拟器连接")
		// 创建新的上下文
		client.ctx, client.cancel = context.WithCancel(context.Background())

		done := make(chan struct{})
		go func() {
			client.run()
			close(done)
		}()

		// 等待运行结束
		<-done

		slog.Info("终端模拟器已停止，等待 5 秒中重新启动")
		time.Sleep(time.Second * 5)
	}
}

func (c *jt808Client) run() {
	t := c.jt808
	// 连接到服务器
	address := c.address
	conn, err := net.Dial("tcp", address)
	if err != nil {
		slog.Error("连接服务器失败", slog.Any("err", err))
		c.cancel() // 触发重连
		return
	}
	defer conn.Close()
	slog.Info("成功连接到服务器:", slog.String("address", address))

	// 创建各种消息数据
	register := t.CreateDefaultCommandData(consts.T0100Register)   // 注册消息
	auth := t.CreateDefaultCommandData(consts.T0102RegisterAuth)   // 鉴权消息
	heartbeat := t.CreateDefaultCommandData(consts.T0002HeartBeat) // 心跳消息
	locationData := createLocationReport(t)                        // 位置上报消息

	// 发送注册消息
	slog.Info("发送注册消息", slog.String("data", fmt.Sprintf("%x", register)))
	_, err = conn.Write(register)
	if err != nil {
		slog.Error("发送注册消息失败", slog.Any("err", err))
		c.cancel() // 触发重连
		return
	}
	c.readResponse(conn)

	// 发送鉴权消息
	slog.Info("发送鉴权消息", slog.String("data", fmt.Sprintf("%x", auth)))
	_, err = conn.Write(auth)
	if err != nil {
		slog.Error("发送鉴权消息失败", slog.Any("err", err))
		c.cancel() // 触发重连
		return
	}
	c.readResponse(conn)

	// 发送位置上报消息
	slog.Info("发送位置上报消息", slog.String("data", fmt.Sprintf("%x", locationData)))
	_, err = conn.Write(locationData)
	if err != nil {
		slog.Error("发送位置上报消息失败", slog.Any("err", err))
		c.cancel() // 触发重连
		return
	}
	c.readResponse(conn)

	// 启动心跳协程
	go c.sendHeartbeat(conn, heartbeat)

	// 启动位置上报协程
	go c.sendLocationReport(conn, t)

	// 等待上下文结束
	<-c.ctx.Done()
}

// 创建位置上报消息
func createLocationReport(t *terminal.Terminal) []byte {
	// 创建位置信息对象
	location := &model.T0x0200{
		T0x0200LocationItem: model.T0x0200LocationItem{
			AlarmSign:  0,                                 // 报警标志
			StatusSign: 0,                                 // 状态标志
			Latitude:   31876543,                          // 北纬31.876543度，实际值需要乘以10^6
			Longitude:  118765432,                         // 东经118.765432度，实际值需要乘以10^6
			Altitude:   100,                               // 海拔高度，单位为米
			Speed:      60,                                // 速度，单位为0.1km/h
			Direction:  120,                               // 方向，0-359，正北为0，顺时针
			DateTime:   time.Now().Format("060102150405"), // 时间，YYMMDDHHMMSS格式
		},
	}

	// 使用位置信息对象创建消息
	return t.CreateCommandData(consts.T0200LocationReport, location.Encode())
}

// 读取服务器响应
func (c *jt808Client) readResponse(conn net.Conn) {
	buf := make([]byte, 1024)
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(3 * time.Second))
	n, err := conn.Read(buf)
	if err != nil {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			slog.Warn("读取响应超时")
		} else {
			slog.Error("读取响应失败", slog.Any("err", err))
		}

		// 超时也算异常
		c.cancel()
		return
	}
	// 重置读取超时
	conn.SetReadDeadline(time.Time{})

	slog.Info("收到服务器响应", slog.String("data", fmt.Sprintf("%x", buf[:n])))
}

// 定期发送心跳
func (c *jt808Client) sendHeartbeat(conn net.Conn, heartbeat []byte) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			slog.Info("发送心跳消息")
			_, err := conn.Write(heartbeat)
			if err != nil {
				slog.Error("发送心跳消息失败", slog.Any("err", err))
				c.cancel() // 触发重连
				return
			}
			c.readResponse(conn)
		case <-c.ctx.Done():
			slog.Info("停止发送心跳消息")
			return
		}
	}
}

// 定期发送位置上报
func (c *jt808Client) sendLocationReport(conn net.Conn, t *terminal.Terminal) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			location := createLocationReport(t)
			slog.Info("发送位置上报消息")
			_, err := conn.Write(location)
			if err != nil {
				slog.Error("发送位置上报消息失败", slog.Any("err", err))
				c.cancel() // 触发重连
				return
			}
			c.readResponse(conn)
		case <-c.ctx.Done():
			slog.Info("停止发送位置上报消息")
			return
		}
	}
}
