package main

import (
	"log/slog"
	"os"

	"github.com/cuteLittleDevil/go-jt808/service"
	"github.com/cuteLittleDevil/go-jt808/shared/consts"
)

// 初始化日志
func init() {
	logHandler := slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	})
	slog.SetDefault(slog.New(logHandler))
}

func main() {
	slog.Info("启动JT808服务器，监听端口：0.0.0.0:8680")

	// 创建自定义处理函数映射
	customHandlers := map[consts.JT808CommandType]service.Handler{
		consts.T0200LocationReport: &t0200ReportHandler{},
	}

	_ = customHandlers

	// 创建服务器实例
	goJt808 := service.New(
		service.WithHostPorts("0.0.0.0:8680"),
		service.WithNetwork("tcp"),
		// service.WithCustomHandleFunc(func() map[consts.JT808CommandType]service.Handler {
		// 	return customHandlers
		// }),
		service.WithCustomTerminalEventer(func() service.TerminalEventer {
			return &meTerminal{} // 自定义开始 结束 报文处理等事件
		}),
	)

	// 运行服务器
	goJt808.Run()
}
