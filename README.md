
## 项目介绍

新平台，和水利平台 ebox 拆分开，前端重新使用新的框架开发。支持后续各种业务接入。

- 物联网平台
- 视频服务器业务平台
- 。。。。其他业务平台

## 特点

- 支持响应式
- 支持多租户
- 模块之间低耦合
- 平台通用性

## 编译

直接 make，编译的文件在 build 目录下

## 运行

可以指定 config.yaml 文件

```shell
./build/app_darwin.bin gw --config config_shujun.yaml
或者
./build/app_darwin.bin --config config_shujun.yaml gw
```


## 虚拟设备运行

虚拟设备的配置直接修改代码 internal/vdmqtt/app.go 中。运行命令：

```shell
./build/app_darwin.bin  vdmqtt
```