package xjwt

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// 自定义token data
type CustomJwtClaims struct {
	jwt.RegisteredClaims
	UID       int64  `json:"uid"`
	Phone     string `json:"phone"`
	Is<PERSON><PERSON>r   bool   `json:"is_super"`   //是否超级用户
	CompanyID int64  `json:"company_id"` //公司 ID，支持多个公司之间切换

	ProvinceID int64 `json:"province_id"` //省ID
	CityID     int64 `json:"city_id"`     //市ID
	DistrictID int64 `json:"district_id"` //区县ID

	ProjectCode string `json:"project_code"` //当前projectcode
}

// 请求头的形式为 Authorization: Bearer token
const (
	JwtHeader = "Authorization"
	//中间件的错误码
	//ERRCODE_InvalidToken uint32 = 4001 // jwt token错误
)

const (
	TokenTTL  = 3600 * 24 * 1 // token 有效期默认 1 天
	JwtSecret = "dayu-jwt-secret-123456"
)

// 从header中获取token
func GetJwtFromHeader(c *gin.Context) (string, error) {
	aHeader := c.Request.Header.Get(JwtHeader)
	if len(aHeader) == 0 {
		return "", errors.New("token is empty")
	}

	strs := strings.SplitN(aHeader, " ", 2)
	if len(strs) != 2 || strs[0] != "Bearer" {
		return "", errors.New("token invalid")
	}
	return strs[1], nil
}

// 解析token内容
func ParseToken(tokenStr string) (*CustomJwtClaims, error) {
	var err error
	var token *jwt.Token
	claims := new(CustomJwtClaims)

	token, err = jwt.ParseWithClaims(tokenStr, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(JwtSecret), nil
	})
	if err == nil {
		return token.Claims.(*CustomJwtClaims), nil
	}

	if strings.Contains(err.Error(), "expired") {
		err = fmt.Errorf("token has expired: %w", err)
	} else {
		err = fmt.Errorf("parse token failed: %w", err)
	}

	return nil, err
}

// 根据ctx获取 custom jwt claims
func GetJwtClaimsFromCtx(c *gin.Context) (*CustomJwtClaims, error) {
	tokenStr, err := GetJwtFromHeader(c)
	if err != nil {
		return nil, err
	}
	cjwt, err := ParseToken(tokenStr)
	if err != nil {
		return nil, err
	}
	return cjwt, nil
}

// 根据公司id生成token，主要是给设备推送消息用
func GenCompanyToken(companyID int64) (string, error) {
	staffClaims := CustomJwtClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Second * time.Duration(TokenTTL))),
		},
		CompanyID: companyID,
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, staffClaims)
	// SecretKey 用于对用户数据进行签名，不能暴露
	return token.SignedString([]byte(JwtSecret))
}

func SetGinHeader(ctx *gin.Context, token string) {
	headerToken := fmt.Sprintf("Bearer %s", token)
	if ctx.Request == nil {
		ctx.Request = &http.Request{}
	}
	if ctx.Request.Header == nil {
		ctx.Request.Header = http.Header{}
	}
	ctx.Request.Header.Set(JwtHeader, headerToken)
}
