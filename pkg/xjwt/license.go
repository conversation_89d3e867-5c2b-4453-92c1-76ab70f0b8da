package xjwt

import (
	"crypto/hmac"
	"crypto/sha256"
	"fmt"
	"os"
	"strings"
	"time"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/golang-jwt/jwt/v5"
)

// system
// /sys/devices/virtual/dmi/id/product_serial
// /sys/devices/virtual/dmi/id/product_uuid
// /sys/devices/virtual/dmi/id/product_version
// /sys/devices/virtual/dmi/id/product_sku
// board
// /sys/devices/virtual/dmi/id/board_version
// /sys/devices/virtual/dmi/id/board_serial

// bios
// /sys/devices/virtual/dmi/id/bios_version

// proc

// /proc/version_signature

// 磁盘信息
// /sys/class/block/sdX/device/serial
// /sys/class/block/nvme0n1/device/serial

// getDmiInfo 从系统中读取指定的DMI信息
// 将读取的信息按顺序存入到一个字符串切片中
func getDmiInfo() []string {

	// 先确保当前程序是否有 root 权限
	isRoot := os.Geteuid() == 0
	if !isRoot {
		fmt.Println("请使用root权限运行")
		os.Exit(0)
	}

	// 需要读取的DMI信息文件路径
	dmiFiles := []string{
		"/sys/devices/virtual/dmi/id/product_serial",
		"/sys/devices/virtual/dmi/id/product_uuid",
		"/sys/devices/virtual/dmi/id/product_version",
		"/sys/devices/virtual/dmi/id/product_sku",
		"/sys/devices/virtual/dmi/id/board_version",
		"/sys/devices/virtual/dmi/id/board_serial",
		"/sys/devices/virtual/dmi/id/bios_version",
	}

	// 用于存储读取的信息
	results := make([]string, len(dmiFiles))

	// 逐个读取文件内容
	for i, filePath := range dmiFiles {
		content, err := os.ReadFile(filePath)
		// fmt.Println("filepath :", filePath)
		// fmt.Println("content :", string(content))
		if err != nil {
			results[i] = ""
		} else {
			results[i] = strings.TrimSpace(string(content))
		}
	}

	return results
}

// ---------------------------------------------------------------------------------------------------
const (
	keySign = "LHM8K7kcLYjArw1eF9JTs2b8wE5X3L5KfJYqliEUqH8iC4qEAhull+H9FJNe70DQ1gD1UkGDMospWZO4mY1NwBJ0FcEZHvCiGY8XSCT5zWOOQqsHLwOZ1a7QaQAZ69p"
	keyJWT  = "MIIEpgIBAAKCAQEA3c1YP6WawwG18QF9B7cH1AXwQNOtWsOAUulkiLXHboGuLwu"
)

// 获取当前运行环境的宿主机的信息的签名，每一台机器都固定不变
// 读取失败，则程序无法正常执行
func GetDeployInfo() string {
	// 获取DMI信息
	dmiInfos := getDmiInfo()

	// xlog.Debug("dmi info", "arr", dmiInfos)

	// 检查是否成功读取到足够的DMI信息
	hasValidInfo := false
	for _, info := range dmiInfos {
		if info != "" {
			hasValidInfo = true
			break
		}
	}

	if !hasValidInfo {
		xlog.Error("无法获取有效的系统信息")
		os.Exit(1)
	}

	// 将所有DMI信息合并成一个字符串，用分号分隔
	ret := strings.Join(dmiInfos, ";")

	nospaceStr := strings.ReplaceAll(ret, " ", "")
	// 使用 base62编码
	return xutils.HmacSha256Hex(nospaceStr, []byte("deploy_info_with_sha256"))
}

func genSign(id, msg string) string {
	key := []byte(keySign)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(id))
	h.Write([]byte(""))
	h.Write([]byte(msg))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// 生成授权文件
type DeployClaims struct {
	jwt.RegisteredClaims
	DeployID  string // 客户 ID
	Signature string //客户 license
}

// 为私有部署，生成签名信息
// id  私有部署唯一标识，msg  私有部署的系统信息
func GenLicense(id, msg string) string {
	sign := genSign(id, msg)
	myClaims := &DeployClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour * 365 * 10)), //10 年有效期
		},
		DeployID:  id,
		Signature: sign,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, myClaims)
	license, err := token.SignedString([]byte(keyJWT))
	if err != nil {
		xlog.Error("gen failed:" + err.Error())
		return ""
	}
	// xlog.Debug("gen license ok : " + license)
	return license
}

// 校验自己的 license 是否合法: 从 license 里面解析 deployID，对比看是否匹配。并解析出签名，验证是否匹配。
func ParseLicense(id, license string) bool {

	tokenOut, err := jwt.ParseWithClaims(license, &DeployClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(keyJWT), nil
	})
	if err != nil {
		xlog.Error("license parse failed:" + err.Error())
		return false
	}
	xlog.Debug("parse id : ", id)
	xlog.Debug("parse license: ", license)
	claims, ok := tokenOut.Claims.(*DeployClaims)
	if ok {
		xlog.Debugf("id: %s, sign: %s", claims.DeployID, claims.Signature)
	} else {
		return false
	}

	// 1、校验 deployID
	if claims.DeployID != id {
		return false
	}

	// 2、校验 sign
	msg := GetDeployInfo()
	sign := genSign(id, msg)
	if claims.Signature != sign {
		return false
	}

	fmt.Println("parse license OK")
	return true
}
