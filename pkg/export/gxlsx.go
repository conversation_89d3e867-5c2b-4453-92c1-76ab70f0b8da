package export

import (
	"fmt"
	"os"
	"reflect"
	"regexp"
	"strings"

	"bs.com/app/pkg/bean"
	"bs.com/app/pkg/now"
	"bs.com/app/pkg/xlog"
	"github.com/xuri/excelize/v2"
)

var (
	titleCelKey = map[int]string{}
	colList     = []string{}
)

func init() {
	colList = []string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"}
	colList = append(colList, "AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM")
	colList = append(colList, "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ")

	titleCelKey = make(map[int]string)
	for k, cel := range colList {
		titleCelKey[k] = fmt.Sprintf("%v1", cel) // title 是首行
	}

}

// 获取title的位置
func getTitleMap(titleList []string) (titleMap map[string]string) {
	celKeyLen := len(titleCelKey)
	titleMap = make(map[string]string)
	for k, title := range titleList {
		if k < celKeyLen {
			titleMap[titleCelKey[k]] = title
		} else {
			xlog.Warn("not found this key")
		}
	}
	return
}

// 获取到data的位置
func getDataMap(dataList [][]interface{}) (dataMap map[string]interface{}) {
	dataMap = make(map[string]interface{})
	for row, datas := range dataList {
		for col, data := range datas {
			key := fmt.Sprintf("%v%v", colList[col], row+2)
			dataMap[key] = data
		}
	}
	return
}

// 导出到 excel 文件
func ExportXlsx(filename string, titleList []string, dataList [][]interface{}) (err error) {
	xlsx := excelize.NewFile()

	titles := getTitleMap(titleList)
	values := getDataMap(dataList)

	sheetName := "Sheet1"

	indexSheet, err := xlsx.NewSheet(sheetName)
	if err != nil {
		return err
	}
	for k, v := range titles {
		err = xlsx.SetCellValue(sheetName, k, v)
		if err != nil {
			return err
		}
	}
	for k, v := range values {
		err = xlsx.SetCellValue(sheetName, k, v)
		if err != nil {
			return err
		}
	}

	xlsx.SetActiveSheet(indexSheet)
	err = xlsx.SaveAs(filename)
	if err != nil {
		fmt.Println("save file : ", err)
	}
	return
}

// ========================================================================================================
/*
用法，按照如下格式定义 struct

type User struct {
	Name  string `excel:"A;width:20;required;header:用户名"`
	Age   int    `excel:"B;width:10;header:年龄"`
	Email string `excel:"C;width:30;header:邮箱地址"`
}
其中：A、B、C 是指所占列，width 是列宽，header 是标题栏；required：是否必须

// 使用 title 的时候，才需要指定这些样式
var styleTitleLine = &excelize.Style{
	Alignment: &excelize.Alignment{ // 对齐方式
		Horizontal: "center", // 水平对齐居中
		Vertical:   "center", // 垂直对齐居中
	},
	Fill: excelize.Fill{ // 背景颜色
		Type:    "pattern",
		Color:   []string{"#a0a0a0"},
		Pattern: 1,
	},
	Font: &excelize.Font{ // 字体
		Bold: true,
		Size: 16,
	},
}
*/

// 标题栏，灰色背景样式，居中
var headerStyle = &excelize.Style{
	// Font:      &excelize.Font{Size: 20, Bold: true},
	Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	Fill:      excelize.Fill{Type: "pattern", Color: []string{"A5A5A5"}, Pattern: 1},
}

var headerStyleRequired = &excelize.Style{
	Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	Fill:      excelize.Fill{Type: "pattern", Color: []string{"A5A5A5"}, Pattern: 1},
	Font: &excelize.Font{
		Color: "FF0000", // Red color
		Bold:  true,     // Bold font
	},
}

func GenExcelTemplate(structType interface{}, fileName string, sheetName string) error {
	f := excelize.NewFile()
	if sheetName == "" {
		sheetName = "Sheet1"
	}
	index, _ := f.NewSheet(sheetName)
	f.SetActiveSheet(index)

	t := reflect.TypeOf(structType)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	if t.Kind() != reflect.Struct {
		return fmt.Errorf("provided type is not a struct")
	}

	// 第一行表头
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		tag := field.Tag.Get("excel")
		if tag == "" {
			continue
		}

		// 解析 tag 中的列名、宽度和表头名称
		parts := strings.Split(tag, ";")
		column := parts[0]
		width := 0.0
		header := field.Name // 默认使用字段名作为表头名称

		// 匹配 "width:" 和 "header:" 后的内容
		widthRegex := regexp.MustCompile(`width:(\d+(?:\.\d+)?)`)
		headerRegex := regexp.MustCompile(`header:(.*)`)
		required := false
		for _, part := range parts {
			if part == "required" {
				required = true
			}
			if matches := widthRegex.FindStringSubmatch(part); len(matches) == 2 {
				fmt.Sscanf(matches[1], "%f", &width)
			}
			if matches := headerRegex.FindStringSubmatch(part); len(matches) == 2 {
				header = matches[1]
			}
		}

		// 设置列宽
		if width > 0 {
			f.SetColWidth(sheetName, column, column, width)
		}

		// 写入自定义表头名称
		cell := fmt.Sprintf("%s1", column)
		f.SetCellValue(sheetName, cell, header)

		// 设置 header style
		style1, _ := f.NewStyle(headerStyle)
		if required {
			// 判断 tag中是否有 required，如果有的话，将此列的表头字体设置为红色，加粗
			style1, _ = f.NewStyle(headerStyleRequired)
		}
		if err := f.SetCellStyle(sheetName, cell, cell, style1); err != nil {
			return fmt.Errorf("failed to set cell style: %v", err)
		}
	}

	// 文件后缀为 xlsx
	if !strings.HasSuffix(fileName, ".xlsx") {
		fileName = fileName + ".xlsx"
	}
	return f.SaveAs(fileName)
}

// data 必须是 slice，slice 里面必须是一个 struct 或者 struct_ptr
func ExportToExcel(data interface{}, fileName string, sheetName string) error {
	var f *excelize.File
	var err error

	// 检查文件是否存在
	_, err = os.Stat(fileName)
	fileExists := !os.IsNotExist(err)

	if fileExists {
		// 如果文件存在，打开现有文件
		f, err = excelize.OpenFile(fileName)
		if err != nil {
			xlog.Error("failed to open existing file", "err", err)
			return fmt.Errorf("failed to open existing file: %v", err)
		}
	} else {
		// 如果文件不存在，创建新文件
		f = excelize.NewFile()
	}

	if sheetName == "" {
		sheetName = "Sheet1"
	}

	// 检查 sheet 是否存在
	sheetIndex := -1
	for idx, name := range f.GetSheetList() {
		if name == sheetName {
			sheetIndex = idx
			break
		}
	}

	if sheetIndex == -1 {
		// Sheet 不存在，创建新的
		index, err := f.NewSheet(sheetName)
		if err != nil {
			return fmt.Errorf("invalid sheet name: %v", err)
		}
		f.SetActiveSheet(index)

		// 如果是新创建的文件且 sheetName 不是默认的 Sheet1，则删除默认工作表
		if !fileExists && sheetName != "Sheet1" {
			err = f.DeleteSheet("Sheet1")
			if err != nil {
				xlog.Error("failed to delete default sheet", "err", err)
				return fmt.Errorf("failed to delete default sheet: %v", err)
			}
		}
	} else {
		// Sheet 存在，设置为活动 sheet
		f.SetActiveSheet(sheetIndex)
		// 清空 sheet 中的现有数据
		f.DeleteSheet(sheetName)
		index, _ := f.NewSheet(sheetName)
		f.SetActiveSheet(index)
	}

	v := reflect.ValueOf(data)
	if v.Kind() != reflect.Slice {
		return fmt.Errorf("data must be a non-empty slice")
	}

	// 没有数据就返回
	if v.Len() == 0 {
		xlog.Warn("ExportToExcel: no data")
		// 保存文件
		return f.SaveAs(fileName)
	}

	structType := v.Index(0).Type()

	// 确保 structType 是一个 struct 或 struct 指针
	if structType.Kind() != reflect.Struct {
		if structType.Kind() == reflect.Ptr && structType.Elem().Kind() == reflect.Struct {
			structType = structType.Elem()
		} else {
			return fmt.Errorf("data must be a slice of structs or pointers to structs")
		}
	}

	// 第一行：设置 header
	// headers := []string{}
	headers := make(map[int]string) //key 是 struct 的 Filed序号
	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)
		tag := field.Tag.Get("excel")
		if tag == "" {
			continue
		}

		// 解析 tag 中的列名、宽度和表头名称
		parts := strings.Split(tag, ";")
		column := parts[0]
		headers[i] = column

		width := 0.0
		header := field.Name // 默认使用字段名作为表头名称

		// 匹配 "width:" 和 "header:" 后的内容
		widthRegex := regexp.MustCompile(`width:(\d+(?:\.\d+)?)`)
		headerRegex := regexp.MustCompile(`header:(.*)`)
		required := false
		for _, part := range parts {
			if part == "required" {
				required = true
			}
			if matches := widthRegex.FindStringSubmatch(part); len(matches) == 2 {
				fmt.Sscanf(matches[1], "%f", &width)
			}
			if matches := headerRegex.FindStringSubmatch(part); len(matches) == 2 {
				header = matches[1]
			}
		}

		// 设置列宽
		if width > 0 {
			if err := f.SetColWidth(sheetName, column, column, width); err != nil {
				return fmt.Errorf("failed to set column width: %v", err)
			}
		}

		// 写入自定义表头名称
		cell := fmt.Sprintf("%s1", column)
		if err := f.SetCellValue(sheetName, cell, header); err != nil {
			return fmt.Errorf("failed to set cell value: %v", err)
		}

		// 设置 header style
		style1, _ := f.NewStyle(headerStyle)
		if required {
			// 判断 tag中是否有 required，如果有的话，将此列的表头字体设置为红色，加粗
			style1, _ = f.NewStyle(headerStyleRequired)
		}
		if err := f.SetCellStyle(sheetName, cell, cell, style1); err != nil {
			return fmt.Errorf("failed to set cell style: %v", err)
		}
	}

	// 按行导出数据
	for i := 0; i < v.Len(); i++ {
		row := v.Index(i)
		if row.Kind() == reflect.Ptr {
			row = row.Elem()
		}
		// 逐个 filed 填充
		for j, column := range headers {
			cell := fmt.Sprintf("%s%d", column, i+2)
			fieldType := row.Type().Field(j)
			jsonTag := fieldType.Tag.Get("json")

			// 时间戳需要格式化
			var value any
			if jsonTag == "timestamp" {
				timestamp := row.Field(j).Interface().(int64)
				value = now.TimeUTCtoLocal3(timestamp)
			} else {
				value = row.Field(j).Interface()
			}
			if err := f.SetCellValue(sheetName, cell, value); err != nil {
				return fmt.Errorf("failed to set cell value: %v", err)
			}
		}
	}

	// 保存文件
	return f.SaveAs(fileName)
}

func ExportTsData2Excel(data []*bean.TsData, valueColumnName, fileName, sheetName string) error {
	var f *excelize.File
	var err error

	// 检查文件是否存在
	_, err = os.Stat(fileName)
	fileExists := !os.IsNotExist(err)

	if fileExists {
		// 如果文件存在，打开现有文件
		f, err = excelize.OpenFile(fileName)
		if err != nil {
			xlog.Error("failed to open existing file", "err", err)
			return fmt.Errorf("failed to open existing file: %v", err)
		}
	} else {
		// 如果文件不存在，创建新文件
		f = excelize.NewFile()
	}

	if sheetName == "" {
		sheetName = "Sheet1"
	}

	// 检查 sheet 是否存在
	sheetIndex := -1
	for idx, name := range f.GetSheetList() {
		if name == sheetName {
			sheetIndex = idx
			break
		}
	}

	if sheetIndex == -1 {
		// Sheet 不存在，创建新的
		index, err := f.NewSheet(sheetName)
		if err != nil {
			return fmt.Errorf("failed to create sheet: %v", err)
		}
		f.SetActiveSheet(index)

		// 如果是新创建的文件且 sheetName 不是默认的 Sheet1，则删除默认工作表
		if !fileExists && sheetName != "Sheet1" {
			err = f.DeleteSheet("Sheet1")
			if err != nil {
				xlog.Error("failed to delete default sheet", "err", err)
				return fmt.Errorf("failed to delete default sheet: %v", err)
			}
		}
	} else {
		// Sheet 存在，设置为活动 sheet
		f.SetActiveSheet(sheetIndex)
		// 清空 sheet 中的现有数据
		f.DeleteSheet(sheetName)
		index, _ := f.NewSheet(sheetName)
		f.SetActiveSheet(index)
	}

	// 第一行：设置 header
	headerMap := map[string]string{
		"A": "时间",
		"B": valueColumnName,
	}
	for column, header := range headerMap {
		// 设置列宽
		if err := f.SetColWidth(sheetName, column, column, 20); err != nil {
			return fmt.Errorf("failed to set column width: %v", err)
		}
		// 写入自定义表头名称
		cell := fmt.Sprintf("%s1", column)
		if err := f.SetCellValue(sheetName, cell, header); err != nil {
			return fmt.Errorf("failed to set cell value: %v", err)
		}
		// 设置 header style
		style1, _ := f.NewStyle(headerStyle)
		if err := f.SetCellStyle(sheetName, cell, cell, style1); err != nil {
			return fmt.Errorf("failed to set cell style: %v", err)
		}
	}

	// 按行导出数据
	for i := 0; i < len(data); i++ {
		cellA := fmt.Sprintf("A%d", i+2)
		cellB := fmt.Sprintf("B%d", i+2)

		timeStr := now.TimeUTCtoLocal3(data[i].Timestamp)
		if err := f.SetCellValue(sheetName, cellA, timeStr); err != nil {
			return fmt.Errorf("failed to set cell value: %v", err)
		}
		if err := f.SetCellValue(sheetName, cellB, data[i].Value); err != nil {
			return fmt.Errorf("failed to set cell value: %v", err)
		}
	}

	// 保存文件
	return f.SaveAs(fileName)
}

// outSlice 必须是一个指针，指向导出数据对应的 struct 的 slice
func ImportFromExcel(fileName string, sheetName string, outSlice interface{}, startRow int) error {
	f, err := excelize.OpenFile(fileName)
	if err != nil {
		return err
	}

	if sheetName == "" {
		sheetName = f.GetSheetName(f.GetActiveSheetIndex())
	}

	// rows, err := f.GetRows(sheetName, excelize.Options{RawCellValue: true})
	rows, err := f.GetRows(sheetName) //行尾连续为空的单元格将被跳过
	if err != nil {
		return err
	}

	// xlog.Info("dump import excel---0", "rows", rows)

	// 跳过第一行header
	if startRow == 0 {
		startRow = 1 //默认 header 只占 1 行
	}
	v := reflect.ValueOf(outSlice)
	if v.Kind() != reflect.Ptr || v.Elem().Kind() != reflect.Slice {
		return fmt.Errorf("outSlice must be a pointer to a slice")
	}

	sliceValue := v.Elem()
	elemType := sliceValue.Type().Elem()

	for rowIndex, row := range rows[startRow:] {
		xlog.Debug("====== import from excel, dump row", "row", rowIndex, "content", row)
		instance := reflect.New(elemType).Elem()
		for i := 0; i < elemType.NumField(); i++ {
			field := elemType.Field(i)
			tag := field.Tag.Get("excel")
			if tag == "" {
				continue
			}

			parts := strings.Split(tag, ";")
			column := parts[0]
			required := false
			header := ""

			// xlog.Info("dump import excel---1", "row", row, "parts", parts)

			// 检查是否有 required 标记
			for _, part := range parts {
				if part == "required" {
					required = true
				}
				if strings.HasPrefix(part, "header:") {
					header = strings.TrimPrefix(part, "header:")
				}
			}

			colIdx := strings.Index("ABCDEFGHIJKLMNOPQRSTUVWXYZ", column)
			if colIdx < 0 || colIdx >= len(row) {
				if required {
					return fmt.Errorf("%s 是必填项", header)
				}
				continue
			}

			cellValue := row[colIdx]
			// xlog.Info("dump import excel---2", "required", required, "cellValue", cellValue)
			if required && cellValue == "" {
				return fmt.Errorf("row %d, column %s: required field is empty", startRow+rowIndex+1, column)
			}

			fieldVal := instance.Field(i)
			if fieldVal.CanSet() {
				switch fieldVal.Kind() {
				case reflect.String:
					fieldVal.SetString(cellValue)
				case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
					var intVal int64
					fmt.Sscanf(cellValue, "%d", &intVal)
					fieldVal.SetInt(intVal)
				case reflect.Float32, reflect.Float64:
					var floatVal float64
					fmt.Sscanf(cellValue, "%f", &floatVal)
					fieldVal.SetFloat(floatVal)
					// 其他类型可以根据需要添加
				}
			}
		}
		sliceValue.Set(reflect.Append(sliceValue, instance))
	}

	return nil
}
