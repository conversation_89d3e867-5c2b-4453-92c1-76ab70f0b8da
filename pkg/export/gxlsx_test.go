package export

import (
	"fmt"
	"testing"

	"github.com/xuri/excelize/v2"
)

func TestExportXlsx(t *testing.T) {
	filename := "new_test.xlsx"
	titleList := []string{"测试1", "测试2", "测试3", "测试4", "测试5"}
	data := [][]interface{}{
		{"aaa", "四大", 234, 3434.34},
		{"bbb", 234, "啥啥啥", 0},
	}
	err := ExportXlsx(filename, titleList, data)
	if err != nil {
		fmt.Println(err)
	}
}

func TestChart(t *testing.T) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	for idx, row := range [][]interface{}{
		{nil, "Apple", "Orange", "Pear"},
		{"Small", 2, 3, 3},
	} {
		cell, err := excelize.CoordinatesToCellName(1, idx+1)
		if err != nil {
			fmt.Println(err)
			return
		}
		f.Set<PERSON>heet<PERSON>ow("Sheet1", cell, &row)
	}
	if err := f.Add<PERSON>("Sheet1", "F1", &excelize.Chart{
		Type: excelize.Col3DClustered,
		Series: []excelize.ChartSeries{
			{
				Name:       "Sheet1!$A$2",
				Categories: "Sheet1!$B$1:$D$1",
				Values:     "Sheet1!$B$2:$D$2",
			},
		},
		Title: []excelize.RichTextRun{
			{
				Text: "Fruit 3D Clustered Column Chart",
			},
		},
	}); err != nil {
		fmt.Println(err)
		return
	}
	// Save spreadsheet by the given path.
	if err := f.SaveAs("Book1.xlsx"); err != nil {
		fmt.Println(err)
	}
}

// ===========================================================================
// required 说明此列数据不可以为空
type User struct {
	Name  string `excel:"A;width:20;required;header:用户名"`
	Age   int    `excel:"B;width:10;header:年龄"`
	Email string `excel:"C;width:30;header:邮箱地址"`
}

var users = []*User{
	{"Alice", 30, "<EMAIL>"},
	{"Bob", 25, "<EMAIL>"},
}

// 导出模板
func TestGenExcelTemp(t *testing.T) {
	err := GenExcelTemplate(User{}, "template.xlsx", "Sheet1")
	if err != nil {
		fmt.Println("Failed to generate template:", err)
		return
	}
}

// 导出数据
func TestExportExcel(t *testing.T) {
	err := ExportToExcel(users, "./users.xlsx", "Sheet1")
	if err != nil {
		fmt.Println("Failed to export:", err)
		return
	}
}

// 导入数据
func TestImportExcel(t *testing.T) {
	var importedUsers []User
	err := ImportFromExcel("./users.xlsx", "Sheet1", &importedUsers, 0)
	if err != nil {
		fmt.Println("Failed to import:", err)
		return
	}

	fmt.Println(importedUsers)
}
