package xwebsocket

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"bs.com/app/pkg/xjwt"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var (
	dp *dispatcher //ws调度器
)

const (
	// errorCount    = 5                     //错误次数
	interval = 3 * time.Minute //心跳检查间隔
	// keepAliveType = websocket.PingMessage //心跳类型
)

type connection struct {
	r        *http.Request
	server   *Server
	ws       *websocket.Conn       //ws连接实例
	clientId string                //ws连接实例唯一标识
	closed   bool                  //ws连接已关闭
	send     chan []byte           //发送信息管道
	topics   map[string]WsReq      //订阅信息
	pingNum  []int64               // 心跳检查时间内，收到的ping数量
	userInfo *xjwt.CustomJwtClaims // 当前连接的用户信息
	// pingErrs []int64           //发送的心跳失败次数
	// pongErrs []int64           //收到的心跳失败次数
}

// ws调度器
type dispatcher struct {
	s2cGzip  bool                     //发送的信息是否gzip压缩
	connPool map[string]*connection   //ws连接池 map[clientId]*connection
	subPool  map[string][]*connection //订阅池 map[topic]map[clientId]*connection
	sendSub  chan WsResp              //发送订阅
	mu       sync.Mutex               // 互斥锁
}

// 创建ws调度器
func StartWsDp(s2cGzip bool) {
	if dp == nil {
		dp = newDp(s2cGzip)
	}
}

// 创建ws调度器
func newDp(s2cGzip bool) *dispatcher {
	d := &dispatcher{
		s2cGzip:  s2cGzip,
		connPool: make(map[string]*connection),
		subPool:  make(map[string][]*connection),
		sendSub:  make(chan WsResp, 10000),
	}
	go func(d *dispatcher) {
		for {
			select {
			//发送订阅
			case subBody := <-d.sendSub:
				subs, ok := d.subPool[subBody.Topic]
				if !ok {
					break
				}
				d.mu.Lock()
				for _, conn := range subs {
					if !checkConnUserInfo(conn, subBody.UserInfo) {
						// 如果不是当前用户的公司，则不发送信息
						continue
					}
					tmpRespBody := getRespBody(conn, subBody)
					conn.sendMessage(tmpRespBody)
				}
				d.mu.Unlock()
			}
		}
	}(d)
	return d
}

func checkConnUserInfo(conn *connection, userInfo *xjwt.CustomJwtClaims) bool {
	if conn == nil || userInfo == nil {
		return false
	}
	if conn.userInfo == nil {
		return false
	}
	// 如果连接的用户公司id不等于需要发送的公司id
	if conn.userInfo.CompanyID != userInfo.CompanyID {
		return false
	}
	return true
}

func getRespBody(conn *connection, resp WsResp) WsResp {
	wsReq := conn.topics[resp.Topic] // 必须区分clientid 和topic
	resp.RequestID = wsReq.RequestID
	resp.UserInfo = nil // 前面判断过了，
	return resp
}

// 创建ws连接
func NewConn(ctx context.Context, server *Server, r *http.Request, wsConn *websocket.Conn, userInfo *xjwt.CustomJwtClaims) *connection {
	var clientId string
	for {
		clientId = xutils.UUID()
		if _, ok := dp.connPool[clientId]; !ok {
			break
		}
	}
	conn := &connection{
		server:   server,
		ws:       wsConn,
		r:        r,
		clientId: clientId,
		send:     make(chan []byte, 10000),
		topics:   make(map[string]WsReq),
		userInfo: userInfo,
		pingNum:  []int64{},
	}
	dp.connPool[clientId] = conn
	xlog.Info("[ws]创建连接成功", "RemoteAddr", wsConn.RemoteAddr().String(), "clientId", clientId)
	// resp := WsResp{StatusCode: http.StatusOK}
	// clientToken := trace.TraceIDFromContext(ctx)
	// resp.Handler = map[string]string{"Traceparent": clientToken}
	// conn.sendMessage(resp)
	return conn
}

/*
 * 检查当前用户有的 topic，是否已经取消了订阅
 */
func UserTopicIsUnSub(ctx *gin.Context, topic string) bool {
	userInfo, err := xjwt.GetJwtClaimsFromCtx(ctx)
	if err != nil {
		return true
	}
	subs, ok := dp.subPool[topic]
	if !ok {
		// 已经无法找到该topic了，证明已经取消了订阅
		return true
	}
	for _, conn := range subs {
		if conn.userInfo.UID == userInfo.UID {
			// 未取消订阅
			return false
		}
	}
	// 如果全部都没有，那么就已经取消
	return true
}

func (c *connection) errorSend(data error) {
	resp := WsResp{
		Code:   http.StatusBadRequest,
		WsBody: WsBody{Body: data.Error()},
	}
	c.sendMessage(resp)
}

// 将请求体转换为io.ReadCloser类型
func getRequestBody(body interface{}) (io.ReadCloser, error) {
	var reqBody io.ReadCloser
	if body != nil {
		switch v := body.(type) {
		case string:
			reqBody = io.NopCloser(bytes.NewBufferString(v))
		case []byte:
			reqBody = io.NopCloser(bytes.NewBuffer(v))
		case map[string]interface{}:
			bodyBytes, err := json.Marshal(body)
			if err != nil {
				return nil, err
			}
			reqBody = io.NopCloser(bytes.NewReader(bodyBytes))
		default:
			// 处理其他类型
		}
	}
	return reqBody, nil
}

// 开启读取进程
func (c *connection) StartRead() {
	xlog.Info("ws start read...")
	defer func() {
		xutils.Recovery("xwebsocket startRead")
		c.Close("read message error")
	}()
	for {
		_, message, err := c.ws.ReadMessage()
		if err != nil {
			break
		}
		// xlog.Infof("[ws] read message:%s clientId:%s", string(message), c.clientId)
		var body WsReq
		err = json.Unmarshal(message, &body)
		if err != nil {
			c.errorSend(errors.New("WsReq格式不对"))
			continue
		}
		if body.Type == WTPing {
			c.pongSend()
			continue
		}
		c.handleRequest(body)
	}
}

// 开启发送进程
func (c *connection) StartWrite() {
	ticker := time.NewTicker(interval)
	defer func() {
		xutils.Recovery("xwebsocket startWrite")
		ticker.Stop()
	}()
	for {
		select {
		// 检查心跳
		case <-ticker.C:
			if c.closed {
				return
			}
			// var err error
			err := c.checkHeartbeat()
			if err != nil {
				c.Close("connection timeout")
				return
			}
		//发送信息
		case message := <-c.send:
			if c.closed {
				xlog.Error("websocket is closed. error send message :" + string(message))
				return
			}
			if err := c.writeMessage(websocket.TextMessage, message); err != nil {
				c.Close("send message error")
				return
			}
		}
	}
}

// 关闭ws连接
func (c *connection) Close(msg string) {
	dp.mu.Lock()
	defer dp.mu.Unlock()
	_, ok := dp.connPool[c.clientId]
	if ok || !c.closed {
		c.closed = true
		close(c.send)
		delete(dp.connPool, c.clientId)
		for topic, subs := range dp.subPool {
			for i := 0; i < len(subs); i++ {
				if subs[i].clientId == c.clientId {
					subs = append(subs[:i], subs[i+1:]...)
					break
				}
			}
			dp.subPool[topic] = subs
		}
		c.ws.Close()
		xlog.Info("[ws]关闭连接", "clientId", c.clientId)
	}
}

func (c *connection) handleRequest(body WsReq) {
	if err := isDataComplete(body.Type, body); err != nil {
		c.errorSend(err)
		return
	}
	if len(body.Handler) > 0 {
		for k, v := range body.Handler {
			c.r.Header.Set(k, v)
		}
	}
	switch body.Type {
	case Control:
		downControl(c, body)
	case Sub:
		subscribeHandle(c, body)
	case UnSub:
		unSubscribeHandle(c, body)
	default:
	}
}

func isDataComplete(wsType WsType, body WsReq) error {
	if wsType == "" {
		return errors.New("type is  null")
	}
	switch wsType {
	case Control:
		if body.Topic == "" || body.Method == "" || body.Body == "" {
			return errors.New("topic|method|body is  null")
		}
	case Sub, UnSub:
		if body.Topic == "" {
			return errors.New("topic  is  null")
		}
	case Pub:
		return errors.New("还未实现")
	default:
	}
	return nil
}

func downControl(c *connection, body WsReq) {
	reqBody, err := getRequestBody(body.Body)
	if err != nil {
		return
	}
	bodyBytes, err := json.Marshal(body.Body)
	if err != nil {
		return
	}

	length := len(bodyBytes)
	header := c.r.Header
	header.Set("Content-Type", "application/json")
	header.Set("Content-Length", strconv.Itoa(length))
	r := &http.Request{
		Method: body.Method,
		Host:   c.r.Host,
		URL: &url.URL{
			Path: body.Topic,
		},
		Header:        header,
		Body:          reqBody,
		ContentLength: int64(length),
	}
	w := response{req: &body, resp: WsResp{WsBody: WsBody{Handler: map[string]string{}, Type: ControlRet}}}
	c.server.ServeHTTP(&w, r)
	// if token := w.Header().Get(ctxs.UserSetTokenKey); token != "" { //登录态保持更新
	// 	c.r.Header.Set(ctxs.UserSetTokenKey, token)
	// }

	c.sendMessage(w.resp)
}

func (c *connection) checkHeartbeat() error {
	// 心跳检查。如果超过5(interval)分钟，收到的ping数量依然为0，则认为链接已经断开
	// xlog.Debug("hearbeat ping num", "len", len(c.pingNum))
	if len(c.pingNum) <= 0 {
		return errors.New("time out and ping number is 0")
	}
	c.pingNum = []int64{} // 重置为空
	return nil
}

// 回应ping心跳
func (c *connection) pongSend() error {
	// xlog.Infof("[ws] message:%s clientId:%s", string("ping"), c.clientId)
	time.Sleep(1 * time.Second)
	c.pingNum = append(c.pingNum, time.Now().Unix())
	body := WsBody{
		Type: WTPong,
	}
	pongResp := WsResp{
		Code:   http.StatusOK,
		WsBody: body,
	}
	c.sendMessage(pongResp)
	return nil
}

// 发送信息
func (c *connection) sendMessage(body WsResp) {
	message, _ := json.Marshal(body)
	if !c.closed {
		c.send <- message
	}
}

// 写消息
func (c *connection) writeMessage(messageType int, message []byte) error {
	if message == nil {
		xlog.Warn("[ws]error message: is  null")
	}
	switch messageType {
	case websocket.PingMessage, websocket.PongMessage:
		err := c.ws.WriteControl(messageType, message, time.Time{})
		if err != nil {
			xlog.Warn("[ws]ping pong failed", "message", string(message), "cliend_id", c.clientId)
		} else {
			c.pingNum = append(c.pingNum, time.Now().Unix())
		}
	case websocket.TextMessage:
		err := c.ws.WriteMessage(messageType, message)
		if err != nil {
			xlog.Warn("[ws] write failed", "message", string(message), "cliend_id", c.clientId)
		} else {
			// 能写成功，也证明链接还未断开
			c.pingNum = append(c.pingNum, time.Now().Unix())
		}
	}

	return nil
}

// ---------------
// 发送订阅信息
func SendSub(ctx *gin.Context, body WsResp) {
	// clientToken := trace.TraceIDFromContext(ctx)
	// body.Handler = map[string]string{"Traceparent": clientToken}
	uInfo, err := xjwt.GetJwtClaimsFromCtx(ctx)
	if err != nil {
		xlog.Error("get user info from ctx errror:" + err.Error())
		return
	}
	body.UserInfo = uInfo
	for {
		select {
		case dp.sendSub <- body:
			// 发送成功，则直接返回
			return
		default:
			// 取出一个，下次循环则可以
			xlog.Warn("ws dispatcher sendSub is full", "chan_len", len(dp.sendSub), "message", <-dp.sendSub)
		}
	}
}
