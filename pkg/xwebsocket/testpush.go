package xwebsocket

// func timerPush(userInfo *xjwt.CustomJwtClaims) {
// 	xlog.Info("/topic/test..., timer push...")
// 	count := 0
// 	for range time.Tick(time.Second * 3) {

// 		if UserTopicIsUnSub(TestTopic, userInfo) {
// 			break
// 		}

// 		data := map[string]interface{}{"test": fmt.Sprintf("count:%d", count)}

// 		body := WsResp{
// 			StatusCode: http.StatusOK,
// 			UserInfo:   userInfo,
// 			WsBody: WsBody{
// 				Topic: TestTopic,
// 				Body:  data,
// 				Type:  RespTypeResult,
// 			},
// 		}
// 		SendSub(context.Background(), body)
// 		count += 1
// 	}
// }
