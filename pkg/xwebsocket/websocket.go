package xwebsocket

import (
	"bs.com/app/pkg/xjwt"
)

type WsType string

const (
	WTPing WsType = "ping"
	WTPong WsType = "pong"
)

const (
	Sub        WsType = "up.sub"          //订阅
	SubRet     WsType = "down.subRet"     //订阅回复
	Pub        WsType = "down.pub"        //发布
	Control    WsType = "up.control"      //控制
	ControlRet WsType = "down.controlRet" //控制回复
	UnSub      WsType = "up.unSub"        //取消订阅
	UnSubRet   WsType = "down.unSubRet"   //取消订阅回复
)

const (
	// 返回结果类型, 由前端决定
	RespTypeComplate WsType = "complete"
	RespTypeResult   WsType = "result"
	RespTypeError    WsType = "error"
)

type (
	WsBody struct {
		Handler   map[string]string `json:"handler,omitempty"`
		Type      WsType            `json:"type,omitempty"`  //req 请求类型
		Topic     string            `json:"topic,omitempty"` //url路径或发布及订阅的主题
		Body      any               `json:"body,omitempty"`  //消息体
		RequestID string            `json:"requestId,omitempty"`
	}
	WsReq struct {
		// Method specifies the HTTP method (GET, POST, PUT, etc.).
		// For client requests, an empty string means GET.
		//
		// Go's HTTP client does not support sending a request with
		// the CONNECT method. See the documentation on Transport for
		// details.
		Method string `json:"method"`
		WsBody
	}

	WsResp struct {
		Code     int                   `json:"code"` // http状态码 200
		UserInfo *xjwt.CustomJwtClaims // 将用户信息放入resp中，确认需要返回的属于该用户,从token中获取到
		WsBody
	}

	Ping struct {
		Ping int64 `json:"ping"` //时间戳
	}

	Pong struct {
		Pong int64 `json:"pong"` //时间戳
	}
)
