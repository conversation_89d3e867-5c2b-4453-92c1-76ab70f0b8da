package xwebsocket

import (
	"net/http"
	"time"

	"bs.com/app/pkg/xlog"
)

const (
	_defaultReadTimeout     = 5 * time.Second
	_defaultWriteTimeout    = 60 * time.Second
	_defaultAddr            = ":80"
	_defaultShutdownTimeout = 3 * time.Second
)

type Server struct {
	name            string
	server          *http.Server
	shutdownTimeout time.Duration
}

func (s *Server) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	xlog.Info("start http gin server", "name", s.name)
	go func() {
		err := s.server.ListenAndServe()
		if err != nil {
			if err == http.ErrServerClosed {
				xlog.Info("http server stoped")
			} else {
				xlog.Error("http server failed: " + err.Error())
			}
		}
	}()

}

// NewServer
func NewServer(name string, handler http.Handler, opts ...Option) *Server {
	httpServer := &http.Server{
		Handler:      handler,
		ReadTimeout:  _defaultReadTimeout,
		WriteTimeout: _defaultWriteTimeout,
		Addr:         _defaultAddr,
	}

	s := &Server{
		name:            name,
		server:          httpServer,
		shutdownTimeout: _defaultShutdownTimeout,
	}
	// Custom options
	for _, opt := range opts {
		opt(s)
	}
	return s
}
