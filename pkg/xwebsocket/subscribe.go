package xwebsocket

import (
	"errors"
	"net/http"

	"bs.com/app/pkg/xlog"
)

func subscribeHandle(c *connection, bd WsReq) {
	//校验订阅topic
	topic, err := wsCheckSub(bd.Topic)
	var resp WsResp
	resp.WsBody = bd.WsBody
	resp.WsBody.RequestID = bd.RequestID
	resp.WsBody.Type = SubRet
	if err != nil {
		c.errorSend(errors.New("error  subscribe  topic"))
		return
	} else {
		c.addSubscribe(topic, bd) //添加订阅
		resp.Code = http.StatusOK
	}
	xlog.Info("sunb scribe success", "resp_topic", resp.Topic)
	c.sendMessage(resp)

	// 当订阅后，需要立马执行的topic
	// go selectTopicDo(topic, c.userInfo)
}

// 处理取消订阅
func unSubscribeHandle(c *connection, bd WsReq) {
	//校验订阅topic
	xlog.Debug("----------------- ws unsub -------------------")
	topic, err := wsCheckSub(bd.Topic)
	var resp WsResp
	resp.WsBody = bd.WsBody
	resp.WsBody.RequestID = bd.RequestID
	resp.WsBody.Type = UnSubRet
	if err != nil {
		c.errorSend(errors.New("error  unSubscribe  topic"))
		return
	} else {
		c.unSubscribe(topic) //取消订阅
		resp.Code = http.StatusOK
		c.sendMessage(resp)
		return
	}
}

// 校验订阅topic合法性
func wsCheckSub(sub string) (topic string, err error) {
	//代码逻辑
	return sub, nil
}

// 添加订阅
func (c *connection) addSubscribe(topic string, bd WsReq) {
	dp.mu.Lock()
	defer dp.mu.Unlock()
	_, ok := dp.connPool[c.clientId]
	if !ok {
		return
	}
	subs, ok := dp.subPool[topic]
	if !ok {
		subs = []*connection{}
		subs = append(subs, c)
		dp.subPool[topic] = subs
	} else {
		subs = append(subs, c)
		dp.subPool[topic] = subs
	}

	xlog.Info("add subscribe", "clientId", c.clientId, "topic", dp.subPool[topic])
	c.topics[topic] = bd
}

// 取消订阅
func (c *connection) unSubscribe(topic string) {
	dp.mu.Lock()
	defer dp.mu.Unlock()
	delete(c.topics, topic)
	_, ok := dp.connPool[c.clientId]
	if !ok {
		return
	}
	subs, ok := dp.subPool[topic]
	if ok {
		for i := 0; i < len(subs); i++ {
			if subs[i].clientId == c.clientId {
				subs = append(subs[:i], subs[i+1:]...)
				break
			}
		}
		if len(subs) <= 0 {
			delete(dp.subPool, topic)
		} else {
			dp.subPool[topic] = subs
		}

	}
}

// // 根据topic参数进行选择对应的函数功能
// func selectTopicDo(topic string, userInfo *xjwt.CustomJwtClaims) {
// 	xlog.Info("current topic:", topic)
// 	switch topic {
// 	case TestTopic:
// 		// 测试连接使用
// 		timerPush(userInfo)
// 	// case xxx:
// 	// func()
// 	default:
// 		xlog.Debug("current topic: ", topic)
// 	}
// }
