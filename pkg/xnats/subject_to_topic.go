package xnats

import (
	"strings"
	"unicode"
)

// ConvertNATSSubjectToMQTTTopic 将 NATS JetStream 主题转换为 MQTT 主题
// 示例:
//   - "devices.*.status" -> "devices/+/status"
//   - "sensors.room1.>" -> "sensors/room1/#"
//   - "a.b.c" -> "a/b/c"
func SubjectToTopic(natsSubject string) string {
	// 特殊处理空字符串
	if natsSubject == "" {
		return ""
	}

	// 分割 NATS 主题
	parts := strings.Split(natsSubject, ".")

	// 转换每个部分
	for i, part := range parts {
		switch part {
		case "*":
			parts[i] = "+"
		case ">":
			// NATS 的 ">" 只能出现在最后位置
			if i == len(parts)-1 {
				parts[i] = "#"
			} else {
				// 非法位置，转换为普通字符
				parts[i] = sanitizeMQTTTopicPart(part)
			}
		default:
			parts[i] = sanitizeMQTTTopicPart(part)
		}
	}

	// 重新组合
	return strings.Join(parts, "/")
}

// sanitizeMQTTTopicPart 清理 MQTT 主题部分中的非法字符
func sanitizeMQTTTopicPart(part string) string {
	var builder strings.Builder

	for _, r := range part {
		// MQTT 允许的字符: 字母、数字、下划线、斜杠、+、#
		// 但 + 和 # 只能作为通配符，不能出现在普通部分
		if unicode.IsLetter(r) || unicode.IsDigit(r) || r == '_' || r == '-' {
			builder.WriteRune(r)
		} else {
			// 替换其他字符为下划线
			builder.WriteRune('_')
		}
	}

	return builder.String()
}
