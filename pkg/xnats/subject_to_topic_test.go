package xnats

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestConvertNATSSubjectToMQTTTopic(t *testing.T) {
	tests := []struct {
		name     string
		nats     string
		expected string
	}{
		{"simple path", "a.b.c", "a/b/c"},
		{"single wildcard", "devices.*.status", "devices/+/status"},
		{"multi wildcard at end", "sensors.room1.>", "sensors/room1/#"},
		{"multi wildcard not at end", "sensors.>.status", "sensors/_/status"}, // 非法位置转换为普通字符
		{"mixed case", "Devices.Room1.Temperature", "Devices/Room1/Temperature"},
		{"special chars", "data.$room.status", "data/_room/status"},
		{"empty", "", ""},
		{"root wildcard", ">", "#"},
		{"root single wildcard", "*", "+"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, SubjectToTopic(tt.nats))
		})
	}
}
