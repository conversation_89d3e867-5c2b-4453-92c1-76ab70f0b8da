package xnats

import (
	"context"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/nats-io/nats.go/jetstream"
)

const (
	STREAM_BUSINESS = "business" // 业务消息
	STREAM_THING    = "thing"    // 物联网消息
	STREAM_TEMP     = "temp"     // 临时消息
)

// NatsClient 封装了 NATS JetStream 的常用操作，可以直接用 nc pub 和 sub，也可以用 js 做一些消息队列的操作
type NatsClient struct {
	nc *nats.Conn
	js jetstream.JetStream
}

// StreamConfig 流配置
type StreamConfig struct {
	Name        string        // 流名称
	Subjects    []string      // 主题列表
	Description string        // 描述
	MaxAge      time.Duration // 消息最大存活时间
}

// ConsumerConfig 消费者配置
// durable 不为空的时候，离线消息不丢失
// 每个服务实例使用不同的 durable name，这样每个实例都会收到所有消息，用于广播消息
// 多个服务实例使用相同的 durable name，这样消息会在实例间分发，用于队列消费（负载均衡）

// 确认策略
// AckNonePolicy ：不需要 ack，消息发出后立即被认为已处理（可能丢消息，性能最高）
// AckAllPolicy ：一次 ack 会自动确认该消费者在当前会话中之前的所有未确认消息
// AckExplicitPolicy ：每条消息都必须明确调用 msg.Ack() 来确认

// subject 规则
// nats 的 subject 是 由英文句点 . 分隔的多层字符串；
// 通配符； * 匹配单层; > 匹配当前及所有子层

// ReplayPolicy 重放策略类型：
// ReplayInstantPolicy（即时策略），这是默认策略，消费者只接收从连接时刻开始的新消息，不会重放历史消息，适用于实时处理场景。
// ReplayOriginalPolicy（原始策略），消费者会按照消息原始发布的时间间隔重放历史消息，保持原始的时间间隔，模拟消息原始发布的时序，适用于需要按原始时序重放的场景。

// 使用场景
// ReplayInstantPolicy：适用于实时消息处理，如设备状态监控、实时告警等
// ReplayOriginalPolicy：适用于需要重现历史事件序列的场景，如数据回放、调试分析等

type ConsumerConfig struct {
	Name          string                 // 消费者名称（可选，为空则创建临时消费者）
	Durable       string                 // 持久化名称
	FilterSubject string                 // 过滤主题
	AckPolicy     jetstream.AckPolicy    // 确认策略
	AckWait       time.Duration          // 确认等待时间
	MaxDeliver    int                    // 最大投递次数
	ReplayPolicy  jetstream.ReplayPolicy // 重放策略
}

// MessageHandler 消息处理函数类型
type MessageHandler func(msg jetstream.Msg) error

// NewJetStreamClient 创建新的 JetStream 客户端
func NewJetStreamClient(url string) (*NatsClient, error) {
	connectTimeout := 10 * time.Second
	maxReconnects := -1 // 无限重连
	reconnectWait := 2 * time.Second
	// 设置连接选项
	opts := []nats.Option{
		nats.MaxReconnects(maxReconnects),
		nats.ReconnectWait(reconnectWait),
		nats.Timeout(connectTimeout),
	}

	// 连接到 NATS 服务器
	nc, err := nats.Connect(url, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to NATS: %w", err)
	}

	// 创建 JetStream 上下文
	js, err := jetstream.New(nc)
	if err != nil {
		nc.Close()
		return nil, fmt.Errorf("failed to create JetStream context: %w", err)
	}

	return &NatsClient{
		nc: nc,
		js: js,
	}, nil
}

// CreateStream 创建流
func (c *NatsClient) CreateStream(ctx context.Context, config StreamConfig) error {
	streamConfig := jetstream.StreamConfig{
		Name:        config.Name,
		Subjects:    config.Subjects,
		Description: config.Description,
	}

	if config.MaxAge > 0 {
		streamConfig.MaxAge = config.MaxAge
	}

	_, err := c.js.CreateOrUpdateStream(ctx, streamConfig)
	if err != nil {
		return fmt.Errorf("failed to create stream %s: %w", config.Name, err)
	}

	return nil
}

// GetStream 获取流
func (c *NatsClient) GetStream(ctx context.Context, name string) (jetstream.Stream, error) {
	stream, err := c.js.Stream(ctx, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get stream %s: %w", name, err)
	}
	return stream, nil
}

// Publish 发布消息（同步）
// pub 消息不需要指定 stream，nats 自己通过 subject 匹配到 stream
func (c *NatsClient) Publish(ctx context.Context, subject string, data []byte) (*jetstream.PubAck, error) {
	ack, err := c.js.Publish(ctx, subject, data)
	if err != nil {
		return nil, fmt.Errorf("failed to publish message to %s: %w", subject, err)
	}
	return ack, nil
}

// PublishAsync 发布消息（异步）
func (c *NatsClient) PublishAsync(ctx context.Context, subject string, data []byte) (jetstream.PubAckFuture, error) {
	future, err := c.js.PublishAsync(subject, data)
	if err != nil {
		return nil, fmt.Errorf("failed to publish async message to %s: %w", subject, err)
	}
	return future, nil
}

// CreateConsumer 创建消费者
func (c *NatsClient) CreateConsumer(ctx context.Context, streamName string, config ConsumerConfig) (jetstream.Consumer, error) {
	consumerConfig := jetstream.ConsumerConfig{
		// AckNonePolicy ：不需要 ack，消息发出后立即被认为已处理（可能丢消息，性能最高）
		// AckAllPolicy ：一次 ack 会自动确认该消费者在当前会话中之前的所有未确认消息
		// AckExplicitPolicy ：每条消息都必须明确调用 msg.Ack() 来确认，一般用这个
		AckPolicy:     config.AckPolicy,
		DeliverPolicy: jetstream.DeliverNewPolicy,
	}

	// 消费者名字：为空则自动生成
	if config.Name != "" {
		consumerConfig.Name = config.Name
	}

	// durable 不为空的时候，支持离线消息
	// 每个服务实例使用不同的 durable name，这样每个实例都会收到所有消息，用于广播消息
	// 多个服务实例使用相同的 durable name，这样消息会在实例间分发，用于队列消费（负载均衡）
	if config.Durable != "" {
		consumerConfig.Durable = config.Durable
	}
	if config.FilterSubject != "" {
		consumerConfig.FilterSubject = config.FilterSubject
	}
	if config.AckWait > 0 {
		consumerConfig.AckWait = config.AckWait
	}
	if config.MaxDeliver > 0 {
		consumerConfig.MaxDeliver = config.MaxDeliver
	}
	// ReplayPolicy 默认值是 ReplayInstantPolicy (0)，只有当设置了非默认值时才应用
	if config.ReplayPolicy != jetstream.ReplayInstantPolicy {
		consumerConfig.ReplayPolicy = config.ReplayPolicy
	}

	consumer, err := c.js.CreateOrUpdateConsumer(ctx, streamName, consumerConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create consumer for stream %s: %w", streamName, err)
	}

	return consumer, nil
}

// ConsumeMessages 消费消息（使用回调函数）
func (c *NatsClient) ConsumeMessages(ctx context.Context, consumer jetstream.Consumer, handler MessageHandler, opts ...jetstream.PullConsumeOpt) (jetstream.ConsumeContext, error) {
	consumeCtx, err := consumer.Consume(func(msg jetstream.Msg) {
		if err := handler(msg); err != nil {
			// 处理错误，可以选择 Nak 或者记录日志
			msg.Nak()
		} else {
			msg.Ack()
		}
	}, opts...)

	if err != nil {
		return nil, fmt.Errorf("failed to start consuming messages: %w", err)
	}

	return consumeCtx, nil
}

// IsConnected 检查连接状态
func (c *NatsClient) IsConnected() bool {
	return c.nc != nil && c.nc.Status() == nats.CONNECTED
}

// 服务之间，通过 nats 实现类似 RPC 同步调用
func (c *NatsClient) Request(subj string, req []byte, timeout time.Duration) ([]byte, error) {
	// 发送请求并等待响应
	msg, err := c.nc.Request(subj, req, timeout)
	if err != nil {
		return nil, err
	}
	return msg.Data, nil
}
