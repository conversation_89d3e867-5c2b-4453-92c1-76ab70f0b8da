


## 接入点鉴权

对于接入点鉴权，这里是有区别的：

- 接入点绑定有 deviceTypeID
- 接入点没有 deviceID，我们使用 accessPointID 作为 deviceID。
- 接入点使用 accessPointCode 作为 secret。

也是通过 GenMqttAuthInfo 生成 mqtt 三元组。


## 详细介绍

接入点是一个个子服务，作为非标准协议和标准 mqtt 协议之间的桥梁。

有以下规范：

- username 有如下格式，以便在 ACL 鉴权的时候，区分这是一个接入服务。而不是一个具体的设备。
    - username 的格式如下： ap.{accessPointID}.{timestamp_ms}.{random_string}
- 连接鉴权。clientID 是由 deviceTypeID 和 accessPointID 拼接
    - 每一个接入点对应一个 deviceTypeID， 和 accessPointID。
    - 一个 deviceTypeID 是可以有多个接入服务。一个 deviceType 对应的多个接入点，有相同的 deviceTypeID，和不同的 accessPointID。
- ACL 鉴权。接入服务仅对比 deviceTypeID 是否匹配。
    - 可以扩展解析出 accessPointID 和 具体 deviceID的关系。

在接入点详情信息 —— 连接信息中，也显示接入点的 accessPointID 和 accessPointCode。


## 代理消息收发

虽然整个接入服务是一个 mqtt 连接，但是也代表了很多设备的消息的发布和订阅。规范如下：

- 此 mqtt 订阅和发布的 topic 规范，和正常具体一个个设备一样，都要包含 deviceTypeID 和 deviceID，方便统一管理。
- 当接入点离线，则接入点下的设备都离线。


## 具体签名方法


主要是函数 GenMqttAuthInfoForAccessPoint 

参数：

- deviceTypeID ：设备类型 ID
- accessPointID： 接入点 ID
- accessPointCode： 接入点 Code
- salt: 常数字符串，默认是 "some-salt-string"

签名内容：

- clientID = {deviceTypeID}.{accessPointID}
- username = ap.{accessPointID}.{timestamp_ms}.{random_string}
- content  = {clientID}.{userName}.{salt}

算法：默认是 hmacsha256，签名结果是 password

鉴权回调函数：

- 在 OnConnectAuth 中，会重新签名过程，检查 password 是否匹配。如果不匹配则拒绝连接。
- 在 OnACLCheck 中，从 clientID 中解析  {deviceTypeID}，再对比 topic 中的  {deviceTypeID}。一致则通过。

## 接入点的功能性

- 发送消息：按照我们规范的 topic 来上报
- 订阅消息：订阅此 deviceTypeID 的消息，使用通配符：  thing/down/{ attri | event | action }/{deviceTypeID}/ {deviceID}

新的问题：

如果一个 deviceType 对应了多个接入点，应该如何处理？ 

- 方案 1：下发消息所有接入点都会收到，各个接入点自己确认下，是否是自己的设备。
- 方案 2：曲线救国。为另外一批设备分配不一样的 deviceTypeID。

另外，只做一下协议转换的话，单个接入服务承载至少上万个设备，目前还没有遇到这样的场景。实在遇到了，用一个新的 deviceType 就好了。


## 接入点的安全性

- 在 mqtt connect 的时候，通过签名的方式，能确保接入点是安全的。
- 在 mqtt acl 的时候
    - 发布的 topic 里面有 deviceTypeID 和 deviceID，但是 clientID 里面只有 deviceTypeID 和 accessPointID，无法判断是否合法的 deviceID（能做有限的检查， 比如 deviceID 是否和 deviceTypeID 以及 accessPointID 正确关联 ）。设备是否正确的发布消息，取决于 accessPoint 内部的实现。
    - 订阅的 时候，使用通配符，类似 thing/down/+/+/{deviceTypeID}/+，也是只能检查 deviceTypeID 是否与 client 的匹配。订阅的下行消息分发到具体的设备，取决于 accessPoint 内部的实现。


综上，在 gw 和 accessPoint 互相配合下，没有安全问题。如果其他开发者做的 accessPoint 没有合理实现，也不会影响到平台内其他设备。