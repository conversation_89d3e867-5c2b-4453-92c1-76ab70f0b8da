
## 运维协议

- 是否需要创造 product 的概念？
- 是否需要维护一个用于运维协议的 设备ID？当前标准协议的 deviceID 是业务标识，是用户自定义、可修改的。

## 固件升级

固件升级的通讯中，mqtt 协议仅负责获取可用固件信息。固件信息里面有文件下载地址和文件 ID。
- 设备可以选择使用 http 协议下载固件，使用文件下载地址。
- 设备可以选择使用 mqtt 文件下载协议下载固件，使用文件 ID。

- 升级方式：
    - 自动升级：设备请求新的可用固件，服务器返回固件信息，携带了文件 ID、文件名、文件大小、校验和等。设备自行下载。
    - 手动升级：服务器下发固件信息，设备请求固件，携带了文件 ID、文件名、文件大小、校验和等。设备自行下载。
- 设备下载文件
- 设备完成升级


## 设备配置

配置参数的结构体参考 developLink，用 压缩的 json 格式。

- 考虑到 兼容性、扩展性，传输性能

```json
{
    "version":1, //协议版本
    // 设备参数
    "storage":[], // 存储参数
    "network": [], // 网络参数，以太网、4G、5G、Wi-Fi 等，可以多组配置
    "mqtt": [], // mqtt 参数
    "log":[], // 日志参数

    "sensors":[], // 非标传感器配置

    // modbus 读取参数（采集）
    "modbus_read":[
        {
            "modbus": [], // modbus 读取配置：从机地址、功能码、寄存器地址、寄存器数量、采集结果解析方式
            "data": [], // 数据转换配置，公式 ID，公式参数
            "protocol": [], // 协议转换， mqtt+json
        },
        {
            "modbus": [], // modbus 读取配置：从机地址、功能码、寄存器地址、寄存器数量、采集结果解析方式
            "data": [], // 数据转换配置，公式 ID，公式参数
            "protocol": [], // 协议转换， mqtt+json
        },
    ],
    // modbus 写入参数（控制）
    "modbus_write": [
        {
            "modbus": [], // modbus 写入配置：从机地址、功能码、寄存器地址、寄存器数量、采集结果解析方式
            "data": [], // 数据转换配置，公式 ID，公式参数
            "protocol": [], // 协议转换， mqtt+json
        }
    ]
}
```

