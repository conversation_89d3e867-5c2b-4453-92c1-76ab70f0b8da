package bean

type DataType string

/*
// ====================  数据类型 ==================
export const DataTypeList = [

	{ id:1, label: "Number(数值)", value: "number"},
	{ id:2, label: "Switch(开关)", value: "switch"},
	{ id:3, label: "Text(文本)", value: "text"},
	{ id:4, label: "Enum(枚举)",  value: "enum"},
	{ id:5, label: "Object(键值对)",  value: "object"},
	{ id:6, label: "List(数组)",  value: "list"},

]
*/
const (
	DataTypeNumber DataType = "number"
	DataTypeSwitch DataType = "switch"
	DataTypeText   DataType = "text"
	DataTypeEnum   DataType = "enum"
	DataTypeObject DataType = "object"
	DataTypeList   DataType = "list"
)
