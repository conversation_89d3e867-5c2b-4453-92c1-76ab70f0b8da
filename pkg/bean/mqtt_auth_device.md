

## MQTT 设备鉴权

通过 hook 的方式实现鉴权。包括连接、订阅、发布。

- 当选择一型一密的时候，添加一个设备类型，就会生成一个鉴权信息： deviceTypeCode，作为签名用 secret
- 当选择一机一密的时候，添加一个设备，就会生成一个鉴权信息：deviceCode，作为签名用 secret

设备可以基于 deviceTypeID、deviceID、secret，使用签名算法 GenMqttAuthInfo 生成 mqtt 三元组。

在设备详情 —— 连接信息中，显示 deviceTypeID、deviceID、secret，并且为了方便调试，也生成 mqtt 三元组。

### 一型一密

添加设备类型的时候，必须生成 deviceTypeCode。

连接鉴权的时候，签名所用的的 key 使用 deviceTypeID 能提取到设备的 deviceTypeCode。 


### 一机一密

添加设备的时候，必须生成设备对应的 deviceCode。
连接鉴权的时候，签名所用的的 key 使用 deviceID 能提取到设备的 deviceCode 


### 鉴权流程

原理

- 在连接的时候，把 deviceTypeID 和 deviceID 编码到 clientID 和 topic 中，通过签名算法得到 password。与 password 对比
- 在订阅和发布的时候，解析 clientID 和 topic，如果匹配 deviceTypeID 和 deviceID，则认为是合法的。

mqtt 三元组

- clientId ：组合生成， {deviceTypeID}.{deviceID} 的方式，固定不变
- username ：设备的 username 格式，dev.{timestamp_ms}.{random_string}，可以扩展编码进去更多信息，比如 brokerID、签名算法等
- password ：平台通过签名算法生成。在 connect 的时候，会重新校验签名结果。

注意，clientId 必须保持固定且唯一。如果同一个 broker，多个设备使用同样的 clientID 连接平台，平台将这些连接视为同一个设备，这会导致后一个设备连接成功后会顶掉之前的设备连接。

## 三元组生成流程

主要是函数 GenMqttAuthInfo 

参数：

- deviceTypeID ：设备类型 key 
- deviceID： 设备唯一 ID
- secret：如上所述，一型一密使用 deviceTypeCode，一机一密使用 deviceCode。
- salt: 常数字符串，默认是 "some-salt-string"

签名内容：

- clientID = {deviceTypeID}.{deviceID}
- username = dev.{timestamp_ms}.{random_string}
- content  = {clientID}.{userName}.{salt}

算法：默认是 hmacsha256，key 是 productSecret（一型一密），或者 deviceSecret（一机一密），签名结果是 password

鉴权回调函数：

- 在 OnConnectAuth 中，会重新签名过程，检查 password 是否匹配。如果不匹配则拒绝连接。
- 在 OnACLCheck 中，从 clientID 中解析  {deviceTypeID}.{deviceID}，再对比 topic 中的  {deviceTypeID}.{deviceID}，如果不匹配，则拒绝操作。


### 其他

- 有些平台在暴露 deviceTypeCode 或者 deviceCode 的时候，使用了 base64 编码。
- 有的签名可以选择不同的算法，比如  HMAC-SHA1、HMAC-SHA256、HMAC-MD5 等。
- 有的在签名信息里面携带了时间戳，可检查设备的试用时间过期，也可以防止重入。

我们暂时使用最简化的设计，后面根据需要再扩展即可。扩展的规则是：保持 clientID 唯一且固定不变，且可以解析出  {deviceTypeID}.{deviceID}，其他的信息都可以在 username 里面扩展，双方约定即可。


## 接入方式

- 预先配置：平台生成三要素，设备接入服务器的时候，手动配置，可用于一机一密，以及一型一密。
    - 第三方设备直连我们平台的时候，使用此方案。
- 自动注册： 无需提前注册设备，新设备即插即用。设备自动生成三要素。仅用于一型一密模式。
    - 我们自己的设备，使用算法+密码的方式，比直接公开固定不可变的三要素更安全。


## hardware 鉴权

hareware 是对应 product 的，负责硬件的运维功能，如升级、配置、调试 等。

鉴权流程 和 device 一样。区别在于:

- clientID 和 topic 中，使用 productID 代替 deviceTypeID，使用 hardwareID 代替 deviceID。
- username 使用 hardware. 作为前缀