package bean

// dtu 设备的配置消息
type ConfigMessageDtu struct {
	Version     int   `json:"version"`
	Stoage      []any `json:"storage"`
	Network     []any `json:"network"`
	Mqtt        []any `json:"mqtt"`
	Log         []any `json:"log"`
	Sensor      []any `json:"sensor"`
	ModbusRead  []any `json:"modbus_read"`
	ModbusWrite []any `json:"modbus_write"`
}

// TODO：按需定义其他配置参数结构图
type (
	StorageConfig struct {
	}
	NetworkConfig4G struct {
	}
	NetworkConfigEth struct {
	}
	NetworkConfig5G struct {
	}
	NetworkConfigWifi struct {
	}
	MqttConfig struct {
	}
	LogConfig struct {
	}
	SensorConfig struct {
	}
	ModbusReadConfig struct {
	}
	ModbusWriteConfig struct {
	}

	ModbusDataParse struct {
	}

	JsonProtocol struct {
	}
)

// 将配置参数转换成精简的 json 格式
func ToFlatJson(config *ConfigMessageDtu) string {
	return ""
}

// 从精简 json 转换到 struct，方便前端使用
func FromFlatJson(json string) (*ConfigMessageDtu, error) {
	return nil, nil
}
