package bean

import "fmt"

// 文件信息
// 新建文件的参数
// 获取文件信息的返回值
type FileInfo struct {
	FileID string         `json:"file_id"`
	Name   string         `json:"name"`
	Path   string         `json:"path"`
	Size   int64          `json:"size"`
	Md5    string         `json:"md5"`
	Crc16  uint16         `json:"crc16"`
	Meta   map[string]any `json:"meta"`
}

// 构建 TOPIC 的规则，所有 topic 都可以通过前缀来区分和解析

// 创建文件
// 上行
// - topic: file/init/{file_id}
func GetTopicFileInit(fileID string) string {
	return "file/init/" + fileID
}

// 下行
func GetTopicFileInitReply(fileID string) string {
	return "file/init_reply/" + fileID
}

// 上传文件
// 上行
// -  file/upload/{file_id}/{offset}[/{checksum}]
func GetTopicFileUpload(fileID string, offset, checksum int64) string {
	return fmt.Sprintf("file/upload/%s/%d/%d", fileID, offset, checksum)
}

// - 响应 file/upload_reply/{file_id}/{offset}
func GetTopicFileUploadReply(fileID string, offset int64) string {
	return fmt.Sprintf("file/upload_reply/%s/%d", fileID, offset)
}

// 获取文件信息
// 上行
// - topic: file/info/{file_id}
func GetTopicFileInfo(fileID string) string {
	return "file/info/" + fileID
}

// 下行
func GetTopicFileInfoReply(fileID string) string {
	return "file/info_reply/" + fileID
}

// 下载文件
// 上行
// - 请求 file/download/{file_id}/{offset}/{length}/{token}
func GetTopicFileDownload(fileID string, offset, length int64, token string) string {
	return fmt.Sprintf("file/download/%s/%d/%d/%s", fileID, offset, length, token)
}

// 下行
// - 响应 file/download_reply/{file_id}/{offset}[/{checksum}]
func GetTopicFileDownloadReply(fileID string, offset, checksum int64) string {
	return fmt.Sprintf("file/download_reply/%s/%d/%d", fileID, offset, checksum)
}
