
## 介绍

MQTT标准协议，是物联网平台直接支持的协议，基于 mqtt + json。这个协议会规范一些 topic 和数据结构的定义。

用户可结合此协议和物模型，直接接入到平台。
也可以通过接入服务，实现私有协议和标准协议之间的转换。

## topic规范

topic 使用 / 分割 5 个字段：

{head}/{direction}{type}/{deviceTypeID}/{deviceID} 

其中：
- head：暂时仅支持 thing
- type：表示数据类型，暂时仅支持 attri（属性）、event（事件）、action（方法）
- deviceTypeID：设备类型 ID
- deviceID：设备 ID

## topic 的定义

参考了 ithings 的协议

属性上行   thing/up/attri/{deviceTypeID}/{deviceID}
属性下行   thing/down/attri/{deviceTypeID}/{deviceID}

事件上行   thing/up/event/post/{deviceTypeID}/{deviceID}
事件下行   thing/down/event/post_reply/{deviceTypeID}/{deviceID}

方法下行   thing/down/action/call/{deviceTypeID}/{deviceID}
方法上行   thing/up/action/call_reply/{deviceTypeID}/{deviceID}

## 数据结构

```go
// SN 由发起者来生成，请求和响应必须匹配
// 实际上主要用于服务器对设备进行同步的 action call。
type (
	MqttMessage struct {
		Version string         `json:"version"`
		Methond string         `json:"method"`
		SN      string         `json:"sn"`
		TS      int64          `json:"ts"`
		Data    map[string]any `json:"data"`
	}

	MqttMessageReply struct {
		Version string         `json:"version"`
		Methond string         `json:"method"`
		SN      string         `json:"sn"`
		TS      int64          `json:"ts"`
		Code    int            `json:"code"`
		Msg     string         `json:"msg"`
		Data    map[string]any `json:"data"`
	}
)

```

### 属性上报

设备 → 云

thing/up/attri/{deviceTypeID}/{deviceID} 

```json
{
    "method":"report",
    "sn":"123",  //消息唯一标识
    "ts":1677762028638, //时间戳，单位是 ms

    "data":{
        "power_switch":1,
        "color":1,
        "brightness":32
    }
}
```

### 属性上报响应

云 → 设备

thing/down/attri/{deviceTypeID}/{deviceID} 

```json
{
    "method":"reportReply",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "code":0,
    "msg":"", // 如 code 不为 0，则 msg 是错误提示消息
}
```

### 属性读取

云 → 设备

thing/down/attri/{deviceTypeID}/{deviceID} 

```json
{
    "method":"getReport",
    "sn":"123",  //消息唯一标识
    "ts":1677762028638, //时间戳，单位是 ms
    "data":{
        "attris":["power_switch","color","brightness"],
    }
}
```


### 属性读取响应

设备 → 云

thing/up/attri/{deviceTypeID}/{deviceID} 

```json
{
    "method":"getReportReply",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "code":0,
    "msg":"", // 如 code 不为 0，则 msg 是错误提示消息
    "data":{
        "power_switch":1,
        "color":1,
        "brightness":32
    },
}
```


### 属性设置

云 → 设备

thing/down/attri/{deviceTypeID}/{deviceID} 

```json
{
    "method":"set",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "data":{
        "color":1,
        "brightness":32
    },
}
```

### 属性设置响应

设备 → 云

thing/up/attri/{deviceTypeID}/{deviceID} 

```json
{
    "method":"setReply",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "code":0,
    "msg":"", // 如 code 不为 0，则 msg 是错误提示消息
}
```



### 事件上报

设备 → 云

thing/up/event/{deviceTypeID}/{deviceID} 

```json
{
    "method":"eventPost",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "event": "PowerAlarm", 
    "data":{
        "color":1,
        "brightness":32
    },
}
```

### 事件上报响应

云 → 设备

thing/down/event/{deviceTypeID}/{deviceID} 

```json
{
    "method":"eventReply",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "code":0,
    "msg":"", // 如 code 不为 0，则 msg 是错误提示消息
}
```


### 方法调用

云 → 设备

thing/down/action/{deviceTypeID}/{deviceID} 
```json
{
    "method":"action",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "action": "PowerSwitch", 
    "data":{
        "switch":1,
        "color":2,
        "brightness":32
    },
}
```

### 方法调用响应

设备 → 云

thing/up/action/{deviceTypeID}/{deviceID} 


```json
{
    "method":"actionReply",
    "sn":"123",
    "ts":1677762028638, //时间戳，单位是 ms

    "code":0,
    "msg":"", // 如 code 不为 0，则 msg 是错误提示消息
    "data":{

    }
}
```


- 设备端在发布（publish）消息时，QoS 请使用 0，不要使用 1 或 2。ThingsCloud 不支持 QoS，以免通信模组重复发送。


## 设备其他

通过 maintain 这个 topic 来管理。

- 设备扩展信息
- 设备状态
- 其他