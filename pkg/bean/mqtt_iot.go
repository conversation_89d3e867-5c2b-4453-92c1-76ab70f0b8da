package bean

import "fmt"

// 运维上行   thing/up/iot/{deviceTypeID}/{deviceID}
// 运维下行   thing/down/iot/{deviceTypeID}/{deviceID}

func GetTopicIotUp(deviceTypeID, deviceID string) string {
	iotUpTopic := "thing/up/iot/%s/%s"
	return fmt.Sprintf(iotUpTopic, deviceTypeID, deviceID)
}

func GetTopicIotDown(deviceTypeID, deviceID string) string {
	iotDownTopic := "thing/down/iot/%s/%s"
	return fmt.Sprintf(iotDownTopic, deviceTypeID, deviceID)
}

// iot相关功能
const (
	// 注册信息：注册需携带设备扩展信息，设备扩展信息在 deviceType 里面定义
	Register      string = "register"      //表示设备注册
	RegisterReply string = "registerReply" //表示设备注册的响应

	// 故障信息
	Fault      string = "fault"      //故障信息
	FaultReply string = "faultReply" //故障信息的响应

	// 设备日志
	Log      string = "log"      //设备日志
	LogReply string = "logReply" //设备日志的响应
)

// 固件升级
const (
	otaPull      string = "otaPull"      //设备获取最新固件信息，上行
	otaPullReply string = "otaPullReply" //设备获取最新固件信息的响应

	otaPush      string = "otaPush"      //服务器推送设备固件信息，下行
	otaPushReply string = "otaPushReply" //服务器推送设备固件信息的响应
)

// 参数配置
const (
	ConfigSet      string = "configSet"      //设置参数
	ConfigSetReply string = "configSetReply" //设置参数的响应

	ConfigGet      string = "configGet"      //获取参数
	ConfigGetReply string = "configGetReply" //获取参数的响应
)

// =============================================================== ota

// 远程升级:仅传输升级信息，不传输文件

// 设备请求新固件信息的响应信息
// 服务器主动下发给设备的新可用固件信息
type OtaInfo struct {
	Token     string `json:"token"`      //token，用于设备下载文件的鉴权
	FileID    string `json:"file_id"`    //文件 ID
	Name      string `json:"name"`       //固件名称
	Path      string `json:"path"`       //固件下载地址
	Size      int64  `json:"size"`       //固件大小
	Checksum  string `json:"checksum"`   //固件校验和
	Group     string `json:"group"`      //固件分组
	Version   string `json:"version"`    //固件版本
	ProductID string `json:"product_id"` //产品 ID
	Md5       string `json:"md5"`        //文件的 md5
}

// For TLV 编码的 OTA
type RespOtaSlice struct {
	FileID string `json:"file_id,omitempty"     mapstructure:"file_id"`
	Offset uint32 `json:"offset"                mapstructure:"offset"`
	Length uint16 `json:"length"                mapstructure:"length"`
	Buffer []byte `json:"slice"                 mapstructure:"slice"`
}

// NC1800 使用扩展651协议，下载固件分片的时候，使用的是版本参数。
// 其他MQTT协议，需支持差分升级，所以要用固件的uuid。
type ReqOtaSlice struct {
	FileID string `json:"file_id,omitempty"      mapstructure:"file_id"` //only for mqtt正式协议。因为要支持差分升级，不可以用简单的版本号。
	Offset uint32 `json:"offset,omitempty"       mapstructure:"offset"`  //必选参数
	Length uint16 `json:"length,omitempty"       mapstructure:"length"`  //必选参数:分片大小
}
