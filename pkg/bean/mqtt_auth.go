package bean

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"bs.com/app/pkg/xutils"
)

// MessageType
const (
	MqttTypeAttri  = "attri"
	MqttTypeEvent  = "event"
	MqttTypeAction = "action"
	MqttTypeIot    = "iot" // 扩展了设备运维功能，注册设备、参数配置、设备状态、固件升级等
)

type (

	// 从 topic 解析出来的信息
	TopicInfo struct {
		Head         string // thing：设备消息
		Direction    string // up | down
		Type         string // attri | event | action | maintain
		DeviceTypeID string // 当 type == iot 的时候，对应  ProductID
		DeviceID     string // 当 type == iot 的时候，对应 HardwareID
		Topic        string
	}

	// 鉴权信息 用于 mqtt connect auth
	AuthAclInfo struct {
		Topic    string // 主题
		ClientID string // clientID
	}

	// clientID 的 拼接规则   {DeviceTypeID}.{DeviceID}.{BrokerID}
	ClientIDInfo struct {
		DeviceTypeID string //产品id
		DeviceID     string //设备名称
	}

	AuthConnectInfo struct {
		ClientID string //clientID
		UserName string //用户名
		Password string //密码
		Secret   string //密钥
	}
)

// 参考文档中，topic 的定义方法。{head}/{direction}/{type}/{deviceTypeID}/{deviceID}
func GetTopicInfo(topic string) (*TopicInfo, error) {
	arr := strings.Split(topic, "/")
	if len(arr) != 5 {
		return nil, errors.New("invalid topic")
	}
	ti := &TopicInfo{
		Head:         arr[0],
		Direction:    arr[1],
		Type:         arr[2],
		DeviceTypeID: arr[3],
		DeviceID:     arr[4],
		Topic:        topic,
	}

	return ti, nil
}

// 从 ClientID 中解析出信息： {deviceTypeID}.{deviceID} 的方式
func GetClientIDInfo(ClientID string) (*ClientIDInfo, error) {
	arr := strings.Split(ClientID, ".")

	if len(arr) != 2 {
		return nil, errors.New("invalid clientID")
	}
	info := &ClientIDInfo{
		DeviceTypeID: arr[0],
		DeviceID:     arr[1], //对于接入点来说，arr[1] 是 accessPointID
	}
	return info, nil
}

// 根据 clientID, userName, secret 生成 password，和设备测的算法一致
// 对于 clientID, userName 的拼接方式，请参考文档 《mqtt 标准协议.md》
// 对于一型一密，secret 是 deviceTypeCode
// 对于一机一密，secret 是 deviceCode
// mode 取值是 sha1 sha256 md5

const (
	mode = "sha256"
	salt = "some-salt-string"
)

// 这个算法需要和硬件开发者约定好
func genPassword(clientID, userName, secret string) string {

	password := ""
	content := fmt.Sprintf("%s.%s.%s", clientID, userName, salt)

	switch mode {
	case "sha1":
		password = xutils.ComputeHmacSha1(content, secret)

	case "md5":
		password = xutils.ComputeHmacMd5(content, secret)
	case "sha256":
		password = xutils.ComputeHmacSha256(content, secret)
	}
	return password
}

// 参考文档 《mqtt 设备鉴权.md》
func GenMqttAuthInfo(deviceTypeID, deviceID, secret string) (clientID, username, password string) {
	// - username = dev.{timestamp_ms}.{random_string}
	timestampMs := time.Now().UnixMilli()
	username = fmt.Sprintf("dev.%d.%s", timestampMs, xutils.RandString(10))

	clientID = fmt.Sprintf("%s.%s", deviceTypeID, deviceID)
	password = genPassword(clientID, username, secret)

	return
}

// 参考文档 《mqtt 接入点鉴权.md》
func GenMqttAuthInfoForAccessPoint(deviceTypeID, deviceTypeCode, accessPointID, accessPointCode string) (clientID, username, password string) {
	// - username = ap.{accessPointID}.{timestamp_ms}.{random_string}
	timestampMs := time.Now().UnixMilli()
	username = fmt.Sprintf("ap.%s.%d.%s", accessPointID, timestampMs, xutils.RandString(10))

	_ = deviceTypeCode //未使用

	clientID = fmt.Sprintf("%s.%s", deviceTypeID, accessPointID)
	password = genPassword(clientID, username, accessPointCode)

	return
}

func ValidateMqttAuthInfo(info *AuthConnectInfo) bool {
	// 验证clientID格式
	_, err := GetClientIDInfo(info.ClientID)
	if err != nil {
		return false
	}

	// 验证 password
	expectedPassword := genPassword(info.ClientID, info.UserName, info.Secret)

	return expectedPassword == info.Password
}

// thing 相关 topic
// 获取某设备，所有可用于订阅的 topic 和 可以用于发布的 topic，以及支持的 method
func GetThingTopicInfo(deviceTypeID, deviceID string) []*DeviceTopicInfo {
	arr := []*DeviceTopicInfo{}
	for _, one := range thingTopicInfos {
		one.Topic = one.GetTopic(deviceTypeID, deviceID)
		arr = append(arr, &one)
	}
	return arr
}

type FuncGetTopic func(deviceTypeID, deviceID string) string

type DeviceTopicInfo struct {
	Direction string            `json:"direction"` // up | down
	Desc      string            `json:"desc"`      // 描述
	GetTopic  FuncGetTopic      `json:"-"`         // 用于生成 topic
	Topic     string            `json:"topic"`     // topic 模板
	Method    map[string]string `json:"method"`    // 支持的方法
}

var thingTopicInfos = []DeviceTopicInfo{
	{
		Direction: "UP",
		Desc:      "属性上行",
		GetTopic:  GetTopicAttriUp,
		Method: map[string]string{
			AttriReport:   "属性上报",
			AttriSetReply: "属性设置的响应",
			AttriGetReply: "属性获取的响应",
		},
	},
	{
		Direction: "DOWN",
		Desc:      "属性下行",
		GetTopic:  GetTopicAttriDown,
		Method: map[string]string{
			AttriReportReply: "属性上报的响应",
			AttriSet:         "属性设置",
			AttriGet:         "属性获取",
		},
	},
	// event 和 action 的 method，都是用户在物模型里面定义的
	{
		Direction: "UP",
		Desc:      "事件上行",
		GetTopic:  GetTopicEventUp,
	},
	{
		Direction: "DOWN",
		Desc:      "事件下行",
		GetTopic:  GetTopicEventDown,
	},
	{
		Direction: "UP",
		Desc:      "方法上行",
		GetTopic:  GetTopicActionUp,
	},
	{
		Direction: "DOWN",
		Desc:      "方法下行",
		GetTopic:  GetTopicActionDown,
	},
}

// 运维功能：注册设备、参数配置、设备状态、固件升级等，仅部分设备支持
var iotTopicInfos = []DeviceTopicInfo{
	// 设备注册
	{
		Direction: "UP",
		Desc:      "运维上行",
		GetTopic:  GetTopicIotUp,
		Method: map[string]string{
			Register: "设备注册",
			// 可扩展
		},
	},
	{
		Direction: "DOWN",
		Desc:      "运维下行",
		GetTopic:  GetTopicIotDown,
		Method: map[string]string{
			RegisterReply: "设备注册的响应",
			// 可扩展
		},
	},
}
