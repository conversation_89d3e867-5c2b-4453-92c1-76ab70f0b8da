

## 文件传输

mqtt 文件传输协议，参考了 EMQX 的文件传输协议 https://docs.emqx.com/zh/emqx/latest/file-transfer/client.html

特点就是：使用消息体传输文件，使用 topic 传输参数，不需要额外定义数据包协议。

支持断点续传：服务端维护上传状态，客户端维护下载状态。

### 1、新建文件

上传文件之前先新建文件。

- 请求: file/upload/init/{file_id}

message:

```json
{
  "name": "{name}", // 文件名
  "path": "{path}", // 文件路径,http 协议下载文件
  "size": {size}, // 文件大小
  "checksum": "{checksum}", // 校验和
  "meta": {meta} // 其他数据，对于固件升级，这里有固件版本、固件分组、固件产品 ID、固件签名信息等
}
```
- 响应: file/upload/init_reply/{file_id}

message:

```json
{
    "code": 0,
    "msg":"success"
}
```

### 2、上传文件分片

上传文件的时候，服务器端需要保存状态，负责文件分片的校验、重组。

- timeout: 15 秒。新的分片到来，会清零这个时间。超过这个时间，服务器端清理掉所有的未完成传输的 slice。
- 客户端没有收到正确的响应，则需要重传。

topic: 

- 请求 file/upload/{file_id}/{offset}[/{checksum}]
- message: 文件分片，二进制数据，一般不超过 TCP 的 MTU，默认是 1024

- 响应 file/upload_reply/{file_id}/{offset}

message:

```json
{
    "code": 0,
    "message":"success"
}
```

### 3、获取文件信息

下载之前也可以先获取文件信息

- 请求: file/download/info/{file_id}
- message: 无

- 响应: file/download/info_reply/{file_id}

message:

```json
{
  "name": "{name}", // 文件名
  "size": {size}, // 文件大小
  "checksum": "{checksum}", // 校验和
  "meta": {meta} // 其他数据
}
```

### 4、下载文件

- 客户端自己保存状态、对文件进行校验、重组、重传请求。
- timeout: 5 秒。如果超时则重新请求。

- 请求 file/download/{file_id}/{offset}/{length}/{token}
- message: 无

- 响应 file/download_reply/{file_id}/{offset}[/{checksum}]

- message: 文件分片，二进制数据


## 固件升级时鉴权操作

文件操作相关 topic 是鉴权白名单，所有设备都可以使用。

固件下载过程中，我们设计这样的一个鉴权方案：

- 当下发固件信息时，服务器生成一个 token， token 很简单，就是文件 ID 作为 content，用一个字符串 secret，进行 md5 签名，得到 token。
- 下载文件分片时，设备需要携带这个 token，服务器端检查 token 是否正确。正确则允许下载。
