package bean

type TsData struct {
	Timestamp int64 `json:"timestamp"`
	Value     any   `json:"value"`
}

// FieldStats 字段统计特征值
type FieldStats struct {
	Max     float64 `json:"max,omitempty"`      // 最大值
	MaxTime int64   `json:"max_time,omitempty"` // 最大值的时间

	Min     float64 `json:"min,omitempty"`      // 最小值
	MinTime int64   `json:"min_time,omitempty"` // 最小值的时间

	Mean     float64 `json:"mean,omitempty"`     // 平均值
	Variance float64 `json:"variance,omitempty"` // 方差
	Stddev   float64 `json:"stddev,omitempty"`   // 标准差
	Rms      float64 `json:"rms,omitempty"`      // 均方根
	Count    int64   `json:"count,omitempty"`    // 有效数据点数量
}
