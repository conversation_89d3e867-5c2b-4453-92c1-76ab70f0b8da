package bean

import (
	"fmt"
	"strings"
)

// MQTT 标准协议

// SN 由发起者来生成，请求和响应必须匹配
// 实际上主要用于服务器对设备进行同步的 action call。

const MqttStdV1 = "1"

type (
	// code = 0，表示此消息是请求消息
	// code = 200，表示响应成功
	// code = 其他，表示错误码
	MqttMessage struct {
		Version string         `json:"version"` //协议版本
		Method  string         `json:"method"`  //请求方法
		MsgID   string         `json:"msg_id"`  //消息唯一 ID，必须与请求 SN 一致
		TS      int64          `json:"ts"`      //时间戳，单位 ms
		Data    map[string]any `json:"data"`    //响应数据
		Code    int            `json:"code"`    //状态码，正常：200， 其他值为错误码，按需定义
	}

	// 约定：对于响应消息，code 如果不为 200，那么错误消息可以放在 data 中
)

/*
属性上行   thing/up/attri/{deviceTypeID}/{deviceID}
属性下行   thing/down/attri/{deviceTypeID}/{deviceID}

事件上行   thing/up/event/post/{deviceTypeID}/{deviceID}
事件下行   thing/down/event/post_reply/{deviceTypeID}/{deviceID}

方法下行   thing/down/action/call/{deviceTypeID}/{deviceID}
方法上行   thing/up/action/call_reply/{deviceTypeID}/{deviceID}
*/

// attri
const (
	AttriReport      string = "attri_report"      //表示设备属性上报
	AttriReportReply string = "attri_reportReply" // 表示云端接收设备上报后的响应报文

	AttriSet      string = "attri_set"      //表示云端设置设备属性
	AttriSetReply string = "attri_setReply" // 表示云端设置设备属性的响应报文

	AttriGet      string = "attri_get"      //表示云端请求设备获取属性消息
	AttriGetReply string = "attri_getReply" // 表示设备属性上报
)

// event
const (
// 用户在物模型中定义的 event identifier
)

// action
const (
// 用户在物模型中定义的 command identifier

)

// topic 和 subject 的转换
func TopicToSubject(topic string) string {
	// 将 topic 中的/替换成 .
	return strings.Replace(topic, "/", ".", -1)
}

func TopicFromSubject(subject string) string {
	// 将 subject 中的 . 替换成 /
	return strings.Replace(subject, ".", "/", -1)
}

func GetTopicAttriUp(deviceTypeID, deviceID string) string {
	attriUpTopic := "thing/up/attri/%s/%s"
	return fmt.Sprintf(attriUpTopic, deviceTypeID, deviceID)
}

func GetTopicAttriDown(deviceTypeID, deviceID string) string {
	attrDownTopic := "thing/down/attri/%s/%s"
	return fmt.Sprintf(attrDownTopic, deviceTypeID, deviceID)
}

func GetTopicEventUp(deviceTypeID, deviceID string) string {
	eventUpTopic := "thing/up/event/%s/%s"
	return fmt.Sprintf(eventUpTopic, deviceTypeID, deviceID)
}

func GetTopicEventDown(deviceTypeID, deviceID string) string {
	eventDownTopic := "thing/down/event/%s/%s"
	return fmt.Sprintf(eventDownTopic, deviceTypeID, deviceID)
}

func GetTopicActionUp(deviceTypeID, deviceID string) string {
	actionUpTopic := "thing/up/action/%s/%s"
	return fmt.Sprintf(actionUpTopic, deviceTypeID, deviceID)
}

func GetTopicActionDown(deviceTypeID, deviceID string) string {
	actionDownTopic := "thing/down/action/%s/%s"
	return fmt.Sprintf(actionDownTopic, deviceTypeID, deviceID)
}
