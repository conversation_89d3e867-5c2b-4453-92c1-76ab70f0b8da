package bean

import (
	"bytes"
	"encoding/json"
	"reflect"

	"bs.com/app/pkg/xlog"
)

// 结构体转 map
// 确认val是否结构体，或者是结构体的指针
func IsStruct(val interface{}) bool {
	//结构体
	if reflect.TypeOf(val).Kind() == reflect.Struct {
		return true
	}

	//结构体指针
	if reflect.TypeOf(val).Kind() == reflect.Ptr {
		if reflect.TypeOf(val).Elem().Kind() == reflect.Struct {
			return true
		}
	}
	return false
}

func StructToMap(val interface{}) (m map[string]interface{}) {
	m = make(map[string]interface{})
	if !IsStruct(val) {
		xlog.Error("invalid struct to map")
		return m
	}

	buf, err := json.Marshal(val)
	if err != nil {
		return
	}

	d := json.NewDecoder(bytes.NewReader(buf))
	d.UseNumber()
	_ = d.Decode(&m)

	delete(m, "created_at")
	delete(m, "updated_at")
	//xlog.Info("struct to map : ", string(buf))
	return
}
