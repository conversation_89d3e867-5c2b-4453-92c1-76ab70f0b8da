package eventbus

import (
	"sync"
)

const (
	eventsBuffer = 1000
)

// NewClient creates a new eventbus client
func NewClient() *Client {
	events := make(chan *Event, eventsBuffer)
	client := &Client{
		events:   events,
		registry: sync.Map{},
	}

	go func(client *Client) {
		for event := range client.events {
			handlers, ok := client.registry.Load(event.Topic)
			if !ok {
				continue
			}
			arrHandlers, ok := handlers.([]EventHandler)
			if !ok {
				continue
			}
			for _, handler := range arrHandlers {
				handler(event.Value)
			}
		}
	}(client)

	return client
}

// EventHandler an event handler
type EventHandler func(value interface{})

// Event contains information about an event
type Event struct {
	Topic string
	Value interface{}
}

// Client publishes and subscribes to events
type Client struct {
	events   chan *Event
	registry sync.Map
}

// Publish publishes an event
func (c *Client) Publish(topic string, value interface{}) {
	event := &Event{Topic: topic, Value: value}

	// Attempt to publish an event to the channel, if the channel's buffer was full, discard
	select {
	case c.events <- event:
	default:
	}
}

// Subscribe subscribes to a topic
func (c *Client) Subscribe(topic string, handler EventHandler) {
	var ok2 bool
	var arrHandlers []EventHandler

	handlers, ok1 := c.registry.Load(topic)
	if ok1 {
		arrHandlers, ok2 = handlers.([]EventHandler)
	}

	if ok2 {
		arrHandlers = append(arrHandlers, handler)
	} else {
		arrHandlers = []EventHandler{handler}
	}

	c.registry.Store(topic, arrHandlers)
}

// Close closes eventbus
func (c *Client) Close() {
	close(c.events)
}
