package eventbus

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestEventbus_Fail(t *testing.T) {
	client := NewClient()
	defer client.Close()

	client.Publish("unknown", "Unknown")

	time.Sleep(time.Second)

	client.Subscribe("unknown", func(value interface{}) {
		assert.Fail(t, "Not supposed to be reached")
	})

	time.Sleep(time.Second)
}

func TestEventbus_Success(t *testing.T) {
	client := NewClient()
	defer client.Close()

	client.Subscribe("name", func(value interface{}) {
		assert.Equal(t, value, "Raed Shomali")
	})

	time.Sleep(time.Second)

	client.Publish("name", "<PERSON><PERSON>")

	time.Sleep(time.Second)
}

func TestEventbus_Multi(t *testing.T) {
	client := NewClient()
	defer client.Close()

	client.Subscribe("name", func(value interface{}) {
		fmt.Println("1...value :", value)
		assert.Equal(t, value, "<PERSON><PERSON>")
	})

	client.Subscribe("name", func(value interface{}) {
		fmt.Println("2...value :", value)
		assert.Equal(t, value, "<PERSON><PERSON>")
	})

	time.Sleep(time.Second)

	client.Publish("name", "Raed Shomali")

	time.Sleep(time.Second)
}
