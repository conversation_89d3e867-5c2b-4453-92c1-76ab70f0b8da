package xminio

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"

	"bs.com/app/pkg/now"
	"bs.com/app/pkg/xutils"
)

func TestFPutObject(t *testing.T) {
	ssn := "3489234420"
	fileName := "minio.go"
	url, err := minioUpload(ssn, fileName)
	if err != nil {
		t.Error("minio upload error")
	}
	fmt.Println(url)

}

func minioUpload(ssn, path string) (string, error) {
	conf := Config{
		EndPoint:        "127.0.0.1:9000",
		Domain:          "https://nuc.beithing.com:59000",
		AccessKeyID:     "beithing",
		SecretAccessKey: "Root@1024Minio",
		BucketName:      "ebox-beiting-test",
	}

	dateStr, timeStr := now.TimeUTCtoLocal4(time.Now().Unix())
	fileType := xutils.FileType(path)

	//ssn/yyyy-mm-dd/hh-mm-ss.jpg
	objName := fmt.Sprintf("%s/%s/%s.%s", ssn, dateStr, timeStr, fileType)
	client := NewClient(&conf)
	return client.FPutObject(objName, path)
}

func TestCreateFolder(t *testing.T) {
	conf := Config{
		EndPoint:        "127.0.0.1:9000",
		Domain:          "https://nuc.beithing.com:59000",
		AccessKeyID:     "beithing",
		SecretAccessKey: "Root@1024Minio",
		BucketName:      "ebox-beiting-test",
	}
	client := NewClient(&conf)

	ssn := "3489234420"
	tmpPath := fmt.Sprintf("%s/%d", ssn, time.Now().Unix())
	err := client.CreateFolder(tmpPath)
	if err != nil {
		t.Error(err.Error())
	} else {
		fmt.Println("create path success:", tmpPath)
	}

}

func TestListObjectsTree(t *testing.T) {
	ssn := "3489234420"
	conf := Config{
		EndPoint:        "127.0.0.1:9000",
		Domain:          "https://nuc.beithing.com:59000",
		AccessKeyID:     "beithing",
		SecretAccessKey: "Root@1024Minio",
		BucketName:      "ebox-beiting-test",
	}

	client := NewClient(&conf)
	// result, err := client.ListObjectList("3489234420/1733910442/")
	result, err := client.ListObjectsTree(ssn)
	if err != nil {
		t.Error(err.Error())
	}
	fmt.Println(ssn)
	fmt.Println(result)
	printTree(result, 0)
}

// printTree 打印树结构
func printTree(node *Node, level int) {
	// prefix := strings.Repeat("  ", level)
	// fmt.Printf("%s%s\n", prefix, node.Name)
	// if !node.IsDir {
	// 	fmt.Printf("%s  Download: %s\n", prefix, node.URL)
	// }
	// for _, child := range node.Children {
	// 	printTree(child, level+1)
	// }
	// 将 Node 结构体转换为 JSON
	jsonBytes, err := json.MarshalIndent(node, "", "  ")
	if err != nil {
		fmt.Fprintf(os.Stderr, "JSON marshaling failed: %s\n", err)
		return
	}

	// 输出 JSON 字符串
	fmt.Println(string(jsonBytes))

}
