package xminio

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"bs.com/app/pkg/now"
	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
	minio "github.com/minio/minio-go/v7"
)

/*
 * projectCode: 项目唯一编码code
 * rtype: 类型(日报/月报/...)
 * fileName: 文件名称
 * localFilePath: 本地文件所在路径
 */
func (x XMinio) ProjectReportToMinio(projectCode, rtype, fileName, localFilePath string) (string, error) {
	/*
	 * objectName: 指定上传文件名
	 * filePath: 上传路径(全)
	 */

	timeNowStr := now.TimeToDayStr(time.Now())
	/*
	 *  minio路径: /项目code/类型(日报、周报、月报)/2024-12-1/xxx-日报.xlsx
	 */
	fullPathName := fmt.Sprintf("/%s/%s/%s/%s", projectCode, rtype, timeNowStr, fileName)
	return x.FPutObject(fullPathName, localFilePath)
}

// ============================= 获取minio的文件树列表 ================================
// Node 表示文件系统中的节点
type Node struct {
	ID         string    `json:"id"` // 作为唯一字段
	Name       string    `json:"name"`
	IsDir      bool      `json:"is_dir"`
	Size       int64     `json:"size"`        // 新增字段，用于存储文件的大小
	Type       string    `json:"type"`        // 文件类型 (MIME类型)
	URL        string    `json:"url"`         // 新增字段，用于存储文件的下载链接
	UpdateTime time.Time `json:"update_time"` //
	Children   []*Node   `json:"children"`
}

func getSecondLastSegment(u string) (string, error) {
	parsedURL, err := url.Parse(u)
	if err != nil {
		return "", fmt.Errorf("无法解析URL: %v", err)
	}
	// 获取路径部分
	path := parsedURL.Path
	// 分割路径
	segments := strings.Split(path, "/")
	// 检查是否有足够的段
	if len(segments) < 2 {
		return "", fmt.Errorf("URL路径没有足够的段")
	}
	// 返回倒数第二个段
	return segments[len(segments)-2], nil
}

func getLastName(u string) string {
	parsedURL, err := url.Parse(u)
	if err != nil {
		return u
	}
	// 获取路径部分
	path := parsedURL.Path
	// 分割路径
	segments := strings.Split(path, "/")
	// 检查是否有足够的段
	if len(segments) < 1 {
		return u
	}
	// 返回倒数第二个段
	return segments[len(segments)-1]
}

// 列出某个文件夹的目录，以tree返回
func (x XMinio) ListObjectsTree(prefix string) (*Node, error) {
	// 确保prefix以斜杠结尾，避免意外的行为
	if prefix != "" && !strings.HasSuffix(prefix, "/") {
		prefix += "/"
	}

	name, err := getSecondLastSegment(prefix)
	if err != nil {
		name = prefix
	}

	root := &Node{
		ID:       prefix,
		Name:     name,
		IsDir:    true,
		URL:      "", // 文件夹通常没有直接的下载链接
		Size:     0,
		Type:     "directory",
		Children: nil,
	}

	seen := make(map[string]struct{}) // 用于跟踪已经处理过的对象

	for obj := range x.client.ListObjects(x.ctx, x.bucketName, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: false, // 只列出当前层级的对象
	}) {
		if obj.Err != nil {
			return nil, obj.Err
		}

		relativePath := strings.TrimPrefix(obj.Key, prefix)
		if relativePath == "" {
			continue // 忽略前缀本身
		}

		firstSegment := strings.Split(relativePath, "/")[0]
		childPrefix := fmt.Sprintf("%s%s/", prefix, firstSegment)

		if _, exists := seen[childPrefix]; exists {
			continue // 避免重复处理
		}
		seen[childPrefix] = struct{}{}

		// 检查是否为“文件夹”
		if strings.HasPrefix(obj.Key, childPrefix) && obj.Key[len(childPrefix)-1] == '/' {
			child, err := x.ListObjectsTree(childPrefix)
			if err != nil {
				xlog.Errorf("err: %v", err)
				return nil, err
			}
			root.Children = append(root.Children, child)
		} else {
			// 如果是文件，则直接添加到children，并生成下载链接
			downloadURL := fmt.Sprintf("%s/%s/%s", x.domain, x.bucketName, obj.Key)
			fname := getLastName(obj.Key)
			root.Children = append(root.Children, &Node{
				ID:         obj.Key,
				Name:       fname,
				UpdateTime: obj.LastModified,
				IsDir:      false,
				URL:        downloadURL,
				Size:       obj.Size, // 设置文件大小
				Type:       xutils.FileType(fname),
				Children:   nil,
			})
		}
	}

	// 对于空目录，我们仍然需要返回根节点
	if len(root.Children) == 0 && prefix != "" {
		root.Children = []*Node{}
	}

	return root, nil
}

// 列出某个文件夹的目录，以列表返回
func (x XMinio) ListObjectList(prefix string) ([]*Node, error) {
	var nodes []*Node

	// 如果前缀为空或仅为"/"，则我们从存储桶的根开始列出
	if prefix == "" || prefix == "/" {
		prefix = ""
	} else if !strings.HasSuffix(prefix, "/") {
		// 确保前缀以斜杠结尾，表示是一个文件夹路径
		prefix += "/"
	}

	// 获取顶级目录下的所有对象和子目录
	for obj := range x.client.ListObjects(x.ctx, x.bucketName, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: false, // 不递归列出，只获取当前层级的对象
	}) {
		if obj.Err != nil {
			return nodes, obj.Err
		}

		relativePath := strings.TrimPrefix(obj.Key, prefix)
		firstSegment := strings.Split(relativePath, "/")[0]

		if firstSegment == "" {
			continue // 忽略空路径段
		}

		// 检查是否为“文件夹”
		isDir := strings.HasSuffix(firstSegment, "/")

		// 构建节点名称
		nodeName := firstSegment
		if isDir {
			nodeName = strings.TrimSuffix(nodeName, "/")
		}

		// 避免重复添加相同的文件夹
		exists := false
		for _, node := range nodes {
			if node.Name == nodeName {
				exists = true
				break
			}
		}
		if exists {
			continue
		}

		if isDir {
			// 如果是文件夹，则创建一个新的Node
			nodes = append(nodes, &Node{
				ID:         fmt.Sprintf("%s%s/", prefix, nodeName),
				Name:       nodeName,
				IsDir:      true,
				Size:       0,
				Type:       "directory",
				URL:        "", // 文件夹通常没有直接的下载链接
				UpdateTime: obj.LastModified,
				Children:   nil,
			})
		} else {
			// 如果是文件，则直接添加到nodes，并生成下载链接
			downloadURL := fmt.Sprintf("%s/%s/%s", x.domain, x.bucketName, obj.Key)
			nodes = append(nodes, &Node{
				ID:         obj.Key,
				Name:       nodeName,
				UpdateTime: obj.LastModified,
				IsDir:      false,
				URL:        downloadURL,
				Size:       obj.Size, // 设置文件大小
				Type:       xutils.FileType(nodeName),
				Children:   nil,
			})
		}
	}

	return nodes, nil
}
