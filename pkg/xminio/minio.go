package xminio

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"bs.com/app/config"
	"bs.com/app/pkg/now"
	"bs.com/app/pkg/xlog"
	minio "github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// 设备上传的图像，保存到 minio
func Upload(ssn, path string) (string, error) {
	mConf := config.Get().Minio
	conf := Config{
		EndPoint:        mConf.Endpoint,
		Domain:          mConf.Domain,
		AccessKeyID:     mConf.AccessKey,
		SecretAccessKey: mConf.SecretKey,
		BucketName:      mConf.BucketImage,
	}
	// 获取当前日期作为文件夹
	dateStr, timeStr := now.TimeUTCtoLocal4(time.Now().Unix())
	_ = timeStr

	// 从 path 中提取文件名字（去掉路径部分）
	fileName := filepath.Base(path)

	// 定义在 minio 中的存储路径
	// 可以选择使用原始文件名
	objName := fmt.Sprintf("%s/%s/%s", ssn, dateStr, fileName)
	client := NewClient(&conf)
	return client.FPutObject(objName, path)
}

// =========================================================================================================================
type Config struct {
	EndPoint        string
	Domain          string
	AccessKeyID     string
	SecretAccessKey string
	BucketName      string
	Ctx             context.Context
}

type XMinio struct {
	ctx        context.Context
	client     *minio.Client
	domain     string
	endpoint   string
	bucketName string
}

func NewClient(conf *Config) *XMinio {
	// var x *XMinio
	// client, err := minio.New(conf.EndPoint, conf.AccessKeyID, conf.SecretAccessKey, false)
	client, err := minio.New(conf.EndPoint, &minio.Options{
		Creds:  credentials.NewStaticV4(conf.AccessKeyID, conf.SecretAccessKey, ""),
		Secure: false,
	})
	if err != nil {
		xlog.Error("xminio : new minio client failed", "err", err)
		return nil
	}

	xlog.Debug("minio connect success")
	if conf.Ctx == nil {
		conf.Ctx = context.Background()
	}
	x := XMinio{
		ctx:        conf.Ctx,
		domain:     conf.Domain,
		client:     client,
		endpoint:   conf.EndPoint,
		bucketName: conf.BucketName,
	}
	x.MakeBucket("")
	x.SetPublicPolicy()
	return &x
}

func (x XMinio) MakeBucket(location string) {
	if location == "" {
		location = "shenzhen"
	}
	err := x.client.MakeBucket(x.ctx, x.bucketName, minio.MakeBucketOptions{Region: location})
	if err != nil {
		// 检查存储桶是否已经存在。
		exists, err := x.client.BucketExists(x.ctx, x.bucketName)
		if err == nil && exists {
			xlog.Debugf("bucket %s is exists ", x.bucketName)
		} else {
			xlog.Error("query bucket status error", err)
			return
		}
	}
}

func (x XMinio) SetPublicPolicy() error {
	publicProcyString := `{
		"Version":"2012-10-17",
		"Statement":[
		  {
			"Effect":"Allow",
			"Principal":{
			  "AWS":["*"]
			},
			"Action":[
			  "s3:GetBucketLocation"
			],
			"Resource":[
              "arn:aws:s3:::` + x.bucketName + `"
			]
		  },
		  {
			"Effect":"Allow",
			"Principal":{
			  "AWS":["*"]
			},
			"Action":[
			  "s3:GetObject"
			],
			"Resource":[
			  "arn:aws:s3:::` + x.bucketName + `/*"
			]
		  }
		]
	  }`
	err := x.client.SetBucketPolicy(x.ctx, x.bucketName, publicProcyString)
	return err
}

/*
 * minioPathName: 指定上传文件名
 * localPathName: 上传路径(全)
 */
func (x XMinio) FPutObject(minioPathName, localPathName string) (string, error) {
	// 指定上传文件为 test.txt
	//   minioPathName := "test.txt"
	// 指定上传文件路径
	//   localPathName := "/test/test.txt"
	// 指定上传文件类型
	//   contentType := "application/zip"

	xlog.Debugf("bucket name: %s,object name: %s , full path name: %s", x.bucketName, minioPathName, localPathName)
	// 调用 FPutObject 接口上传文件。
	_, err := x.client.FPutObject(x.ctx, x.bucketName, minioPathName, localPathName, minio.PutObjectOptions{ContentType: ""})
	if err != nil {
		xlog.Error("upload file error", "err", err.Error())
		return "", err
	}
	var url string
	if x.domain != "" {
		url = fmt.Sprintf("%s/%s/%s", x.domain, x.bucketName, minioPathName)
	} else {
		xlog.Warn("xminio domain not config")
		url = fmt.Sprintf("/%s/%s", x.bucketName, minioPathName)
	}

	xlog.Infof("upload file %s success.access url: %s \n", minioPathName, url)
	return url, nil
}

// CreateFolder 在MinIO中创建一个文件夹
func (x XMinio) CreateFolder(folderPath string) error {
	// 确保folderPath以斜杠结尾
	if !strings.HasSuffix(folderPath, "/") {
		folderPath += "/"
	}

	// 创建一个空的对象，其键名以斜杠结尾，模拟文件夹
	n, err := x.client.PutObject(x.ctx, x.bucketName, folderPath, strings.NewReader(""), 0, minio.PutObjectOptions{
		ContentType: "application/octet-stream",
	})

	if err != nil {
		xlog.Errorf("Failed to create folder %s: %v", folderPath, err)
		return err
	}

	// 记录成功创建文件夹的日志
	xlog.Infof("Successfully created folder %s in bucket %s, object size: %d", folderPath, x.bucketName, n.Size)
	return nil
}

/*
 * minio删除文件
 */
func (x XMinio) DeleteFile(objName string) error {
	// 调用RemoveObject方法删除对象
	err := x.client.RemoveObject(x.ctx, x.bucketName, objName, minio.RemoveObjectOptions{})
	if err != nil {
		// 如果删除过程中出现错误，记录错误日志
		xlog.Errorf("Failed to delete object %s: %v", objName, err)
		return err
	}
	// 记录删除成功的日志
	xlog.Infof("Successfully deleted object %s from bucket %s", objName, x.bucketName)
	return nil
}
