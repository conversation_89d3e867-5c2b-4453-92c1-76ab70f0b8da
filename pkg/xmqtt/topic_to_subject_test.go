package xmqtt

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestConvertMQTTTopicToNATSSubject(t *testing.T) {
	tests := []struct {
		name     string
		mqtt     string
		expected string
	}{
		{"simple path", "a/b/c", "a.b.c"},
		{"single wildcard", "devices/+/status", "devices.*.status"},
		{"multi wildcard", "sensors/#", "sensors.>"},
		{"mixed case", "Devices/Room1/Temperature", "Devices.Room1.Temperature"},
		{"special chars", "data/$room/status", "data._room.status"},
		{"empty", "", ""},
		{"root wildcard", "#", ">"},
		{"root single wildcard", "+", "*"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, TopicToSubject(tt.mqtt))
		})
	}
}
