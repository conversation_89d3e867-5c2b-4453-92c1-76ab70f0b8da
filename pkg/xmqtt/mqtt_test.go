package xmqtt

import (
	"fmt"
	"sync"
	"testing"
	"time"

	paho "github.com/eclipse/paho.mqtt.golang"
)

// go语言版本
// 自定义格式设备接入
func TestMQTT(t *testing.T) {

	var (
		host     = "tcp://127.0.0.1:1883"
		clientid = "client001"
		pubTopic = "test"
		subTopic = "test"
	)

	connOpts := paho.NewClientOptions().AddBroker(host).SetClientID(clientid).SetCleanSession(true)

	//TODO：mqtt 鉴权
	//username, password := "", ""
	//if username != "" && password != "" {
	//	connOpts.SetUsername(username)
	//	connOpts.SetPassword(password)
	//}
	// 设备连接平台　设备上线
	client := paho.NewClient(connOpts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		fmt.Println(token.Error())
		return
	}
	var wg sync.WaitGroup
	wg.Add(2)
	//设备每隔3s　向平台发送数据
	go func() {
		defer wg.Done()
		for {
			message := []byte(`{"data":report"}`)
			if token := client.Publish(pubTopic, byte(0), true, message); token.Wait() && token.Error() != nil {
				panic("error")
			}
			fmt.Println("published", pubTopic)
			time.Sleep(3 * time.Second)
		}
	}()
	//设备订阅下行主题　接收应用消息 -> 处理消息 -> 返回数据
	go func() {
		defer wg.Done()
		if token := client.Subscribe(subTopic, byte(0), func(client paho.Client, message paho.Message) {
			// 设备注册的topic /sys/{ModelId}/{EntityId}/user/down/{messageId}}/call
			// 设备发布的topic /sys/{ModelId}/{EntityId}/user/down/{messageId}}/call_reply
			fmt.Println("sub", message.Topic(), message.Payload())
			if token := client.Publish(message.Topic()+"_reply", byte(0), true, []byte(`{"reply":"success"}`)); token.Wait() && token.Error() != nil {
				panic("Publish error")
			}
		}); token.Wait() && token.Error() != nil {
			fmt.Println("fail")
		}
	}()
	wg.Wait()
}

func TestMqtt2(t *testing.T) {
	const (
		host     = "tcp://127.0.0.1:1883"
		clientid = "mqtt_test_client001"
	)
	a := NewMqttClient(host, clientid)
	a.Handle("/test/001", func(topic string, data []byte) error {
		fmt.Println("topic : ", topic)
		fmt.Println("messsage: ", string(data))

		return nil
	})

	_ = a.Start("", "")
	defer a.Stop()
}
