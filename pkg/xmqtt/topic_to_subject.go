package xmqtt

import (
	"strings"
	"unicode"
)

// ConvertMQTTTopicToNATSSubject 将 MQTT 主题转换为 NATS JetStream 主题
// 示例:
//   - "devices/+/status" -> "devices.*.status"
//   - "sensors/room1/#" -> "sensors.room1.>"
//   - "a/b/c" -> "a.b.c"
func TopicToSubject(mqttTopic string) string {
	// 特殊处理空字符串
	if mqttTopic == "" {
		return ""
	}

	// 分割 MQTT 主题
	parts := strings.Split(mqttTopic, "/")

	// 转换每个部分
	for i, part := range parts {
		switch part {
		case "+":
			parts[i] = "*"
		case "#":
			parts[i] = ">"
		default:
			// 处理特殊字符：NATS 主题只允许 字母、数字、下划线、点、>、*
			parts[i] = sanitizeNATSSubjectPart(part)
		}
	}

	// 重新组合
	return strings.Join(parts, ".")
}

// sanitizeNATSSubjectPart 清理 NATS 主题部分中的非法字符
func sanitizeNATSSubjectPart(part string) string {
	var builder strings.Builder

	for _, r := range part {
		// 允许的字符: 字母、数字、下划线、点、>、*
		if unicode.IsLetter(r) || unicode.IsDigit(r) || r == '_' || r == '.' || r == '*' || r == '>' {
			builder.WriteRune(r)
		} else {
			// 替换其他字符为下划线
			builder.WriteRune('_')
		}
	}

	return builder.String()
}
