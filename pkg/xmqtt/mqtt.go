package xmqtt

import (
	"encoding/json"
	"sync"
	"sync/atomic"
	"time"

	"bs.com/app/pkg/xlog"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

type MqttClient struct {
	client mqtt.Client

	routes map[string]MqttHandler
	//mapTopicHandler map[string]mqtt.MessageHandler
	sync.Mutex

	//mqtt client runtime
	recon int32 //重连次数统计, atomic32 for arm32 and amd64

	//QoS 0：消息最多传递一次，如果当时客户端不可用，则会丢失该消息。
	//QoS 1：消息传递至少 1 次。
	//QoS 2：消息仅传送一次。
	//如果要支持离线消息，则需要 Qos 为1， cleanSession 为 false
	defaultQos   uint8
	cleanSession bool

	host     string
	clientID string

	willTopic string
	willMsg   string

	ConnectHandler        mqtt.OnConnectHandler      //新建连接的回调
	ReConnectHandler      mqtt.OnConnectHandler      //重连的回调
	ConnectLostHandler    mqtt.ConnectionLostHandler //意外断开时的回调
	DefaultPublishHandler mqtt.MessageHandler        //当接收到不匹配任何已知订阅的消息时的回调
}

type MqttHandler func(topic string, data []byte) error

// 初始连接或者重连接的时候回调
func (a *MqttClient) defaultOnConnect(c mqtt.Client) {
	atomic.AddInt32(&a.recon, 1)
	xlog.Info("mqtt connection ok", "retry", a.recon)

	err := a.doSubAll()
	if err != nil {
		xlog.Error("do sub failed: " + err.Error())
	}

	cnt := atomic.LoadInt32(&a.recon)
	if cnt == 1 {
		//第一次连接
		if a.ConnectHandler != nil {
			a.ConnectHandler(c)
		}
	} else {
		//重连的时候
		if a.ReConnectHandler != nil {
			a.ReConnectHandler(c)
		}
	}
}

// 默认需要会话功能，支持离线消息。所以 cleanSession 应该是 false。
// 当客户端确定不再需要会话时，可使用 Clean Session 为 true 进行重连，重连成功后再断开连接。
// 如果是 MQTT 5.0 则可在断开连接时直接设置 Session Expiry Interval 为 0，表示连接断开后会话即失效。

/*
客户端为了能接收离线消息，需要以下条件：
1、客户端链接到 mqtt broker 的时候，需设置参数 clean session = false
2、客户端 clientID 不可以改变
3、客户端不可以在 onDisconnect 的时候,做 unsubscribe 的操作

目前测试发现， nats-mqtt 不支持 clean-session = false 的时候保持连接。EMQX 支持。
*/

func NewMqttClient(host, clientID string) *MqttClient {
	xlog.Debug("new mqtt client", "host", host, "clientID", clientID)

	a := &MqttClient{
		defaultQos:   1,
		cleanSession: true,
		routes:       make(map[string]MqttHandler),
		host:         host,
		clientID:     clientID,
	}

	return a
}

func (a *MqttClient) Handle(topic string, h MqttHandler) {
	a.Lock()
	defer a.Unlock()

	if _, ok := a.routes[topic]; ok {
		xlog.Info("duplicated routes", "topic", topic)
		return
	}
	a.routes[topic] = h
}

func (a *MqttClient) SetWill(willTopic string, willMsg string) {
	a.willTopic = willTopic
	a.willMsg = willMsg
}

func (a *MqttClient) Start(username, password string) error {
	xlog.Info("mqtt start")
	opts := mqtt.NewClientOptions()

	// ithings 需要用户名和密码
	opts.Username = username
	opts.Password = password

	//
	opts.AddBroker(a.host)
	opts.SetClientID(a.clientID)

	//发送PING请求之前应该等待的时间，心跳间隔
	opts.SetKeepAlive(60 * time.Second)

	//broker 不存储消息，仅转发
	//opts.SetCleanSession(true)
	opts.SetCleanSession(a.cleanSession)

	//重新连接尝试之间等待的最大时间
	opts.SetOnConnectHandler(a.defaultOnConnect)
	opts.SetConnectionLostHandler(a.defaultOnConnectLost)
	opts.SetDefaultPublishHandler(a.defaultOnPublish)
	opts.SetReconnectingHandler(a.defaultReconnectionHandler)

	opts.SetAutoReconnect(true).SetMaxReconnectInterval(12 * time.Second) //意外离线的重连参数
	opts.SetConnectRetry(true).SetConnectRetryInterval(3 * time.Second)   //首次连接的重连参数

	opts.SetOrderMatters(false) //不需要消息有序

	//will message
	if a.willTopic != "" && a.willMsg != "" {
		opts.SetWill(a.willTopic, a.willMsg, 0, false)
	}

	a.client = mqtt.NewClient(opts)

	token := a.client.Connect()
	token.WaitTimeout(10 * time.Second) //默认10秒超时
	if err := token.Error(); err != nil {
		xlog.Error("mqtt start failed, err :"+err.Error(), "clidnerID", a.clientID)
		return err
	}
	return nil
}

func (a *MqttClient) doSubAll() error {
	//连接成功，开始订阅
	for topic, h := range a.routes {
		//必须用匿名函数
		func(topic string, h MqttHandler) {
			token := a.client.Subscribe(topic, a.defaultQos, func(client mqtt.Client, message mqtt.Message) {
				t := message.Topic()
				h(t, message.Payload())
			})
			if token.Error() != nil {
				xlog.Error("sub failed", "topic", topic, "err", token.Error().Error())
			} else {
				xlog.Info("do sub ok", "topic", topic)
			}
		}(topic, h)
	}
	return nil
}

func (a *MqttClient) Pub(topic string, msg interface{}) error {
	//xlog.Info("pub : ", topic)
	data, _ := json.Marshal(msg)
	token := a.client.Publish(topic, a.defaultQos, false, data)

	token.WaitTimeout(5 * time.Second)
	err := token.Error()
	if err != nil {
		xlog.Error("mqtt pub failed: PubRetain, err:"+err.Error(), "topic", topic)
	}
	return err
}

func (a *MqttClient) PubRaw(topic string, data []byte) error {
	token := a.client.Publish(topic, a.defaultQos, false, data)

	token.WaitTimeout(5 * time.Second)
	err := token.Error()
	if err != nil {
		xlog.Error("mqtt pub failed: PubRetain, err:"+err.Error(), "topic", topic)
	}
	return err
}

func (a *MqttClient) PubRetain(topic string, msg interface{}) error {
	data, _ := json.Marshal(msg)
	token := a.client.Publish(topic, a.defaultQos, true, data)

	token.WaitTimeout(5 * time.Second)
	err := token.Error()
	if err != nil {
		xlog.Error("mqtt pub failed: PubRetain, err:"+err.Error(), "topic", topic)
	}
	return err
}

func (a *MqttClient) Stop() {
	xlog.Info("mqtt stop")
	for topic, _ := range a.routes {
		xlog.Info("mqtt unsub", "topic", topic)
		if token := a.client.Unsubscribe(topic); token.Wait() && token.Error() != nil {
			xlog.Error("mqtt unsub failed", "topic", topic, "err", token.Error())
		}
	}

	if a.client != nil {
		a.client.Disconnect(250)
	}

}

//===========================================================================================

// 意外断开时执行。调用Disconnect或ForceDisconnect时不会执行
func (a *MqttClient) defaultOnConnectLost(c mqtt.Client, err error) {
	xlog.Warn("default: mqtt connection lost", "err", err, "host", a.host, "cliend_id", a.clientID)

	if a.ConnectLostHandler != nil {
		a.ConnectLostHandler(c, err)
	}

	time.Sleep(3 * time.Second)
}

// 当接收到不匹配任何已知订阅的消息时的回调
func (a *MqttClient) defaultOnPublish(c mqtt.Client, msg mqtt.Message) {
	if a.DefaultPublishHandler != nil {
		a.DefaultPublishHandler(c, msg)
	}
}

// TODO：是否要在重连的时候做点什么？
// 断开再重连是否需要重新订阅？是否需要cleansession = false
func (a *MqttClient) defaultReconnectionHandler(c mqtt.Client, opt *mqtt.ClientOptions) {
	xlog.Info("default: mqtt reconnecting")
}
