package xwg

import (
	"context"
	"errors"

	"github.com/abiosoft/ishell/v2"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
)

type Xshell struct {
	name  string
	shell *ishell.Shell
	cmds  []*ishell.Cmd
	exit  chan int
}

func NewXshell(name string, cmds ...*ishell.Cmd) IService {
	s := &Xshell{
		name:  name,
		shell: ishell.New(),
		cmds:  make([]*ishell.Cmd, 0),
		exit:  make(chan int),
	}
	s.cmds = append(s.cmds, cmds...)

	return s
}

func (s *Xshell) Name() string {
	return s.name
}

func (s *Xshell) Run(ctx context.Context) error {
	defer func() {
		xlog.Info("xshell exit", "name", s.name)
	}()

	for _, one := range s.cmds {
		s.shell.AddCmd(one)
	}

	name := &ishell.Cmd{
		Name: "name",
		Help: "show name",
		Func: func(c *ishell.Context) {
			c.Println(s.Name())
		},
	}

	//系统运行时信息
	{
		osinfo := &ishell.Cmd{
			Name: "os",
			Help: "show os info",
			Func: func(c *ishell.Context) {
				c.Println(xutils.InfoOS())
			},
		}
		cpu := &ishell.Cmd{
			Name: "cpu",
			Help: "show cpu info",
			Func: func(c *ishell.Context) {
				c.Println(xutils.InfoCPU())
			},
		}
		ram := &ishell.Cmd{
			Name: "ram",
			Help: "show ram info",
			Func: func(c *ishell.Context) {
				c.Println(xutils.InfoRAM())
			},
		}
		disk := &ishell.Cmd{
			Name: "disk",
			Help: "show disk info",
			Func: func(c *ishell.Context) {
				c.Println(xutils.InfoDisk())
			},
		}
		// 这些变量已经被使用，不再需要标记为未使用

		s.shell.AddCmd(osinfo)
		s.shell.AddCmd(cpu)
		s.shell.AddCmd(ram)
		s.shell.AddCmd(disk)
		s.shell.AddCmd(name)
	}
	//============================================================================

	errCh := make(chan error)
	go func() {
		s.shell.Start()
		s.shell.Wait() //block here
		s.shell.Close()
		errCh <- errors.New("exit by ishell")
	}()

	select {
	case err := <-errCh:
		return err
	case <-ctx.Done():
		return nil
	}
}
