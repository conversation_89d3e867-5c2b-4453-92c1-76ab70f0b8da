package xwg

import (
	"context"
	"time"
)

//job：任务
//interval：执行的间隔时间
type Template struct {
	job      func()
	interval time.Duration
}

func NewTemplateService(job func(), interval time.Duration) IService {
	return &Template{
		job:      job,
		interval: interval,
	}
}

//定时执行job
func (t *Template) Run(ctx context.Context) error {
	tc := time.NewTicker(10 * time.Minute)
	//tc := time.NewTicker(10 * time.Second)
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-tc.C:
			t.job()
		}
	}
}

func (t *Template) Name() string {
	return "service-template"
}
