package xwg

import (
	"context"
	"sync"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xutils"
)

type IService interface {
	Name() string                  //服务的名字
	Run(ctx context.Context) error //启动服务，启动异常则直接报错并退出全部协程。
}

// 对waitgroup的封装
type WorkerGroup struct {
	ctx    context.Context
	cancel context.CancelFunc
	wg     *sync.WaitGroup
}

func NewWorkerGroup() *WorkerGroup {
	wg := &WorkerGroup{
		wg: &sync.WaitGroup{},
	}
	wg.ctx, wg.cancel = context.WithCancel(context.Background())
	return wg
}

// 在workgroup里面执行此线程
func (w *WorkerGroup) Go(s IService) {
	w.wg.Add(1)

	go func() {
		defer func() {
			w.wg.Done()
			xutils.Recovery("xwg-GO")
		}()

		//任意一个子服务（协程）退出的时候，返回 error，就会关闭所有其他协程。
		//传递 context ，方便main 线程接收到 signal 退出的时候，可以通过 ctx 的 chan 收到信号，做一些退出前的工作。
		err := s.Run(w.ctx)
		if err != nil {
			xlog.Warnf("service %s exit, err message: %s", s.Name(), err)
			w.cancel()
		}
	}()
}

// 阻塞等待所有服务自己退出
func (w *WorkerGroup) Wait() {
	w.wg.Wait()
}
