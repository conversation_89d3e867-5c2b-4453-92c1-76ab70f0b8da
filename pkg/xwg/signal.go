package xwg

import (
	"context"
	"errors"
	"os"
	"os/signal"
	"syscall"

	"bs.com/app/pkg/xlog"
)

type SignalServer struct {
	quit chan os.Signal
}

func NewSignalServer() IService {
	return &SignalServer{
		quit: make(chan os.Signal),
	}
}

var signalException = errors.New("stop all server by signal")

// http server 无法监听 ctx的done，所以只能主动关闭
func (s *SignalServer) Run(ctx context.Context) error {
	signal.Notify(s.quit, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT, syscall.SIGKILL)
	select {
	case <-ctx.Done():
		return nil

	case sig := <-s.quit:
		xlog.Info("recv signal:" + sig.String())
		return signalException
	}
}

func (s *SignalServer) Name() string {
	return "signal"
}
