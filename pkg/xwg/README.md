
## 介绍

这个包是封装了 sync.Waitgroup 和 context， 用来管理协程的。

服务需要实现三个接口

```
type IService interface {
    Name() string                    //服务的名字
    Start(ctx context.Context) error //启动服务，启动异常则直接报错并退出。
    Status() bool                    //运行状态，true：running， false：stoped，测试用。
}
```

其中主要是 start接口

- start 在返回错误的时候，会cancel掉ctx.Done, 结束整个 WorkGroup 的所有服务。
- start 启动服务之后，必须监听ctx的done 和 exit，如果done 或者 exit被关闭，则自己退出。
- 每个服务管理自己的退出操作。
- 服务可以在完成任务后正常退出。也可以通过 Stop 接口，强制退出。（http server除外）
