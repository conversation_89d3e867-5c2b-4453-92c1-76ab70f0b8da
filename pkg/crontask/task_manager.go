package crontask

import (
	"fmt"
	"sync"
	"time"

	"bs.com/app/pkg/xlog"
	"github.com/robfig/cron/v3"
)

type TaskEntry struct {
	Name string       `json:"name"`
	ID   cron.EntryID `json:"id"`
	// Schedule on which this job should be run.
	Schedule cron.Schedule `json:"schedule"`

	// Next time the job will run, or the zero time if <PERSON><PERSON> has not been
	// started or this entry's schedule is unsatisfiable
	Next time.Time `json:"next"`

	// Prev is the last time this job was run, or the zero time if never.
	Prev time.Time `json:"prev"`
	Spec string    `json:"spec"`
}

type Timer interface {
	AddTaskByFunc(taskName string, spec string, task func(...interface{}), args ...interface{}) (cron.EntryID, error)
	AddTaskByJob(taskName string, spec string, job interface{ Run() }, args ...interface{}) (cron.EntryID, error)
	List() []*TaskEntry
	Remove(taskName string) error

	Start()
	Close()
}

// timer 定时任务管理
type timer struct {
	c        *cron.Cron
	taskList map[string]TaskEntry
	sync.Mutex
}

func (t *timer) List() []*TaskEntry {
	t.Lock()
	defer t.Unlock()

	// 将name和entry_id互换，后面可根据ie返回name
	swappedMap := make(map[cron.EntryID]TaskEntry)
	for _, value := range t.taskList {
		swappedMap[value.ID] = value
	}

	result := []*TaskEntry{}
	// 打印所有任务条目
	entries := t.c.Entries()
	for _, entry := range entries {
		// fmt.Printf("Task: %s, Next Run: %s\n", entry.ID, entry.Next)
		taskEntry := swappedMap[entry.ID]
		tentry := TaskEntry{
			Name:     taskEntry.Name,
			ID:       entry.ID,
			Schedule: entry.Schedule,
			Next:     entry.Next,
			Prev:     entry.Prev,
			Spec:     taskEntry.Spec,
		}
		// result[name] = tentry
		result = append(result, &tentry)
	}
	return result
}

// AddTaskByFunc 通过函数的方法添加任务
func (t *timer) AddTaskByFunc(taskName string, spec string, task func(...interface{}), option ...interface{}) (cron.EntryID, error) {
	t.Lock()
	defer t.Unlock()

	if _, ok := t.taskList[taskName]; ok {
		return 0, fmt.Errorf("task with name %s already exists", taskName)
	}

	// 使用闭包捕获参数
	wrappedTask := func() {
		task(option...)
	}
	entryID, err := t.c.AddFunc(spec, wrappedTask)
	if err != nil {
		return 0, err
	}

	t.taskList[taskName] = TaskEntry{
		ID:   entryID,
		Spec: spec,
		Name: taskName,
	}
	return entryID, err
}

// AddTaskByJob 通过接口的方法添加任务
func (t *timer) AddTaskByJob(taskName string, spec string, job interface{ Run() }, option ...interface{}) (cron.EntryID, error) {
	t.Lock()
	defer t.Unlock()
	if _, exists := t.taskList[taskName]; exists {
		return 0, fmt.Errorf("task with name %s already exists", taskName)
	}

	// xlog.Debug("add task by job", "spec", spec, ", job:", job)
	entryID, err := t.c.AddJob(spec, job)
	if err != nil {
		xlog.Debug("timer add task by job error", "err", err.Error())
		return entryID, err
	}

	t.taskList[taskName] = TaskEntry{
		ID:   entryID,
		Spec: spec,
		Name: taskName,
	}
	return entryID, err
}

// StartTask 开始启动 cront
func (t *timer) Start() {
	t.Lock()
	defer t.Unlock()
	t.c.Start()
}

// Remove 从taskName 删除指定任务
func (t *timer) Remove(taskName string) error {
	t.Lock()
	defer t.Unlock()
	if v, ok := t.taskList[taskName]; ok {
		t.c.Remove(cron.EntryID(v.ID))
		delete(t.taskList, taskName)
		return nil
	}
	return fmt.Errorf("not found task by name: %s", taskName)
}

// Close 释放资源
func (t *timer) Close() {
	t.Lock()
	defer t.Unlock()
	t.c.Stop()
}

func NewTimerTask() Timer {
	// return &timer{taskList: make(map[string]*cron.Cron)}
	return &timer{
		c:        cron.New(),
		taskList: make(map[string]TaskEntry),
	}
}
