package xcode

import (
	"fmt"
)

type ErrCode struct {
	code int
	msg  string
}

// Error return a error string
func (e *ErrCode) String() string {
	return fmt.Sprintf("code: %d, msg: %s", e.Code(), e.msg)
}

func (e *ErrCode) Code() int {
	return e.code
}

func (e *ErrCode) Msg() string {
	return e.msg
}

// NewError create a error
var errorCodes = map[int]*ErrCode{} //避免重复添加
func NewError(code int, msg string) *ErrCode {
	if _, ok := errorCodes[code]; ok {
		// panic(fmt.Sprintf("code %d is exsit, please change one", code))
		// 如果已经存在，直接返回这个code
		return errorCodes[code]
	}
	curCode := &ErrCode{code: code, msg: msg}
	errorCodes[code] = curCode
	return curCode
}

var (
	// 预定义错误
	// Common errors
	Success = NewError(0, "Ok")

	ERRCODE_SYSTEM           = NewError(10001, "系统内部错误")
	ERRCODE_PARAM            = NewError(10002, "参数错误")
	ErrMethodNotAllowed      = NewError(10003, "Method not allowed")
	ERRCODE_DB_NOT_FOUND     = NewError(10005, "数据未找到")
	ERRCODE_DB_UPDATE        = NewError(10006, "更新失败")
	ERRCODE_DAO              = NewError(10009, "数据错误")
	ERRCODE_IO               = NewError(10010, "IO异常")
	ERRCODE_DB_CACHE         = NewError(10011, "缓存异常")
	ERRCODE_QUERY_OVER_RANGE = NewError(10012, "查询时间范围太大")
	ERRCODE_EMPTY            = NewError(10013, "没有数据")
	ERRCODE_DATA_NOT_OK      = NewError(10014, "请先召测，再整编")

	ERRCODE_INVALID               = NewError(10015, "无效数据")
	ERRCODE_NOT_SUPPORT           = NewError(10016, "不支持")
	ERRCODE_RPC                   = NewError(10017, "RPC错误")
	ERRCODE_UPLOAD_FILE           = NewError(10018, "文件上传失败")
	ERRCODE_PERMISSION_DUPLICATED = NewError(10019, "权限字段重复")
	ERRCODE_NOT_SUPER             = NewError(10020, "非超级管理员，无权限")
	ERRCODE_INVALID_TOKEN         = NewError(10021, "请重新登录")   // 无效的token
	ERRCODE_EXPORT_EXCEL          = NewError(10022, "导出模板错误")  //
	ERRCODE_LICENSE_EXIST         = NewError(10023, "此激活码已存在") //

	//user
	ERRCODE_USER_LOGIN       = NewError(11001, "登录错误")
	ERRCODE_USER_NOT_EXIST   = NewError(11002, "用户不存在")
	ERRCODE_USER_OR_PASSWORD = NewError(11003, "账号或密码错误")
	ERRCODE_CAPTCH           = NewError(11004, "请点击验证码，重新获取")
	ERRCODE_CHECKPERMISSION  = NewError(11006, "检查用户权限错误")
	ERRCODE_NOT_PERMISSION   = NewError(11007, "没有权限")
	ERRCODE_PHONE_IS_EXISTS  = NewError(11008, "该手机号码已存在")
	ERRCODE_SEND_PHONE_SMS   = NewError(11009, "发送手机验证码错误")
	ERRCODE_SMS_CODE         = NewError(11010, "手机验证码错误")
	ERRCODE_JWT              = NewError(11011, "JWT相关错误")

	ERRCODE_SESSION_INVALID  = NewError(11012, "未登陆或已掉线")
	ERRCODE_IMAGE_EXT_ERROR  = NewError(11013, "上传图片后缀错误")
	ERRCODE_SMS_OVER_LIMIT   = NewError(11014, "发送短信间隔未满1分钟")
	ERRCODE_CASBIN           = NewError(11015, "casbin接口错误")
	ERRCODE_PASSWORD_ERR     = NewError(11016, "密码错误")
	ERRCODE_CAN_NOT_DEL      = NewError(11017, "正在被使用，不可删除")
	ERRCODE_REMOTE_ERR       = NewError(11018, "远程操作失败")
	ERRCODE_PASSWD_NOT_EQ    = NewError(11019, "两次密码不相同")
	ERRCODE_TOKEN            = NewError(11020, "token不正确")
	ERRCODE_PASSWORD_TIMEOUT = NewError(11021, "密码已经过期")

	//iot
	ERRCODE_FIRMWARE_OVER_SIZE       = NewError(12001, "上传的文件太大")
	ERRCODE_FIRMWARE_LOWWER          = NewError(12002, "软件版本太低")
	ERRCODE_FIRMWARE_INVALID_VERSION = NewError(12003, "版本号不满足semVer标准")
	ERRCODE_DEVICE_OFFLINE           = NewError(12004, "设备不在线")
	ERRCODE_NETWORK_TIMEOUT          = NewError(12005, "连接超时")
	ERRCODE_INVALID_PRODUCT          = NewError(12006, "无效的产品ID")
	ERRCODE_SET_CONFIG_DEVICE        = NewError(12007, "配置设备参数错误")
	ERRCODE_GET_CONFIG_DEVICE        = NewError(12008, "读取设备参数错误")
	ERRCODE_INVALID_SN               = NewError(12009, "SN 不合法")
	ERRCODE_NOT_OWNER                = NewError(12010, "没有操作此设备的权限")
	ERRCODE_COMMAND_TIMEOUT          = NewError(12011, "命令超时")
	ERRCODE_ATTRI_ERROR              = NewError(12012, "属性命令错误")

	//	station
	ERRCODE_INVALID_SENSOR      = NewError(14001, "无效的传感器参数")
	ERRCODE_INVALID_TIMERANGE   = NewError(14002, "无效的时间范围")
	ERRCODE_STAION_OFFLINE      = NewError(14003, "测站不在线")
	ERRCODE_STATION_SN_EXISTS   = NewError(14004, "测站编码已经被占用")
	ERRCODE_MIEI_HAS_USE        = NewError(14005, "当前设备已用于其他测站")
	ERRCODE_STATION_AREA        = NewError(14006, "请至少填写一个地址类型:经纬度或名称")
	ERRCODE_STATION_SN_STR      = NewError(14007, "请填写只包含数字的测站编码")
	ERRCODE_STATION_SENSOR      = NewError(14008, "请先配置测站传感器")
	ERRCODE_STATION_DELETE      = NewError(14009, "删除测站错误")
	ERRCODE_STATION_WARN_EXISTS = NewError(14010, "该预警元素的预警等级已经存在")
	ERRCODE_ST_SN_HAS_USE       = NewError(14011, "当前机身序列号已经被使用")
	ERRCODE_DECODE_E1           = NewError(14012, "解码失败")

	// 北向接口鉴权
	ERRCODE_AUTH_ERR  = NewError(40200, "无权限")
	ERRCODE_SIGNATURE = NewError(40201, "签名错误")
	ERRCODE_EXPIRED   = NewError(40202, "请求已过期")

	// API权限相关错误码
	ERRCODE_API_NOT_FOUND     = NewError(40300, "API不存在")
	ERRCODE_API_NO_PERMISSION = NewError(40301, "无权访问该API")
	ERRCODE_API_RATE_LIMIT    = NewError(40302, "请求过于频繁")
)
