package filecache

import (
	"fmt"
	"testing"
	"time"
)

func TestFileCache(t *testing.T) {

	// 创建文件缓存
	cache := NewFileCache()

	// 获取文件缓存
	key := "key1"
	filename := "cache.go"
	var ok bool
	var err error
	var data []byte
	for i := 1; i < 10; i++ {
		data, ok = cache.Get(key)
		if !ok {
			data, err = cache.CacheFile(key, filename)
			if err != nil {
				return
			}
		}
		fmt.Printf("Filename: %s, key: %s,  Data Len: %d\n", filename, key, len(data))
		time.Sleep(1 * time.Second)
	}
}
