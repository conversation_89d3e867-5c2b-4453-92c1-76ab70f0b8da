package filecache

import (
	"os"
	"sync"
	"time"

	"bs.com/app/pkg/xlog"
)

// FileCache 用于存储文件缓存
// 注意：别搞太大文件

type FileCache struct {
	cache map[string]CacheItem
	mutex sync.RWMutex
}

// CacheItem 表示缓存中的一个项目
type CacheItem struct {
	data      []byte
	size      int
	expiredAt time.Time
}

// NewFileCache 创建一个新的文件缓存
func NewFileCache() *FileCache {
	return &FileCache{
		cache: make(map[string]CacheItem),
	}
}

// Get 获取文件缓存，如果缓存不存在则读取文件并添加到缓存
func (fc *FileCache) Get(key string) ([]byte, bool) {
	fc.mutex.RLock()
	item, ok := fc.cache[key]
	fc.mutex.RUnlock()
	return item.data, ok
}

// Get 获取文件缓存，如果缓存不存在则读取文件并添加到缓存
func (fc *FileCache) CacheFile(key, filename string) ([]byte, error) {
	// 缓存不存在，读取文件并添加到缓存
	xlog.Debug("add new file : " + filename)

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	fc.mutex.Lock()

	// 插入新文件缓存
	fc.cache[key] = CacheItem{
		data:      data,
		size:      len(data),
		expiredAt: time.Now().Add(10 * 24 * time.Hour), // 添加到缓存时设置过期时间为10天后
	}

	// 同时检查下是否有过期文件
	xlog.Debug("check filecache expired")
	now := time.Now()
	for one, item := range fc.cache {
		if now.After(item.expiredAt) {
			delete(fc.cache, one)
		}
	}
	fc.mutex.Unlock()

	return data, nil
}
