package orm

import (
	"database/sql"
	"fmt"
	"os"
	"time"

	"github.com/go-sql-driver/mysql"
	gormsql "gorm.io/driver/mysql"
	"gorm.io/gorm"

	"bs.com/app/pkg/xlog"
)

func InitGormMysql(dbname, pwd, addr, dbLogLevel string) *gorm.DB {

	xlog.Debug("=============== init gorm", "dbname", dbname, "addr", addr, "log_level", dbLogLevel)

	dsn := getMysqlDSN(dbname, pwd, addr)
	//"root:123456@tcp(127.0.0.1:3306)/dayu?charset=utf8mb4&parseTime=True&loc=Local&timeout=30s"
	xlog.Debug("mysql dsn : " + dsn)

	if dbLogLevel == "" {
		dbLogLevel = "error"
	}
	db, err := gorm.Open(
		gormsql.Open(dsn),
		gormConfig(dbLogLevel),
	)

	if err != nil {
		xlog.Error("init mysql failed:" + err.Error())
		os.Exit(0)
	}

	if dbLogLevel == "debug" {
		db = db.Debug() //开启调试
	}

	//设置 mysql 连接池参数
	sqlDb, err := db.DB()
	if err != nil {
		xlog.Error("init mysql failed:" + err.Error())
		os.Exit(0)
	}

	err = sqlDb.Ping()
	if err != nil {
		xlog.Error("gorm ping err:", err)
		os.Exit(1)
	}
	sqlDb.SetConnMaxLifetime(time.Hour)
	maxPoolSize := 10
	maxIdle := 10
	sqlDb.SetMaxOpenConns(maxPoolSize) // 打开到数据库的最大连接数
	sqlDb.SetMaxIdleConns(maxIdle)     // 空闲中的最大连接数

	return db
}

// 如果数据库不存在，则创建
// 返回数据库的dsn： 数据库名字是参数
func getMysqlDSN(dbname, pwd, addr string) string {
	dSource := mysql.NewConfig()
	dSource.User = "root"
	dSource.Passwd = pwd
	dSource.DBName = dbname
	dSource.Net = "tcp"
	dSource.Addr = addr //"127.0.0.1:3306"
	dSource.ParseTime = true
	dSource.Loc = time.Local
	//dSource.Params = map[string]string{"charset": "utf8mb4,utf8"}
	dSource.Params = map[string]string{"charset": "utf8mb4", "loc": "Local"}
	//dSource.Params = map[string]string{"charset": "utf8"}

	//loc := time.FixedZone("CST", 8*3600) // 东八
	//loc, _ := time.LoadLocation("Asia/Shanghai")
	//dSource.Loc = loc

	createDatabase(dSource.User, dSource.Passwd, dSource.Addr, dSource.DBName)

	return dSource.FormatDSN()
}

// create database if not exists
// create database dayu default character set utf8mb4 collate utf8mb4_unicode_ci;
func createDatabase(user, password, addr, dbname string) {

	// mysql拼接dsn
	/*
	   func getDSN(user, password, host, dbname string, port uint16) string {
	   	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
	   		user,
	   		password,
	   		host,
	   		port,
	   		dbname)
	   	return dsn
	   }
	*/

	dsn := fmt.Sprintf("%s:%s@tcp(%s)/", user, password, addr)
	xlog.Info("createDatabase  dsn : " + dsn)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		fmt.Println("mysql open failed :", err.Error())
		os.Exit(0)
	}

	defer func(db *sql.DB) {
		_ = db.Close()
	}(db)

	if err = db.Ping(); err != nil {
		fmt.Println("mysql ping failed :", err.Error())
		os.Exit(0)
	}

	//fmt.Println("create database : ", dbname)
	createSQL := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS `%s` CHARACTER SET utf8mb4 collate utf8mb4_unicode_ci;", dbname)
	_, err = db.Exec(createSQL)
	if err != nil {
		fmt.Println("mysql create database failed:", err.Error())
		os.Exit(0)
	}
}
