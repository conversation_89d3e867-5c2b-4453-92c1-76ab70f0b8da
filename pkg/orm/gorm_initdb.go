package orm

import (
	"database/sql"
	"fmt"
	"os"
	"strings"

	"bs.com/app/pkg/xlog"
)

// 检查数据库是否存在，不存在则创建
func PgCheckOrCreateDatabase(dbname, user, pwd, addr string) bool {
	host, port := "", ""
	addrList := strings.Split(addr, ":")
	if len(addrList) < 2 {
		host = addr
		port = "5432"
	} else {
		host = addrList[0]
		port = addrList[1]
	}
	if port == "" {
		port = "5432"
	}
	connInfo := fmt.Sprintf("host=%s user=%s  dbname=postgres password=%s port=%s  sslmode=disable", host, user, pwd, port)
	// 连接到PostgreSQL服务器（不指定数据库，因为我们要检查的是数据库本身）
	db, err := sql.Open("postgres", connInfo)
	if err != nil {
		xlog.Errorf("failed to connect to postgres: %v", err)
		os.Exit(0)
	}
	defer db.Close()
	existed, err := CheckDatabaseExists(db, dbname)
	if err != nil {
		xlog.Error("check database exists err:", err.Error())
		os.Exit(0)
	}
	if existed {
		// 已经存在，则直接返回
		return true
	}

	// 不存在，则需要创建
	err = CreatePgDatabase(db, dbname, user)
	if err != nil {
		xlog.Errorf("failed to create db : %v", err)
		os.Exit(0)
	}
	return false
}

// 只查询是否存在
func CheckDatabaseExists(db *sql.DB, dbName string) (bool, error) {
	var count int
	err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM pg_database WHERE datname = '%s' ", dbName)).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func CreatePgDatabase(db *sql.DB, dbName string, user string) error {
	sql := fmt.Sprintf(`CREATE DATABASE %s WITH OWNER %s TEMPLATE template0 ENCODING 'UTF8';`, dbName, user)
	_, err := db.Exec(sql)
	if err != nil {
		xlog.InfoMsg("db: error:", err.Error())
	}
	return err
}
