package orm

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"gorm.io/gorm/utils"

	"bs.com/app/pkg/xlog"
)

func gormConfig(level string) *gorm.Config {

	var mLevel = map[string]logger.LogLevel{
		"silent": logger.Silent,
		"error":  logger.Error,
		"warn":   logger.Warn,
		"info":   logger.Info,
		"debug":  logger.Info,
	}

	gormLogger := NewGromLogger(
		logConfig{
			SlowThreshold: 200 * time.Millisecond,
			LogLevel:      mLevel[level],
		})
	// _ = gormLogger

	// gormLogger.LogMode(mLevel[level])

	configuration := &gorm.Config{
		PrepareStmt: false, //缓存每一条sql语句，提高执行速度
		//SkipDefaultTransaction: false, //为每一次修改创建事务
		SkipDefaultTransaction: true, //为每一次修改禁用事务，提高性能
		//NowFunc: func() time.Time {
		//	return time.Now().Local()
		//},
		DisableForeignKeyConstraintWhenMigrating: true, //不创建外键
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",   // 表名前缀
			SingularTable: true, // 使用单数表名
			//NameReplacer:  strings.NewReplacer("_", "_"), // 在转为数据库名称之前，使用NameReplacer更改结构/字段名称。
		},
		// 不打印日志
		Logger: gormLogger,
	}

	return configuration
}

// ========================================================
// 是否使用logrus日志
type logConfig struct {
	SlowThreshold time.Duration
	LogLevel      logger.LogLevel
}

// 如果定义了使用xlog，则不使用此writer
func NewGromLogger(config logConfig) logger.Interface {
	fmt.Println("new gorm logger")
	myLogger := xlog.NewWithDepth(5).WithKV("module", "gorm")

	// 根据 gorm logger level 设置 xlog level
	switch config.LogLevel {
	case logger.Silent:
		myLogger.SetLevel("silent")
	case logger.Error:
		myLogger.SetLevel("error")
	case logger.Warn:
		myLogger.SetLevel("warn")
	case logger.Info:
		myLogger.SetLevel("info")
	default:
		myLogger.SetLevel("error")
	}

	return &_logger{
		ILogger:   myLogger,
		logConfig: config,
	}
}

type _logger struct {
	xlog.ILogger
	logConfig
}

func (c *_logger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := c.ILogger
	switch level {
	case logger.Silent:
		newLogger.SetLevel("silent")
	case logger.Error:
		newLogger.SetLevel("error")
	case logger.Info:
		newLogger.SetLevel("info")
	case logger.Warn:
		newLogger.SetLevel("warn")
	default:
		newLogger.SetLevel("error")
	}

	return &_logger{
		ILogger: newLogger,
		logConfig: logConfig{
			SlowThreshold: c.SlowThreshold,
			LogLevel:      level,
		},
	}
}

// Info print info
func (c *_logger) Info(ctx context.Context, message string, data ...interface{}) {
	if c.LogLevel >= logger.Info {
		arr := []any{}
		// arr := []any{"fileline", utils.FileWithLineNum()}

		arr = append(arr, data...)
		c.ILogger.Info(message, arr...)
	}
}

// Warn print warn messages
func (c *_logger) Warn(ctx context.Context, message string, data ...interface{}) {
	if c.LogLevel >= logger.Warn {
		arr := []any{}
		// arr := []any{"fileline", utils.FileWithLineNum()}

		arr = append(arr, data...)
		c.ILogger.Warn(message, arr...)
	}
}

// Error print error messages
func (c *_logger) Error(ctx context.Context, message string, data ...interface{}) {
	if c.LogLevel >= logger.Error {
		arr := []any{}
		// arr := []any{"fileline", utils.FileWithLineNum()}
		arr = append(arr, data...)
		c.ILogger.Error(message, arr...)
	}
}

// Trace print sql message
func (c *_logger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)

	switch {
	case err != nil:
		sql, rows := fc()
		c.ILogger.Error(err.Error(), "fileline", utils.FileWithLineNum(), "elapsed", float64(elapsed.Nanoseconds())/1e6, "rows", rows, "sql", sql)
	case elapsed > c.SlowThreshold && c.SlowThreshold != 0:
		sql, rows := fc()
		slowLog := fmt.Sprintf("SLOW SQL >= %v", c.SlowThreshold)
		c.ILogger.Warn(slowLog, "fileline", utils.FileWithLineNum(), "elapsed", float64(elapsed.Nanoseconds())/1e6, "rows", rows, "sql", sql)
	default:
		sql, rows := fc()
		if c.LogLevel == logger.Info {
			c.ILogger.Info("trace gorm", "fileline", utils.FileWithLineNum(), "elapsed", float64(elapsed.Nanoseconds())/1e6, "rows", rows, "sql", sql)
		}
	}
}
