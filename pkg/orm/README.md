#### 介绍

数据库和缓存的驱动包封装。


create database if not exists

create database dayu default character set utf8mb4 collate utf8mb4_unicode_ci;

#### Gorm使用要点

一、为什么字段要用指针类型？？？

1、查询的时候判空。

如果要过滤掉空值，则必须用指针类型。比如gorm.Model 的 DeletedAt 是指针类型。
查询的时候，指针也很方便的判断是否为空。当 Instance 结构体引用其他结构体时，如果是可能为Null的，都要用指针，否则不好判断是不是真的查到的这条记录。

查询的时候，都会有一个条件 `instances`.`deleted_at` IS NULL，所以只会查询未被删除的值。

如果这里不用指针，则不好判断空。


```
type Model struct {
    ID        uint `gorm:"primary_key"`
    CreatedAt time.Time
    UpdatedAt time.Time
    DeletedAt *time.Time `sql:"index"`
}
```

2、还有一个问题是，当使用指针类型的时候，可以不和默认值起冲突。比如一个字符串字段，默认是"default"，如果建表的时候是空字符串，则系统自动赋值 "default"。与空值冲突。
使用字符串指针则解决此问题：满足默认值（null） 和空值。



二、更新
gorm 设置更新的时候，会忽略零值。如果要设置为空值，则需要用map，或者使用 select 指定更新列。


## TODO

- 统一数据库初始化和 orm logger