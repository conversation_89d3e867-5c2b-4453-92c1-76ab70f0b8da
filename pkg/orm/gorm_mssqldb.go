package orm

import (
	"database/sql"
	"fmt"
	"net/url"
	"time"

	"bs.com/app/config"
	"bs.com/app/pkg/xlog"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

const (
//	测试参数
//
// username = "sa"
// password = "zhdgps_123456"
//
// hostname = "*************"
// port     = 31433
// database = "adapter"
)

func InitGormMSSQL() (*gorm.DB, error) {

	cfg := config.Get().MSSql
	username, password := cfg.Username, cfg.Password
	host, port := cfg.Host, cfg.Port
	database := cfg.AppDBName

	var dsn string

	query := url.Values{}
	query.Add("database", database)
	query.Add("encrypt", "disable")
	u := &url.URL{
		Scheme:   "sqlserver",
		User:     url.UserPassword(username, password),
		Host:     fmt.Sprintf("%s:%d", host, port),
		RawQuery: query.Encode(),
	}

	dsn = u.String()
	//dsn = fmt.Sprintf("sqlserver://%s:%s@%s:%d/tcp?database=%s", username, password, hostname, port, database)
	//dsnOK := "sqlserver://sa:zhdgps_123456@*************:31433/tcp?database=tempdb"

	xlog.Info("dsn : ", dsn)

	// var err error
	DB, err := connect(dsn)
	if err != nil {
		xlog.Error("sqlserver connect err:", err)
		return nil, err
	}

	//DB.Exec(fmt.Sprintf("use %s;", cfg.Database))

	return DB, nil
}

func connect(dsn string) (*gorm.DB, error) {
	configuration := &gorm.Config{
		PrepareStmt: false, //缓存每一条sql语句，提高执行速度
		//SkipDefaultTransaction: false, //为每一次修改创建事务
		SkipDefaultTransaction: true, //为每一次修改禁用事务，提高性能
		//NowFunc: func() time.Time {
		//	return time.Now().Local()
		//},
		DisableForeignKeyConstraintWhenMigrating: true, //不创建外键
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",   // 表名前缀
			SingularTable: true, // 使用单数表名
			//NameReplacer:  strings.NewReplacer("_", "_"), // 在转为数据库名称之前，使用NameReplacer更改结构/字段名称。
			NoLowerCase: true,
		},
	}

	var db *gorm.DB
	var err error
	cnt := 0
	for {
		db, err = gorm.Open(sqlserver.Open(dsn), configuration)
		if err != nil {
			cnt++
			if cnt < 10 {
				xlog.Warn("db connect retry ............ ", cnt)
				time.Sleep(1 * time.Second)
				continue
			} else {
				return nil, err
			}
		}
		break
	}

	db.Debug()

	sqlDb, err := db.DB()
	if err != nil {
		return nil, err
	}

	err = sqlDb.Ping()
	if err != nil {
		return nil, err
	}

	xlog.Info("SqlServer Ping OK")

	//sqlDb.SetConnMaxLifetime(time.Hour)
	//maxPoolSize := 10
	//maxIdle := 10
	//sqlDb.SetMaxOpenConns(maxPoolSize) // 打开到数据库的最大连接数
	//sqlDb.SetMaxIdleConns(maxIdle)     // 空闲中的最大连接数

	return db, err
}

// not used
func __useRaw() {
	cfg := config.Get().MSSql
	username, password := cfg.Username, cfg.Password
	host, port := cfg.Host, cfg.Port
	database := cfg.AppDBName

	//connString := fmt.Sprintf("server=%s;user id=%s;password=%s;port=%d;database=%s", hostname, username, password, port, database)
	connString := fmt.Sprintf("sqlserver://%s:%s@%s:%d/tcp?database=%s", username, url.QueryEscape(password), host, port, database)

	fmt.Println("connString :", connString)

	db, err := sql.Open("mssql", connString)
	if err != nil {
		fmt.Println("Open connection failed:", err.Error())
		return
	}

	defer db.Close()

	err = db.Ping()
	if err != nil {
		fmt.Println("Cannot connect: ", err.Error())
		return
	} else {
		fmt.Println("Ping OK")
	}
}
