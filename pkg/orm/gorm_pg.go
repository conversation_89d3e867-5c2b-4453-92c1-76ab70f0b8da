package orm

import (
	"fmt"
	"os"
	"time"

	_ "github.com/lib/pq"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"bs.com/app/pkg/xlog"
)

func InitPG(dbname, user, pwd, addr, dbLogLevel string) *gorm.DB {
	dsn := fmt.Sprintf("postgres://%s:%s@%s/%s?sslmode=disable&TimeZone=UTC", user, pwd, addr, dbname)

	xlog.Debug("postgres dsn : " + dsn)
	xlog.Debug("=============== init gorm", "dbname", dbname, "addr", addr, "log_level", dbLogLevel)

	if dbLogLevel == "" {
		dbLogLevel = "error"
	}
	db, err := gorm.Open(postgres.New(postgres.Config{DSN: dsn}), gormConfig(dbLogLevel))
	if err != nil {
		xlog.Error("init postgresql failed:" + err.Error())
		os.Exit(0)
	}

	if dbLogLevel == "debug" {
		db = db.Debug() //开启调试
	}

	// 设置 mysql 连接池参数
	sqlDb, err := db.DB()
	if err != nil {
		xlog.Error("init postgresql  db failed:" + err.Error())
		os.Exit(0)
	}

	err = sqlDb.Ping()
	if err != nil {
		xlog.Error("gorm ping err:", err)
		os.Exit(1)
	}

	xlog.Debug("-------------------- init pgsql OK...OK")

	sqlDb.SetConnMaxLifetime(time.Hour)
	maxPoolSize := 10
	maxIdle := 10
	sqlDb.SetMaxOpenConns(maxPoolSize) // 打开到数据库的最大连接数
	sqlDb.SetMaxIdleConns(maxIdle)     // 空闲中的最大连接数

	return db
}

func SetSchema(db *gorm.DB, schema_name string) *gorm.DB {
	// 检查是否存在当前 schema
	sql := fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s;", schema_name)
	err := db.Exec(sql).Error
	if err != nil {
		xlog.Error("postgresql create schema error:", err)
	}

	// 设置使用该schema
	sql = fmt.Sprintf("SET search_path TO %s", schema_name)
	err = db.Exec(sql).Error
	if err != nil {
		msg := fmt.Sprintf("postgresql set search_path to %s error:", schema_name)
		xlog.Error(msg, err)
	}
	return db
}
