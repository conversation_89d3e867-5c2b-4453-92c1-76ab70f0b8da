package orm

import (
	"fmt"
	"testing"

	"gorm.io/gorm"
)

type TestUser struct {
	gorm.Model
	Username string `josn:username`
	Password string `json:password`
}

func (TestUser) TableName(tenamtName string) string {
	if tenamtName == "" {
		return "test_user"
	}
	return fmt.Sprintf("%s.test_user", tenamtName)
}
func TestInitPG1(t *testing.T) {
	addr := "192.168.2.200:5432"
	user := "root"
	passwd := "root@1024"
	database := "ebox"

	db2 := InitPG(database, user, passwd, addr, "info")

	schema2 := "schema_table_test"

	db2 = SetSchema(db2, schema2)

	err := db2.AutoMigrate(&TestUser{})
	if err != nil {
		t.Error(err)
	}

	tu := TestUser{Username: "test1", Password: "pwsswd"}

	err = db2.Table(tu.TableName(schema2)).Create(&tu).Error
	if err != nil {
		t.Error("create error", err)
	}
	// _ = db
}

func TestInitPG2(t *testing.T) {
	addr := "192.168.2.200:5432"
	user := "root"
	passwd := "root@1024"
	database := "ebox"
	eboxDB := InitPG(database, user, passwd, addr, "info")
	db2 := InitPG(database, user, passwd, addr, "info")

	schema1 := "cbox"
	schema2 := "dbox"
	eboxDB = SetSchema(eboxDB, schema1)
	db2 = SetSchema(db2, schema2)
	err := eboxDB.AutoMigrate(&TestUser{})
	if err != nil {
		t.Error(err)
	}
	err = db2.AutoMigrate(&TestUser{})
	if err != nil {
		t.Error(err)
	}

	tu := TestUser{Username: "test1", Password: "pwsswd"}

	err = eboxDB.Create(&tu).Error
	if err != nil {
		t.Error("create error", err)
	}

	err = db2.Create(&tu).Error
	if err != nil {
		t.Error("create error", err)
	}
	// _ = db
}
