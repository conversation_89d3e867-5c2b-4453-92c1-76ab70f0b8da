package httpclient

import (
	"encoding/json"
	"strings"

	"github.com/guonaihong/gout"
)

type RespBody struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Payload json.RawMessage `json:"payload,omitempty"`
}

var (
	goutHeader = gout.H{
		"Accept": "application/json",
	}
)

type ClientInfo struct {
	Host  string
	Debug bool
}

func (c *ClientInfo) POST(url string, param interface{}) (err error, resp *RespBody) {
	urlStr := c.Host + "/" + strings.TrimLeft(url, "/")

	resp = &RespBody{}
	err = gout.POST(urlStr).SetHeader(goutHeader).SetJSON(param).Debug(c.Debug).BindJSON(resp).Do()
	return
}

func (c *ClientInfo) GET(url string, param map[string]interface{}) (err error, resp *RespBody) {
	urlStr := c.Host + "/" + strings.TrimLeft(url, "/")

	resp = &RespBody{}
	err = gout.GET(urlStr).SetHeader(goutHeader).SetQuery(param).Debug(c.Debug).BindJSON(resp).Do()
	return
}

func (c *ClientInfo) PUT(url string, param map[string]interface{}) (err error, resp *RespBody) {
	urlStr := c.Host + "/" + strings.TrimLeft(url, "/")

	resp = &RespBody{}
	err = gout.PUT(urlStr).SetHeader(goutHeader).SetQuery(param).Debug(c.Debug).BindJSON(resp).Do()
	return
}

func (c *ClientInfo) DELETE(url string, param map[string]interface{}) (err error, resp *RespBody) {
	urlStr := c.Host + "/" + strings.TrimLeft(url, "/")

	resp = &RespBody{}
	err = gout.PUT(urlStr).SetHeader(goutHeader).SetQuery(param).Debug(c.Debug).BindJSON(resp).Do()
	return
}

func (c *ClientInfo) Upload(url string, param map[string]interface{}, filePath string) (err error, resp *RespBody) {
	//客户端发送multipart/form-data到服务端
	param["file"] = gout.FormFile(filePath)

	resp = &RespBody{}
	urlStr := c.Host + "/" + strings.TrimLeft(url, "/")

	err = gout.POST(urlStr).SetHeader(goutHeader).Debug(c.Debug).SetForm(param).BindJSON(resp).Do()
	return
}
