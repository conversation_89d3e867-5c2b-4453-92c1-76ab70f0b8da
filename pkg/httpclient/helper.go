package httpclient

import (
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/guonaihong/gout"
)

func POSTSimIot(urlStr string, param map[string]interface{}) ([]byte, error) {
	data := url.Values{}
	for k, v := range param {
		data.Set(k, fmt.Sprintf("%v", v))
	}
	contentType := "application/x-www-form-urlencoded;charset=utf-8"

	req, err := http.NewRequest("POST", urlStr, strings.NewReader(data.Encode())) //必须这样才是form表单提交
	if err != nil {
		return nil, err
	}
	defer req.Body.Close()

	req.Header.Add("content-type", contentType)
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Timeout: 5 * time.Second, Transport: tr}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	result, _ := io.ReadAll(resp.Body)
	return result, nil
}

// func POSTSimIot(url string, data interface{}) ([]byte, error) {
// 	contentType := "application/x-www-form-urlencoded;charset=utf-8"

// 	buf, _ := json.Marshal(data)
// 	req, err := http.NewRequest("POST", url, bytes.NewBuffer(buf))
// 	if err != nil {
// 		return nil, err
// 	}
// 	defer req.Body.Close()

// 	req.Header.Add("content-type", contentType)
// 	client := &http.Client{Timeout: 5 * time.Second}
// 	resp, err := client.Do(req)
// 	if err != nil {
// 		return nil, err
// 	}
// 	defer resp.Body.Close()

// 	result, _ := io.ReadAll(resp.Body)
// 	return result, nil
// }

func POSTRaw(urlStr string, param interface{}) (interface{}, error) {
	resp := &RespBody{}
	err := gout.POST(urlStr).SetHeader(gout.H{"Accept": "application/json"}).SetBody(param).Debug(false).BindJSON(resp).Do()
	if err != nil {
		fmt.Println("post error:", err.Error())
		return nil, err
	}

	return resp, nil
}

// POSTJson 发送JSON数据，更可靠地处理各种数据类型
// jsonData: 要发送的JSON字符串
// 返回响应体和错误
func POSTJson(urlStr string, jsonData string) ([]byte, error) {
	// 设置请求头和超时
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// 创建请求
	req, err := http.NewRequest("POST", urlStr, strings.NewReader(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	result, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return result, fmt.Errorf("HTTP错误: %d %s", resp.StatusCode, resp.Status)
	}

	return result, nil
}

func POST(urlStr string, param map[string]interface{}) (*RespBody, error) {
	resp := &RespBody{}
	err := gout.POST(urlStr).SetHeader(gout.H{"Accept": "application/json"}).SetBody(param).Debug(false).BindJSON(resp).Do()
	if err != nil {
		fmt.Println("post error:", err.Error())
		return nil, err
	}

	return resp, nil
}

func GET(urlStr string, param map[string]interface{}) (*RespBody, error) {
	resp := &RespBody{}
	err := gout.GET(urlStr).SetHeader(gout.H{"Accept": "application/json"}).SetQuery(param).Debug(false).BindJSON(resp).Do()
	if err != nil {
		fmt.Println("get error:", err.Error())
		return nil, err
	}

	if resp.Code != 0 {
		return nil, errors.New("request failed:" + resp.Message)
	}

	return resp, nil
}
