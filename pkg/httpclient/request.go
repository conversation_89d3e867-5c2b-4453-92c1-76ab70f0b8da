package httpclient

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"time"

	"bs.com/app/pkg/xcode"
)

type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Payload interface{} `json:"payload,omitempty"`
}

func ParsePayload[T any](payload interface{}) (*T, error) {
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	var result T
	if err := json.Unmarshal(payloadBytes, &result); err != nil {
		return nil, err
	}
	return &result, nil
}

func ReqPost(apiUrl, token string, data map[string]interface{}) (*Response, error) {
	const (
		defaultTimeout = 10 * time.Second
		contentType    = "application/json"
	)

	// 1. 检查 Token 是否有效
	// if token == "" {
	// 	return nil, errors.New("token 不能为空")
	// }

	// 2. 序列化请求数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("JSON 编码失败: %w", err)
	}

	// 3. 创建 HTTP 请求
	req, err := http.NewRequest(
		http.MethodPost,
		apiUrl,
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 4. 设置请求头
	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", contentType)

	// 5. 发送请求（带超时控制）
	client := &http.Client{Timeout: defaultTimeout}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 6. 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 7. 检查 HTTP 状态码
	if resp.StatusCode >= http.StatusBadRequest {
		return nil, fmt.Errorf("HTTP 错误: 状态码=%d, 响应=%s", resp.StatusCode, string(body))
	}

	// 8. 解析 JSON 响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("JSON 解析失败: %w, 原始响应: %s", err, string(body))
	}

	// 9. 检查业务逻辑错误（如 Code != 0 表示业务错误）
	if response.Code != xcode.Success.Code() {
		return &response, fmt.Errorf("业务错误: code=%d, message=%s", response.Code, response.Message)
	}

	return &response, nil
}

func ReqGet(apiUrl, token string, params map[string]interface{}) (*Response, error) {
	const defaultTimeout = 10 * time.Second

	// 1. 检查 Token 是否有效
	if token == "" {
		return nil, errors.New("token 不能为空")
	}

	// 2. 构造完整 URL（带查询参数）
	if len(params) > 0 {
		query := url.Values{}
		for k, v := range params {
			query.Add(k, fmt.Sprintf("%v", v)) // 处理各种类型参数
		}
		apiUrl += "?" + query.Encode()
	}

	// 3. 创建 HTTP 请求
	req, err := http.NewRequest(http.MethodGet, apiUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 4. 设置请求头
	req.Header.Set("Authorization", "Bearer "+token)

	// 5. 发送请求（带超时控制）
	client := &http.Client{Timeout: defaultTimeout}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP 请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 6. 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 7. 检查 HTTP 状态码
	if resp.StatusCode >= http.StatusBadRequest {
		return nil, fmt.Errorf("HTTP 错误: 状态码=%d, 响应=%s", resp.StatusCode, string(body))
	}

	// 8. 解析 JSON 响应
	var response Response
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("JSON 解析失败: %w, 原始响应: %s", err, string(body))
	}

	// 9. 检查业务逻辑错误
	if response.Code != xcode.Success.Code() {
		return &response, fmt.Errorf("业务错误: code=%d, message=%s", response.Code, response.Message)
	}

	return &response, nil
}

// DownloadFile 下载文件并保存到指定路径
func DownloadFile(apiUrl, token, savePath string) error {
	const defaultTimeout = 10 * time.Second

	// 1. 检查 Token
	if token == "" {
		return errors.New("token 不能为空")
	}

	// 3. 创建 HTTP 请求
	req, err := http.NewRequest(http.MethodGet, apiUrl, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 4. 设置请求头
	req.Header.Set("Authorization", "Bearer "+token)

	// 5. 发送请求
	client := &http.Client{Timeout: defaultTimeout}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 6. 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body) // 尝试读取错误信息
		return fmt.Errorf("HTTP 错误: 状态码=%d, 响应=%s", resp.StatusCode, string(body))
	}

	// 7. 创建目标文件
	file, err := os.Create(savePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	// 8. 流式写入文件（避免内存爆炸）
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}
