package httpclient

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"testing"
	"time"
)

var (
	api    = "https://iot.simiot.com/api/client/v1"
	appid  = "2414966683604810"
	secret = "ej7Sd899u0vz3HxeMrvuLT0VjCt4YtQ8"
)

type Package struct {
	BusinessType            int    `json:"business_type"`
	Number                  string `json:"number"`
	Title                   string `json:"title"`
	ServicePriod            int    `json:"service_period"`
	ServicePeriodType       int    `json:"service_period_type"`
	PackageCapacity         int    `json:"package_capacity"`
	SubscribedTime          string `json:"subscribed_time"`
	StartTime               string `json:"start_time"`
	EndTime                 string `json:"end_time"`
	Periods                 int    `json:"periods"`
	PeriodList              string `json:"period_list"`
	CurrentPeriodBeginTime  string `json:"current_period_begin_time"`
	CurrentPeriodEndTime    string `json:"current_period_end_time"`
	CurrentPeriodUsage      int32  `json:"current_period_usage"`
	CurrentPeriodVoiceUsage int    `json:"current_period_voice_usage"`
}

type SimCardResult struct {
	Code             string    `json:"code"`
	Message          string    `json:"message"`
	Iccid            string    `json:"iccid"`
	Carrier          string    `json:"carrier"`
	Msisdn           string    `json:"msisdn"`
	Imsi             string    `json:"imsi"`
	ActivatedTime    string    `json:"activated_time"`
	LifeCycle        int       `json:"life_cycle"`
	NetWorkStatus    int       `json:"network_status"`
	DeviceCardStatus string    `json:"device_card_status"`
	PowerStatus      string    `json:"power_status"`
	OnlineStatus     string    `json:"online_status"`
	IsNeedRealname   int       `json:"is_need_realname"`
	RealnameStatus   int       `json:"realname_status"`
	ServiceEndTime   string    `json:"service_end_time"`
	Package          []Package `json:"package"`
}
type SimResp struct {
	Code    string          `json:"code"`
	Message string          `json:"message"`
	Result  []SimCardResult `json:"result"`
}

func signfn(param map[string]interface{}) string {
	var sign string
	// 将map转换为slice
	var keys []string
	for k, _ := range param {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	fmt.Println(keys)
	signStr := ""
	keysLen := len(keys)
	for i, key := range keys {
		value := strings.ReplaceAll(fmt.Sprintf("%v", param[key]), " ", "")
		if i >= keysLen-1 {
			signStr += fmt.Sprintf("%s=%v", key, value)
		} else {
			signStr += fmt.Sprintf("%s=%v&", key, value)
		}
	}
	signStr += secret
	encodedStr := base64.StdEncoding.EncodeToString([]byte(signStr))
	fmt.Println(encodedStr)

	// 创建一个新的SHA256哈希对象
	h := sha256.New()

	// 将字符串写入哈希对象
	h.Write([]byte(encodedStr))

	// 计算哈希值并将结果转换为16进制字符串
	sign = fmt.Sprintf("%x", h.Sum(nil))
	return sign
}

/*
返回的成功结果

	 *
	 {
	    "code":"0",
	    "message":"全部查询成功",
	    "result":[
	        {
	            "code":"0",
	            "message":"查询成功",
	            "iccid":"89860451092270162030",
	            "carrier":"china_mobile",
	            "msisdn":"1440518002025",
	            "imsi":"***************",
	            "activated_time":"2023-07-05 15:30:03",
	            "life_cycle":2,
	            "network_status":0,
	            "device_card_status":"",
	            "power_status":"1",
	            "online_status":"1",
	            "is_need_realname":1,
	            "realname_status":0,
	            "service_end_time":"2024-06-30 23:59:59",
	            "package":[
	                {
	                    "business_type":2,
	                    "number":"TY103-000489",
	                    "title":"移动-流量池-1G/月-通用",
	                    "service_period":1,
	                    "service_period_type":0,
	                    "package_capacity":1,
	                    "capacity_type":"gb",
	                    "voice_capacity":0,
	                    "subscribed_time":"2023-07-05 09:14:46",
	                    "start_time":"2023-07-05 15:29:57",
	                    "end_time":"2024-06-30 23:59:59",
	                    "periods":12,
	                    "period_list":"1/12",
	                    "current_period_begin_time":"2023-07-05 15:29:57",
	                    "current_period_end_time":"2023-07-31 23:59:59",
	                    "current_period_usage":17137,
	                    "current_period_voice_usage":0
	                }
	            ],
	            "next_package":[

	            ]
	        }
	    ]
	}
*/

func TestGetSimIOTStatus(t *testing.T) {
	fmt.Println("get sim iot status")
	uri := "/sim-card/base-info"
	url := fmt.Sprintf("%s/%s", api, uri)

	iccids := "89860451092270162030,866545053377955,"

	param := map[string]interface{}{}
	param["appid"] = appid
	param["timestamp"] = time.Now().Unix()
	param["iccids"] = iccids
	// param["status"] = 0
	param["sign"] = signfn(param)
	fmt.Println(param)
	fmt.Println(url)

	result, err := POSTSimIot(url, param)
	if err != nil {
		fmt.Println("err: ", err.Error())
	}
	fmt.Println("result:", result)
	// databyte, err := mapToByte(result.(map[string]interface{}))
	var simResp SimResp
	json.Unmarshal(result, &simResp)
	fmt.Println(simResp)

	for _, result := range simResp.Result {
		// result.Code
		fmt.Println(result.Code)
		fmt.Println(result.OnlineStatus)
	}

}
