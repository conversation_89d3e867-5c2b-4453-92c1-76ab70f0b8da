// Package httpserver implements HTTP server.
package httpserver

import (
	"context"
	"net/http"
	"time"

	"bs.com/app/pkg/xlog"
	"bs.com/app/pkg/xwg"
)

const (
	_defaultReadTimeout     = 5 * time.Second
	_defaultWriteTimeout    = 60 * time.Second
	_defaultAddr            = ":80"
	_defaultShutdownTimeout = 3 * time.Second
)

// Server
type Server struct {
	name            string
	server          *http.Server
	shutdownTimeout time.Duration
}

// NewServer
func NewServer(name string, handler http.Handler, opts ...Option) xwg.IService {
	httpServer := &http.Server{
		Handler:      handler,
		ReadTimeout:  _defaultReadTimeout,
		WriteTimeout: _defaultWriteTimeout,
		Addr:         _defaultAddr,
	}

	s := &Server{
		name:            name,
		server:          httpServer,
		shutdownTimeout: _defaultShutdownTimeout,
	}

	// Custom options
	for _, opt := range opts {
		opt(s)
	}

	return s
}

// http server 无法监听 ctx的done，所以只能主动关闭
func (s *Server) Run(ctx context.Context) error {
	xlog.Info("start http gin server", "name", s.name)
	//http server 开启之后进入业务循环
	go func() {
		xlog.Info("start http server", "addr", s.server.Addr)
		err := s.server.ListenAndServe()
		if err != nil {
			if err == http.ErrServerClosed {
				xlog.Info("http server stoped")
			} else {
				xlog.Error("http server failed", "err", err)
			}
		}
	}()

	//正常启动，则阻塞等待被关闭
	<-ctx.Done()
	xlog.Info("http server shutdown")

	// 创建新的带超时的 context 用于优雅关闭
	shutdownCtx, cancel := context.WithTimeout(context.Background(), s.shutdownTimeout)
	defer cancel()

	_ = s.server.Shutdown(shutdownCtx)
	return nil
}

func (s *Server) Name() string {
	return s.name
}
