package now

import (
	"strings"
	"time"
)

func formatTimeToList(t time.Time) []int {
	hour, min, sec := t.Clock()
	year, month, day := t.Date()
	return []int{t.Nanosecond(), sec, min, hour, day, int(month), year}
}

func Location() *time.Location {
	//不能确保服务器本地环境的时区是否正确设置
	//loc, _ := time.LoadLocation("Local")
	//return time.FixedZone("CST", 3600*8)
	return time.Local
}

// 格式：yyyy-mm-dd-hh
// local time to UTC timestamp
func TimeLocalToUTC(tstr string) (int64, error) {
	layout := "2006-01-02-15"
	t, err := time.ParseInLocation(layout, tstr, Location())
	if err != nil {
		return 0, err
	}

	return t.Unix(), nil
}

// 格式：yyyy-mm-dd
// local time to UTC timestamp
func TimeLocalToUTC2(tstr string) (int64, error) {
	layout := "2006-01-02"
	t, err := time.ParseInLocation(layout, tstr, Location())
	if err != nil {
		return 0, err
	}

	return t.Unix(), nil
}

// utc to  yyyy-mm-dd hh:mm:ss
func TimeUTCtoLocalFull(ts int64) (str string) {
	tz := Location()
	t := time.Unix(ts, 0).In(tz)

	layout := "2006-01-02 15:04:05"

	return t.Format(layout)
}

func TimeNowToLocalFull() (str string) {
	tz := Location()
	layout := "2006-01-02 15:04:05"
	return time.Now().In(tz).Format(layout)
}

func TimeLocalFullToUTC(str string) (int64, error) {
	layout := "2006-01-02 15:04:05"
	t, err := time.ParseInLocation(layout, str, Location())
	if err != nil {
		return 0, err
	}
	return t.Unix(), nil
}

// 格式化UTC timestamp to local time
// 格式 yyyy mmddhh, 仅用于数据整编
func TimeUTCtoLocal(ts int64) (y, mmddhh string) {
	tz := Location()
	t := time.Unix(ts, 0).In(tz)

	layout := "2006 010215"

	str := t.Format(layout) + ".00"
	arr := strings.Split(str, " ")
	return arr[0], arr[1]
}

// utc to local
// 格式 yyyy-mm-dd-hh
func TimeUTCtoLocal2(ts int64) (date string) {
	tz := Location()
	//tz, _ := time.LoadLocation("UTC")
	t := time.Unix(ts, 0).In(tz)
	layout := "2006-01-02-15"
	return t.Format(layout)
}

// utc to local
// 格式 yyyy-mm-dd hh:mm:ss
func TimeUTCtoLocal3(ts int64) string {
	tz := Location()
	t := time.Unix(ts, 0).In(tz)
	layout := "2006-01-02 15:04:05"
	return t.Format(layout)
}

// utc to local
// 格式 yyyy-mm-dd hh-mm-ss
func TimeUTCtoLocal4(ts int64) (date string, hms string) {
	tz := Location()
	t := time.Unix(ts, 0).In(tz)
	layout := "2006-01-02 15-04-05"
	tStr := t.Format(layout)
	arr := strings.Split(tStr, " ")
	return arr[0], arr[1]
}

// 取时间戳的整小时
func TsRoundHour(ts int64) int64 {
	tm := time.Unix(ts, 0)
	return ts - int64(60*tm.Minute()) - int64(tm.Second())
}

func TsFloorHour(ts int64) int64 {
	tm := time.Unix(ts, 0)
	return ts - int64(60*tm.Minute()) - int64(tm.Second())
}

// 去整 5 分钟
func TsFloor5m(ts int64) int64 {
	tm := time.Unix(ts, 0)
	diff := tm.Minute() % 5
	return ts - int64(diff*int(time.Minute)) - int64(tm.Second())
}

func TsRoundDay1(ts int64) int64 {
	tm := time.Unix(ts, 0)
	y, m, d := tm.Date()
	return time.Date(y, m, d, 0, 0, 0, 0, time.UTC).Unix()
}

func TsRoundDay2(ts int64) int64 {
	tm := time.Unix(ts, 0)
	return ts - int64(3600*tm.Hour()) - int64(60*tm.Minute()) - int64(tm.Second())
}

func MakeDate(y, m, d, hh, mm, ss int) time.Time {
	return time.Date(y, time.Month(m), d, hh, mm, ss, 0, time.Local)
	// return time.Date(y, time.Month(m), d, hh, mm, ss, 0, time.UTC)
}

// 获取最近7天日期, 返回字符串格式
func MakeDayList(n int) []time.Time {
	days := []time.Time{}
	tnow := time.Now()
	for i := n - 1; i >= 0; i-- {
		nday := tnow.AddDate(0, 0, -i)
		days = append(days, nday)
	}
	return days
}

//============================================================================================
//注意时间步长都是默认 5分钟

// ts : 观测最末时间
// n ： 采样数据组数
// 湖南水文监测数据通信规约说明:发报时间与观测时间相同，观测时间为观测最末时间，报文 中五分钟数据为前一小时起的五分钟数据。)
// 通过小时报观测最末时间，计算出来每一个 5 分钟时段的数据的时间
// 如果是 11:00送上来的小时报，数据是 10:00 ~ 11:00的，则第一个 5 分钟时段数据的时间是 10:05
func EndTime5minute(ts int64, n int) int64 {
	ts = ts - int64(n*300)  //取观测最初时间，默认5分钟一次采样
	start := ts + 150 + 300 //间隔5分钟，四舍五入，然后+300 是为了取整五分钟末
	return start - start%300
}

// ts : 观测最初时间
func StartTime5minute(ts int64) int64 {
	start := ts + 150 //间隔5分钟，增加2.5分钟，以便五分钟取整
	return start - start%300
}
