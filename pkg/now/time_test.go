package now

import (
	"fmt"
	"testing"
	"time"
)

func TestTimeUTCtoLocal(t *testing.T) {
	timelocal := time.FixedZone("CST", 3600*8)
	//layout := "2006-01-02 15:04:05"
	layout := "2006 010215"

	ts := time.Now().Unix()
	str1 := time.Now().In(timelocal).Format(layout)
	str2 := time.Now().Format(layout) //使用系统默认时区
	fmt.Println(str1)
	fmt.Println(str2)

	t.Run("test1", func(t *testing.T) {
		gotY, gotMmddhh := TimeUTCtoLocal(ts)
		fmt.Println(gotY, gotMmddhh)
	})

	t.Run("test2", func(t *testing.T) {
		gotYMmddhh := TimeUTCtoLocal2(ts)
		fmt.Println(gotYMmddhh)
	})
}
