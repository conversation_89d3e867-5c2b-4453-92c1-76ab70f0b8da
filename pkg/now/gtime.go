package now

import (
	"fmt"
	"time"
)

var (
	timeLayout     = "2006-01-02 15:04:05"
	hourTimeLayout = "2006-01-02 15"
	dayTimeLayout  = "2006-01-02"
	location       = "Asia/Shanghai"
	// notSpaceLayout = "2006-01-02_15-04-05"
	notSpaceLayout = "2006-01-02-150405"
)

func GetTimeByTemplate(timeLayout string) string {
	return time.Now().Format(timeLayout)
}

func parseWithLocation(name string, timeStr string) (time.Time, error) {
	locationName := name
	if l, err := time.LoadLocation(locationName); err != nil {
		println(err.Error())
		return time.Time{}, err
	} else {
		lt, _ := time.ParseInLocation(timeLayout, timeStr, l)
		// fmt.Println(locationName, lt)
		return lt, nil
	}
}

func GetTimeNow() int64 {
	//return time.Now().Format("2006-01-02 15:04:05")
	return time.Now().Unix()
}

func GetTimeStr() string {
	return time.Now().Format(timeLayout)
}

func GetTimeNotSpaceStr() string {
	return time.Now().Format(notSpaceLayout)
}

func GetTime() time.Time {
	return time.Now()
}

/*
 * time to str
 */
func TimeToDayStr(ttime time.Time) string {
	return ttime.Format(dayTimeLayout)
}

func Time2Str(ttime time.Time) string {
	return ttime.Format(timeLayout)
}

// 检查timePoint 是否是 默认值
func CheckTimeIsNil(timePoint time.Time) bool {
	notTime := "0001-01-01 00:00:00 +0000 UTC"
	if fmt.Sprintf("%v", timePoint) != notTime {
		return false
	}
	return true
}

// 字符串转时间
func StrToTime(timeStr string) time.Time {
	local, _ := time.LoadLocation("Local")
	t, _ := time.ParseInLocation(timeLayout, timeStr, local)
	return t
}

// 整点时间字符串转为时间
func HourStrToTime(timeStr string) time.Time {
	local, _ := time.LoadLocation("Local")
	t, _ := time.ParseInLocation(hourTimeLayout, timeStr, local)
	return t
}

// 获取N分钟以前的时间
func GetMinuteAgo(n int64) time.Time {
	m, _ := time.ParseDuration(fmt.Sprintf("-%vm", n))
	return time.Now().Add(m)
}

// 获取N分钟以后
func GetMinuteAfter(tnow time.Time, n int64) time.Time {
	m, _ := time.ParseDuration(fmt.Sprintf("%vm", n))
	return tnow.Add(m)
}

// int64 转为时间time.Time 类型
func Int64ToTime(n int64) time.Time {
	nstr := fmt.Sprintf("%v", n)
	if len(nstr) == 10 {
		now := time.Unix(n, 0)
		return now
	} else {
		now := time.Unix(n/1000, n%1000)
		return now
	}
}

// 获取整点时间戳
func HourTimestamp(timeNow int64) int64 {
	now := time.Unix(timeNow, 0)
	timestamp := now.Unix() - int64(now.Second()) - int64(60*now.Minute())
	// fmt.Println(timestamp, time.Unix(timestamp, 0), now.Unix())
	return timestamp
}

/*
 * 获取n天以前
 */
func GetDaysAgo(currentTime time.Time, n int32) time.Time {
	nday := n * -1
	return currentTime.AddDate(0, 0, int(nday))
}

/*
*
获取N小时以前的时间
*/
func GetHoursAgo(currentTime time.Time, n int32) time.Time {
	h, _ := time.ParseDuration(fmt.Sprintf("-%vh", n))
	return currentTime.Add(h)
}

// 获取N分钟以前的时间
func GetTimeMinuteAgo(currentTime time.Time, n int64) time.Time {
	m, _ := time.ParseDuration(fmt.Sprintf("-%vm", n))
	return currentTime.Add(m)
}

/*
*
获取N小时以后的时间
*/
func GetHoursAfter(currentTime time.Time, n int32) time.Time {
	h, _ := time.ParseDuration(fmt.Sprintf("%vh", n))
	return currentTime.Add(h)
}

/*
*
字符串转时间2
*/
func StrToTime2(timeStr string, layout string) time.Time {
	local, _ := time.LoadLocation("Local")
	t, _ := time.ParseInLocation(layout, timeStr, local)
	return t
}

/*
获取N时段以后的时间
*/
func GetTimeAfter(currentTime time.Time, stype int32) time.Time {
	switch stype {
	case 24: //日
		return currentTime.AddDate(0, 0, 1)
	case 24 * 30: //月
		return currentTime.AddDate(0, 1, 0)
	case 365 * 24:
		return currentTime.AddDate(1, 0, 0)
	case 365 * 2: //2年后
		return currentTime.AddDate(2, 0, 0)
	default:
		h, _ := time.ParseDuration(fmt.Sprintf("%vh", stype))
		return currentTime.Add(h)
	}
}

/*
获取N时段以前的时间
*/
func GetTimeBefor(currentTime time.Time, stype int32, n int) time.Time {
	switch stype {
	case 24: //日
		return currentTime.AddDate(0, 0, -1*n)
	case 24 * 30: //月
		return currentTime.AddDate(0, -1*n, 0)
	case 365 * 24: // 年
		return currentTime.AddDate(-1*n, 0, 0)
	default:
		h, _ := time.ParseDuration(fmt.Sprintf("-%vh", stype))
		return currentTime.Add(h)
	}
}

/*
 * 获取整点时间
 */
func GetDateHour(t time.Time) string {
	y, m, d := t.Date()
	return fmt.Sprintf("%d-%d-%d-%d", y, m, d, t.Hour())
}

/*
 * 获取某段时间的整点
 */
func TimeHourRange(start, end int64) (timeRange []string) {
	for start < end {
		timeStr := GetDateHour(Int64ToTime(HourTimestamp(start)))
		start += 3600
		timeRange = append(timeRange, timeStr)
	}
	return
}

/*
 * 获取当天8点时间
 */
func Get8HourTime() time.Time {
	timeStr := time.Now().Format(dayTimeLayout)
	local, _ := time.LoadLocation("Local")
	t, _ := time.ParseInLocation(dayTimeLayout, timeStr, local)
	curDay8Housr := GetHoursAfter(t, 8)
	return curDay8Housr
}

/*
*
获取某一天的0点时间
*/
func GetZeroTime(d time.Time) time.Time {
	return time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, d.Location())
}
