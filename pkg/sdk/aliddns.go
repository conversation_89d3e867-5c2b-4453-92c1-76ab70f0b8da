package sdk

import (
	"bs.com/app/pkg/xlog"
	"github.com/aliyun/alibaba-cloud-sdk-go/services/alidns"
)

var (
	accessKeyId  string
	accessSecret string
)

func DNSCheckAndUpdate(key, secret string, dName string, dstRR, dstValue string) error {
	var client *alidns.Client
	var err error

	accessKeyId, accessSecret = key, secret
	client, err = alidns.NewClientWithAccessKey("cn-hangzhou", accessKeyId, accessSecret)

	request := alidns.CreateDescribeDomainRecordsRequest()
	request.Scheme = "https"
	request.Type = "A"
	request.DomainName = dName

	response, err := client.DescribeDomainRecords(request)
	if err != nil {
		return err
	}

	isExist := false
	var rID string
	for _, one := range response.DomainRecords.Record {
		if one.RR == dstRR {
			if one.Value == dstValue {
				//xlog.Info("ddns not change")
				return nil
			}
			isExist = true
			rID = one.RecordId
		}
	}
	if isExist {
		return update(rID, dstRR, dstValue)
	} else {
		return add(dstRR, dstValue)
	}
}

func update(recordID, rr, value string) error {
	xlog.Info("ddns do update, new value : ", value)

	client, err := alidns.NewClientWithAccessKey("cn-hangzhou", accessKeyId, accessSecret)

	request := alidns.CreateUpdateDomainRecordRequest()
	request.Scheme = "https"
	request.Type = "A"
	request.RecordId = recordID
	request.RR = rr
	request.Value = value

	_, err = client.UpdateDomainRecord(request)
	if err != nil {
		return err
	}
	return nil
}

func add(rr string, ip string) error {
	xlog.Info("ddns add new: ", rr, ip)

	client, err := alidns.NewClientWithAccessKey("cn-hangzhou", accessKeyId, accessSecret)
	request := alidns.CreateAddDomainRecordRequest()
	request.Scheme = "https"
	request.Type = "A"
	request.RR = rr
	request.Value = ip
	request.DomainName = "cocotech.top"

	_, err = client.AddDomainRecord(request)
	if err != nil {
		return err
	}

	return nil
}
