package sdk

import (
	"time"

	"bs.com/app/pkg/sdk/sms"
	"bs.com/app/pkg/xlog"
)

// ali sms
type AliSMSConfig struct {
	AccessKeyID      string `yaml:"accessKeyID"`
	AccessKeySecret  string `yaml:"accessKeySecret"`
	SignName         string `yaml:"signName"`
	TempCodeRegister string `yaml:"tempCodeRegister"`
	TempCodeResetPwd string `yaml:"tempCodeResetPwd"`
	TempCodeDefault  string `yaml:"tempCodeDefault"`
}

// AccessKeyID   LTAI5tQf1KnRZELRYtuEHPDE
// AccessKeySecret   ******************************

var smsConf = &AliSMSConfig{
	AccessKeyID:      "LTAIKeUjF0a6LJR6",
	AccessKeySecret:  "******************************",
	SignName:         "椰子壳",
	TempCodeRegister: "SMS_130919878",
	TempCodeResetPwd: "SMS_130919878",
	TempCodeDefault:  "SMS_130919878",
}

func init() {
	sms.GetInstance(smsConf.AccessKeyID, smsConf.AccessKeySecret, smsConf.SignName)
}

// 测试发送验证码
func SendSMSRegisterVerifyCode(phone, code string) error {
	return sendVerifyCode(phone, code, smsConf.TempCodeRegister)
}
func SendSMSResetPasswordVerifyCode(phone, code string) error {
	return sendVerifyCode(phone, code, smsConf.TempCodeResetPwd)
}

func SendSMSDefaultVerifyCode(phone, code string) error {
	return sendVerifyCode(phone, code, smsConf.TempCodeDefault)
}

func sendVerifyCode(phone, code, tempCode string) error {
	start := time.Now()
	err := sms.SendVerifyCode(phone, code, tempCode)
	if err != nil {
		xlog.Info("send verify code failed: ", err.Error())
		return err
	}
	xlog.Info("verify code : ", code)
	xlog.Info("send successful, time : ", time.Now().Sub(start).Seconds())
	return nil
}
