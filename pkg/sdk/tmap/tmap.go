package tmap

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"bs.com/app/pkg/xlog"
)

/*
简介
天地图逆地理服务API是一类简单的HTTP/HTTPS接口，提供将坐标点（经纬度）转换为结构化的地址信息的功能。
使用逆地理编码服务前您需要申请Key。
请求接口
参数值	参数说明	参数类型	是否必备	备注（值域）
lon	坐标的x值	string	是
lat	坐标的y值	string	是
appkey	网站的唯一编码	string	是
ver	接口版本	string	是
逆地理编码响应示例：
请求：http://api.tianditu.gov.cn/geocoder?postStr={'lon':116.37304,'lat':39.92594,'ver':1}&type=geocode&tk=您的密钥
返回：
{
    "result": {
        "formatted_address": "北京市西城区西什库大街31号院23东方开元信息科技公司",
        "location": {
            "lon": 116.37304,
            "lat": 39.92594
        },
        "addressComponent": {
            "address": "西什库大街31号院23",
            "city": "北京市西城区",
            "road": "大红罗厂街",
            "poi_position": "东北",
            "address_position": "东北",
            "road_distance": 49,
            "poi": "东方开元信息科技公司",
            "poi_distance": "38",
            "address_distance": 38
        }
    },
    "msg": "ok",
    "status": "0"
}
*/

type TmapAddress struct {
	Province        string `json:"province"` // 省
	ProvinceCode    int64  `json:"province_code"`
	City            string `json:"city"` // 市
	CityCode        int64  `json:"city_code"`
	County          string `json:"county"` // 区
	CountyCode      int64  `json:"county_code"`
	Town            string `json:"town"` // 乡镇
	TownCode        int64  `json:"town_code"`
	Road            string `json:"road"`             // 区、县或者县级市
	RoadDistance    int    `json:"township"`         // 乡镇
	PoiDistance     int    `json:"poi_distance"`     // 距离此点最近poi点的距离
	Poi             string `json:"towncode"`         // 距离此点最近poi点
	PoiPosition     string `json:"poi_position"`     // 乡镇
	Address         string `json:"address"`          // 此点最近地点信息
	AddressDistance int    `json:"address_distance"` // 此点距离最近地点信息距离
	AddressPosition string `json:"address_position"` // 此点在最近地点信息方向
}

type tmapStrAddress struct {
	Province        string `json:"province"` // 省
	ProvinceCode    string `json:"province_code"`
	City            string `json:"city"` // 市
	CityCode        string `json:"city_code"`
	County          string `json:"county"` // 区
	CountyCode      string `json:"county_code"`
	Town            string `json:"town"` // 乡镇
	TownCode        string `json:"town_code"`
	Road            string `json:"road"`             // 区、县或者县级市
	RoadDistance    int    `json:"township"`         // 乡镇
	PoiDistance     int    `json:"poi_distance"`     // 距离此点最近poi点的距离
	Poi             string `json:"towncode"`         // 距离此点最近poi点
	PoiPosition     string `json:"poi_position"`     // 乡镇
	Address         string `json:"address"`          // 此点最近地点信息
	AddressDistance int    `json:"address_distance"` // 此点距离最近地点信息距离
	AddressPosition string `json:"address_position"` // 此点在最近地点信息方向
}

func stringToInt64(areaCode string) (int64, error) {
	//  返回的区域id以156开头，而实际上需要的是后面的
	if len(areaCode) < 3 {
		return 0, nil // fmt.Errorf("area code error: code %s len %d < 3", areaCode, len(areaCode))
	}
	codeStr := areaCode[3:]
	// 使用 ParseInt 函数，第二个参数是基数（通常是10），第三个参数是指定的位数。
	i, err := strconv.ParseInt(codeStr, 10, 64)
	if err != nil {
		return 0, err // 返回错误信息
	}
	return i, nil
}

/*
 * 返回结果的code是字符串，而我们需要的是int64的
 */
func strAddressToIntAddress(strTmap *tmapStrAddress) (intTmap *TmapAddress, err error) {
	proCode, err := stringToInt64(strTmap.ProvinceCode)
	if err != nil {
		xlog.Debug("province code error")
		return nil, err
	}
	cityCode, err := stringToInt64(strTmap.CityCode)
	if err != nil {
		xlog.Debug("city  code error")
		return nil, err
	}
	counTyCode, err := stringToInt64(strTmap.CountyCode)
	if err != nil {
		xlog.Debug("countyCode code error")
		return nil, err
	}
	townCode, err := stringToInt64(strTmap.TownCode)
	if err != nil {
		xlog.Debug("town code error")
		return nil, err
	}
	intTmap = &TmapAddress{
		Province:        strTmap.Province,
		ProvinceCode:    proCode,
		City:            strTmap.City,
		CityCode:        cityCode,
		County:          strTmap.County,
		CountyCode:      counTyCode,
		Town:            strTmap.Town,
		TownCode:        townCode,
		Road:            strTmap.Road,
		RoadDistance:    strTmap.RoadDistance,
		PoiDistance:     strTmap.PoiDistance,
		Poi:             strTmap.Poi,
		PoiPosition:     strTmap.PoiPosition,
		Address:         strTmap.Address,
		AddressDistance: strTmap.AddressDistance,
		AddressPosition: strTmap.AddressPosition,
	}
	return intTmap, nil
}

func (addr *TmapAddress) ToAddress() string {
	if addr.City == "" {
		return addr.Province + addr.County + addr.Town
	}
	return addr.City + addr.County + addr.Town
}

// 天地图apikey: b216d7c2423da4d62aa807c2a4a96382
// 这个可以在天地图后台更换: https://console.tianditu.gov.cn/api/key
const apiKey = "27fb08079db7eeb117fe9fc1abbc61b5"

type respAmapAddr struct {
	Status string `json:"status"` //String（0：正确，1：错误，404：出错。）
	Msg    string `json:"msg"`
	Result struct {
		FormattedAddress string         `json:"formatted_address"`
		AddressComponent tmapStrAddress `json:"addressComponent"`
		Location         struct {
			Lon float64 `json:"lon"`
			Lat float64 `json:"lat"`
		} `json:"location"`
	} `json:"result"`
}

// 经纬度转换为地址
// AmapGPSToAddress 逆地址解析  通过经纬度解析地址。
// 注意：这里是地址，不是行政编码
func CoordinatesToAddress(lat, lng string) (addr *TmapAddress, err error) {
	//逆地理编码API服务地址
	//GET
	// http: //api.tianditu.gov.cn/geocoder?postStr={'lon':116.37304,'lat':39.92594,'ver':1}&type=geocode&tk=您的密钥

	// 解析地址，经度在前，纬度在后，经纬度间以“,”分割
	urlStr := fmt.Sprintf("http://api.tianditu.gov.cn/geocoder?postStr={'lon':%s,'lat':%s,'ver':1}&type=geocode&tk=%s", lng, lat, apiKey)
	xlog.Debug("tmap, CoordinatesToAddress url : " + urlStr)

	req, err := http.NewRequest("GET", urlStr, nil)
	if err != nil {
		xlog.Error("http new request failed", "err", err.Error())
		return
	}
	// 天地图限制了只能浏览器访问
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	// req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3")

	client := &http.Client{}
	Resp, err := client.Do(req)
	if err != nil {
		xlog.Error("http do request failed", "err", err.Error(), "url", urlStr)
		return
	}

	defer Resp.Body.Close()

	bodyBuf, err := io.ReadAll(Resp.Body)
	if err != nil {
		xlog.Error("http read body failed", "err", err.Error())
		return
	}

	//为空的时候，返回值是[]
	var resp = &respAmapAddr{}
	err = json.Unmarshal(bodyBuf, resp)
	if err != nil {
		xlog.Error("unmarshal failed", "err", err.Error(), "body", string(bodyBuf))
		return
	}
	if resp.Status != "0" {
		err = errors.New("somethings wrong: " + resp.Msg)
		return
	}
	strAddr := resp.Result.AddressComponent
	// xlog.Debug("addr:", strAddr)
	addr, err = strAddressToIntAddress(&strAddr)
	return
}
