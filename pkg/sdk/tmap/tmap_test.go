package tmap

import (
	"fmt"
	"testing"
)

// msg="url : http://api.tianditu.gov.cn/geocoder?postStr={'lon':111.407382,'lat':27.249318,'ver':1}&type=geocode&tk=b216d7c2423da4d62aa807c2a4a96382" service=ebox
/*
body :
{"result":{"formatted_address":"湖南省邵阳市北塔区新滩镇街道南岗冲东北约142米",
"location":{"lon":111.407382,"lat":27.249318},
"addressComponent":{"address":"南岗冲","town":"新滩镇街道","nation":"中国","city":"邵阳市","county_code":"156430511","poi_position":"东北","county":"北塔区",
"city_code":"156430500","address_position":"东北","poi":"南岗冲","province_code":"156430000","town_code":"156430511001","province":"湖南省","road":"X097",
"road_distance":935,"address_distance":142,"poi_distance":142}},"msg":"ok","status":"0"}
*/

func TestCoordinatesToAddress(t *testing.T) {
	type args struct {
		lat   string
		lng   string
		isGPS bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				lat: "39.990475",
				lng: "116.481499",
			},
			wantErr: false,
		},
		{
			name: "test2",
			args: args{
				lat: "27.249318",
				lng: "111.407382",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotAddr, err := CoordinatesToAddress(tt.args.lat, tt.args.lng)
			if (err != nil) != tt.wantErr {
				t.Errorf("error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println("got addr =", gotAddr)
			t.Logf("gotAddr = %v", gotAddr)
		})
	}
}
