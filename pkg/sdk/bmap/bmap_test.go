package bmap

import (
	"strings"
	"testing"

	"bs.com/app/pkg/xlog"
)

var positions = []Coordinate{
	{"111.407382", "27.249318"},
	{"111.41694", "27.255356"},
	{"111.407311", "27.247937"},
	{"111.391285", "27.239136"},
	{"111.405334", "27.255066"},
}

var p1 = []Coordinate{
	{"111.413096", "26.913511"},
}

// 定位相关
type Coordinate struct {
	Lng string `json:"lng"` // 经度
	Lat string `json:"lat"` // 维度
}

func TestLocation(t *testing.T) {
	var err error
	var addr *geoAddress
	var gps *GPS
	for _, pos := range p1 {
		addr, gps, err = BMapGetGeoAddress(pos.Lat, pos.Lng)

		if err != nil {
			t.Error("err :", err.<PERSON><PERSON><PERSON>())
			return
		}
		xlog.Info("addr:", addr)
		xlog.Info("gps:", gps)
		xlog.Info("out : ", strings.Join([]string{addr.Province, addr.City, addr.District}, "-"))
	}
}
