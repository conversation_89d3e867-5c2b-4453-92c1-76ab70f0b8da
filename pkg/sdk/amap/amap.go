package amap

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"

	"bs.com/app/pkg/xlog"
)

// 高德地图地址解析 API
// const AmapKey = "4f6478e6c9098a1ff45faae151025124"
const AmapKey = "38bae5c171650cf757bfd035a81474d1"

/*
统计上使用的县以下行政区划代码编制规则

http://www.mca.gov.cn/article/sj/fgzd/200707/200707150010089.shtml

县以下行政区划代码共有12位数字，分为三段。
代码的第一段为6位数字，表示县及县以上的行政区划；
第二段为3位数字，表示街道、镇和乡；
第三段为3位数字，表示居民委员会和村民委员会。

其具体格式为：第一段 第二段 第三段 □□□□□□------□□□------□□□

第一段的6位代码统一使用《中华人民共和国行政区划代码》（GB2260）国家标准。

第二段的3位代码按照国家标准GB10114--88《县以下行政区划代码编码规则》编制。
其中的第一位数字为类别标识，以“0”表示街道，“1”表示镇，“2和3”表示乡，“4和5”表示政企合一的单位；
其中的第二、三位数字为该代码段中各行政区划的顺序号。
具体划分如下：
1、001—099 表示街道的代码，应在本地区的范围内由小到大顺序编写；
2、100--199 表示镇的代码，应在本地区的范围内由小到大顺序编写；
3、200--399 表示乡的代码，应在本地区的范围内由小到大顺序编写；
4、400—599 表示政企合一单位的代码，应在本地区的范围内由小到大顺序编写。

第三段的3位代码为居民委员会和村民委员会的代码，用3位顺序码表示，
具体编码方法如下：
居民委员会的代码从001—199由小到大顺序编写；
村民委员会的代码从200—399由小到大顺序编写。

https://lbs.amap.com/api/webservice/guide/api/georegeo
逆地理编码API服务的返回值

{
    "status": "1",
    "regeocode":
    {
        "addressComponent":
        {
            "city": "邵阳市",
            "province": "湖南省",
            "adcode": "430521",
            "district": "邵东市",
            "towncode": "************",
            "streetNumber":
            {
                "number": [],
                "direction": [],
                "distance": [],
                "street": []
            },
            "country": "中国",
            "township": "双凤乡",
            "businessAreas": [
                []
            ],
            "building":
            {
                "name": [],
                "type": []
            },
            "neighborhood":
            {
                "name": [],
                "type": []
            },
            "citycode": "0739"
        },
        "formatted_address": "湖南省邵阳市邵东市双凤乡青龙风"
    },
    "info": "OK",
    "infocode": "10000"
}
*/

/*
{
    "status": "1",
    "info": "ok",
    "infocode": "10000",
    "locations": "116.487585177952,39.991754014757;116.487585177952,39.991653917101"
}
*/

type amapAddress struct {
	Country  string `json:"country"`        // 国家
	Province string `json:"province"`       // 省
	City     string `json:"city,omitempty"` // 城市。当城市是省直辖县时返回为空
	District string `json:"district"`       // 区、县或者县级市
	Town     string `json:"town"`           // 乡镇
	Adcode   string `json:"adcode"`         // 行政区划码
	Township string `json:"township"`       // 乡镇
	Towncode string `json:"towncode"`       // 乡镇编码，仅高德地图有此项
}

func (addr *amapAddress) ToAddress() string {
	if addr.City == "" {
		return addr.Province + addr.District + addr.Town
	}
	return addr.City + addr.District + addr.Town
}

type RespAmapAddr struct {
	Status    string `json:"status"`
	Info      string `json:"info"`
	Infocode  string `json:"infocode"`
	Regeocode struct {
		FormattedAddress string      `json:"formatted_address"`
		AddressComponent amapAddress `json:"addressComponent"`
	} `json:"regeocode"`
}

// 经纬度转换为地址
// AmapGPSToAddress 逆地址解析  通过经纬度解析地址，并返回高德坐标系的坐标
func CoordinatesToAddress(lat, lng string) (addr, areaCode string, err error) {
	var location string
	//默认是高德坐标
	isGPS := false
	if isGPS {
		//如果是GPS 坐标，需要先转换为高德坐标
		lat, lng, err = gpsConvert(lat, lng)
		if err != nil {
			xlog.Error("parse gps to amap failed:", err.Error())
			return
		}
	}

	// 高德坐标
	// 经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过6位
	location = fmt.Sprintf("%v,%v", lng, lat)

	//逆地理编码API服务地址
	//GET
	const regeoUrl = "https://restapi.amap.com/v3/geocode/regeo"

	// 解析地址，经度在前，纬度在后，经纬度间以“,”分割
	urlStr := fmt.Sprintf("%s?key=%s&location=%s", regeoUrl, AmapKey, location)
	xlog.Debug("url : " + urlStr)

	Resp, err := http.Get(urlStr)
	if err != nil {
		return
	}
	defer Resp.Body.Close()

	bodyBuf, err := io.ReadAll(Resp.Body)
	if err != nil {
		return
	}

	//为空的时候，返回值是[]
	//bodyStr := string(bodyBuf)
	bodyStr := strings.Replace(string(bodyBuf), "[]", "\"\"", -1)
	//fmt.Println("body : ", string(bodyStr))

	var resp = &RespAmapAddr{}
	err = json.Unmarshal([]byte(bodyStr), resp)
	if err != nil {
		log.Println("unmarshal err: ", err.Error())
		log.Println("body:", string(bodyBuf))
		return
	}
	if resp.Status != "1" {
		err = errors.New("somethings wrong")
		return
	}
	addr = resp.Regeocode.AddressComponent.ToAddress()
	areaCode = resp.Regeocode.AddressComponent.Adcode
	return
}

//坐标转换：将用户输入的非高德坐标（GPS坐标、mapbar坐标、baidu坐标）转换成高德坐标。
/*
{
    "status": "1",
    "info": "ok",
    "infocode": "10000",
    "locations": "116.487585177952,39.991754014757;116.487585177952,39.991653917101"
}
*/

// AmapGpsConvert gps坐标转换为高德坐标
func gpsConvert(lat, lng string) (lat2, lng2 string, err error) {

	type apiResponse struct {
		Status    string `json:"status"`
		Info      string `json:"info"`
		InfoCode  string `json:"infocode"`
		Locations string `json:"locations"`
	}

	//坐标转换API服务地址
	//GET
	const gpsConvertUrl = "https://restapi.amap.com/v3/assistant/coordinate/convert"
	//locations 经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过6位。
	urlVal := fmt.Sprintf("%s?key=%s&locations=%s,%s&coordsys=gps&output=json", gpsConvertUrl, AmapKey, lng, lat)
	buf, err := doHttpGet(urlVal)
	if err != nil {
		return
	}

	rsp := &apiResponse{}
	err = json.Unmarshal(buf, &rsp)
	if err != nil {
		return
	}
	if rsp.Status != "1" {
		err = fmt.Errorf("%s:%s", rsp.InfoCode, rsp.Info)
		return
	}

	xlog.Debug("location : " + rsp.Locations)
	arr := strings.Split(rsp.Locations, ",")
	lng2, lat2 = arr[0], arr[1]
	return
}
