package amap

import (
	"fmt"
	"testing"
)

func TestAmapGPSToAddress(t *testing.T) {
	type args struct {
		lat   string
		lng   string
		isGPS bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				lat: "39.990475",
				lng: "116.481499",
			},
			wantErr: false,
		},
		{
			name: "test2",
			args: args{
				lat: "27.249318",
				lng: "111.407382",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotAddr, acode, err := CoordinatesToAddress(tt.args.lat, tt.args.lng)
			if (err != nil) != tt.wantErr {
				t.Errorf("AmapGPSToAddress() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			fmt.Printf("AmapGPSToAddress() gotAddr = %v, acode = %v\n", gotAddr, acode)
		})
	}
}

func TestAmapGpsConvert(t *testing.T) {
	type args struct {
		lat string
		lng string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				lat: "39.990475",
				lng: "116.481499",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotLat2, gotLng2, err := gpsConvert(tt.args.lat, tt.args.lng)
			if (err != nil) != tt.wantErr {
				t.Errorf("AmapGpsConvert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("AmapGpsConvert() gotLng2 = %v", gotLng2)
			t.Logf("AmapGpsConvert() gotLat2 = %v", gotLat2)
		})
	}
}
