package sms

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"net/url"
	"sort"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"
)

const (
	timeFormat = "2006-01-02T15:04:05Z"
)

func specialEncode(str string) string {
	result := url.QueryEscape(str)
	result = strings.Replace(result, "+", "20%", -1)
	result = strings.Replace(result, "*", "%2A", -1)
	result = strings.Replace(result, "%7E", "~", -1)
	return result
}

func getBaseQuery(params map[string]string) string {
	keys := make([]string, 0)
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var buf bytes.Buffer
	for _, k := range keys {
		buf.WriteString("&")
		buf.WriteString(specialEncode(k))
		buf.WriteString("=")
		buf.WriteString(specialEncode(params[k]))
	}
	result := buf.String()
	return strings.Replace(result, "&", "", 1)
}

func getPopQuery(method, baseQuery string) string {
	return strings.ToUpper(method) + "&" + specialEncode("/") + "&" +
		strings.Replace(specialEncode(baseQuery), "&", "", 1)
}

func sign(accessKeySecret, popQuery string) string {
	h := hmac.New(sha1.New, []byte(accessKeySecret+"&"))
	h.Write([]byte(popQuery))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func getTimeStamp() string {
	now := time.Now()
	local, _ := time.LoadLocation("UTC")
	return now.In(local).Format(timeFormat)
}

func getNonce() string {
	id := uuid.NewV4()
	return id.String()
}

func toString(obj interface{}) string {
	b, _ := json.Marshal(obj)
	return string(b)
}
