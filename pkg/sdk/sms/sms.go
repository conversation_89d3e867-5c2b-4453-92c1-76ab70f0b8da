package sms

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"time"

	"bs.com/app/pkg/xlog"
)

// Region represents ECS region
type Region string

const (
	Hangzhou    = Region("cn-hangzhou")
	Qingdao     = Region("cn-qingdao")
	Beijing     = Region("cn-beijing")
	Hongkong    = Region("cn-hongkong")
	Shenzhen    = Region("cn-shenzhen")
	Shanghai    = Region("cn-shanghai")
	Zhangjiakou = Region("cn-zhangjiakou")
	Huhehaote   = Region("cn-huhehaote")
)

const (
	smsAPIServer        = "http://dysmsapi.aliyuncs.com/"
	smsResponseFormat   = "JSON"
	smsSignatureMethod  = "HMAC-SHA1"
	smsSignatureVersion = "1.0"
	smsAction           = "SendSms"
	smsVersion          = "2017-05-25"
	//smsRegionID         = "cn-hangzhou"
	smsRegionID = "cn-shenzhen"
)

// sms response
type smsResponse struct {
	RequestId string `json:"RequestId"`
	Code      string `json:"Code"`
	Msg       string `json:"Message"`
	BizId     string `json:"BizId"`
}

// Callback for sms response
type smsCallback struct {
	Success chan smsResponse
	Error   chan error
}

type VerifyCode struct {
	Code string `json:"code"`
}

var client *SmsClient

func GetInstance(AccessKeyID, AccessKeySecret, SignName string) {
	client = NewSmsClient(AccessKeyID, AccessKeySecret)
	client.SetSmsGlobOps(SignName, "")
}

func SendVerifyCode(phone, code, tempCode string) error {
	// verify code
	param := VerifyCode{
		Code: code,
	}

	callback := client.RequestSendVerifyCodeSms(phone, toString(param), tempCode)
	select {
	case err := <-callback.Error:
		xlog.Info("send sms err: ", err.Error())
		return err
	case success := <-callback.Success:
		//log.Println("success : ", toString(success))
		if success.Code != "OK" {
			return errors.New(success.Msg)
		} else {
			return nil
		}
	case <-time.After(10 * time.Second):
		return errors.New("timeout")
	}

}

// =========================================
// SmsClient
type SmsClient struct {
	accessKeyID      string
	accessKeySecret  string
	format           string
	signatureMethod  string
	signatureVersion string
	action           string
	version          string
	regionId         string
	smsSignName      string
	smsOutId         string
}

// Set global sms options
func (c *SmsClient) SetSmsGlobOps(smsSignName, outId string) {
	if c == nil {
		return
	}

	c.smsSignName = smsSignName
	c.smsOutId = outId
}

// JSON param for sms template
func (c *SmsClient) RequestSendVerifyCodeSms(phone, smsParam, smsTemplateCode string) *smsCallback {
	if c == nil {
		return nil
	}
	return c.RequestSendSms(c.smsSignName, smsTemplateCode, phone, smsParam, c.smsOutId)
}

// set params for sms send, these values won't be stored, just work this time
func (c *SmsClient) RequestSendSms(smsSignName, smsTemplateCode, phone, smsParam, outId string) *smsCallback {
	if c == nil {
		return nil
	}

	callback := &smsCallback{
		Success: make(chan smsResponse),
		Error:   make(chan error),
	}

	params := c.generateQueryMap(smsSignName, smsTemplateCode, phone, smsParam, outId)
	baseQuery := getBaseQuery(params)
	popQuery := getPopQuery("GET", baseQuery)
	signature := specialEncode(sign(c.accessKeySecret, popQuery))
	go func() {
		urlToGet := smsAPIServer + "?Signature=" + signature + "&" + baseQuery
		resp, err := http.Get(urlToGet)
		if err != nil {
			callback.Error <- err
			return
		}
		defer resp.Body.Close()
		content, err := io.ReadAll(resp.Body)
		if err != nil {
			callback.Error <- err
			return
		}
		smsResp := new(smsResponse)
		err = json.Unmarshal(content, smsResp)
		if err != nil {
			callback.Error <- err
			return
		}
		callback.Success <- *smsResp
	}()
	return callback
}

func (c *SmsClient) generateQueryMap(smsSignName, smsTemplateCode, phone, smsParam, outId string) map[string]string {
	result := make(map[string]string)

	// business params
	result["Action"] = c.action
	result["Version"] = c.version
	result["RegionId"] = c.regionId

	result["PhoneNumbers"] = phone
	result["SignName"] = smsSignName
	result["TemplateCode"] = smsTemplateCode
	result["TemplateParam"] = smsParam
	result["OutId"] = outId

	// system params
	result["AccessKeyId"] = c.accessKeyID
	result["Timestamp"] = getTimeStamp()
	result["Format"] = c.format
	result["SignatureMethod"] = c.signatureMethod
	result["SignatureVersion"] = c.signatureVersion
	result["SignatureNonce"] = getNonce()

	delete(result, "Signature")
	return result
}

func NewSmsClient(accessKeyID, accessKeySecret string) *SmsClient {
	return &SmsClient{
		accessKeyID:      accessKeyID,
		accessKeySecret:  accessKeySecret,
		format:           smsResponseFormat,
		signatureMethod:  smsSignatureMethod,
		signatureVersion: smsSignatureVersion,
		action:           smsAction,
		version:          smsVersion,
		regionId:         smsRegionID,
		smsSignName:      "",
		smsOutId:         "",
	}
}
