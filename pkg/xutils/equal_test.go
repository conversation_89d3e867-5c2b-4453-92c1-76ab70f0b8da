package xutils

import (
	"reflect"
	"testing"
)

func TestMapEqual(t *testing.T) {
	type M1 map[string]any
	type M2 map[string]any

	map1 := M1{
		"name":  "<PERSON>",
		"age":   30,
		"money": 1234.56,
		"email": "<EMAIL>",
	}

	map2 := M2{
		"name":  "<PERSON>",
		"age":   30,
		"money": 1234.56,
		"email": "<EMAIL>",
	}

	type Args struct {
		a M1
		b M2
	}
	tt := struct {
		name string
		args Args
		want bool
	}{
		name: "xx",
		args: Args{
			a: map1,
			b: map2,
		},
		want: true,
	}

	t.Run(tt.name, func(t *testing.T) {
		if got := MapEqual(map1, map2); got != tt.want {
			t.Errorf("=== MapEqual() = %v, want %v", got, tt.want)
		}

		// if got := reflect.DeepEqual(map1, map2); got != tt.want {
		// 	t.Errorf(">>> DeepEqual() = %v, want %v", got, tt.want)
		// }

		// 用 reflect.DeepEqual 的时候，需要做一个类型转换
		type M map[string]any
		if got := reflect.DeepEqual(M(map1), M(map2)); got != tt.want {
			t.Errorf(">>> DeepEqual() = %v, want %v", got, tt.want)
		}
	})
}
