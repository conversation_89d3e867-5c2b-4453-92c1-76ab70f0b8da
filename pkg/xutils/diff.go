package xutils

import (
	"reflect"

	"bs.com/app/pkg/xlog"
)

// 两个interface类型进行对比是否相等
func DeepEqual(i1, i2 interface{}) bool {
	v1 := reflect.ValueOf(i1)
	v2 := reflect.ValueOf(i2)

	// 检查两个类型，如果类型不同则直接不同
	if v1.Type() != v2.Type() {
		return false
	}

	switch v1.Kind() {
	case reflect.Bool, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64,
		reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr,
		reflect.Float32, reflect.Float64, reflect.Complex64, reflect.Complex128, reflect.String:
		// 基本类型进行对比
		return v1.Interface() == v2.Interface()

	case reflect.Slice, reflect.Array:
		// 切片和数组进行对比
		if v1.Len() != v2.Len() {
			return false
		}
		for i := 0; i < v1.Len(); i++ {
			if !DeepEqual(v1.Index(i).Interface(), v2.Index(i).Interface()) {
				return false
			}
		}
		return true

	case reflect.Struct:
		// 结构体对比
		for i := 0; i < v1.NumField(); i++ {
			if !DeepEqual(v1.Field(i).Interface(), v2.Field(i).Interface()) {
				return false
			}
		}
		return true

	case reflect.Map:
		// map 类型对比
		if v1.Kind() != reflect.Map || v2.Kind() != reflect.Map || v1.Len() != v2.Len() {
			return false
		}
		for _, key := range v1.MapKeys() {
			val1 := v1.MapIndex(key)
			val2 := v2.MapIndex(key)
			if !val2.IsValid() || !DeepEqual(val1.Interface(), val2.Interface()) {
				return false
			}
		}
		return true

	case reflect.Ptr:
		if v1.IsNil() != v2.IsNil() {
			return false
		}
		return DeepEqual(v1.Elem().Interface(), v2.Elem().Interface())

	default:
		// 其他未知类型
		xlog.InfoMsg("unknow type:", v1.Kind())
		return false
	}
}
