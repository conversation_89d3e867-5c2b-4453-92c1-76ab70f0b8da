package xutils

import (
	"math/rand"
	"strconv"
	"strings"
	"time"

	"bs.com/app/pkg/xlog"
	"github.com/bwmarrin/snowflake"
	"github.com/gofrs/uuid"
)

func UUID() string {
	u, err := uuid.NewV4()
	if err != nil {
		xlog.Error("uuid V4 err :", err)
		return UUIDShort()
	}
	return u.String()
}

// 时间戳 us + 随机数
func UUIDShort() string {
	t := time.Now().UnixNano() / 1000
	rand.Seed(t)
	n := rand.Int63n(1000)
	t = t*1000 + n
	return snowflake.ID(t).Base58()
}
func UUIDShort2() string {
	t := time.Now().UnixNano() / 1000
	rand.Seed(t)
	n := rand.Int63n(1000)
	t = t + n
	return snowflake.ID(t).Base36()
}

// 时间戳 us
// GZOETR8QFV
func UUIDShort3() string {
	t := time.Now().UnixNano() / 1000
	return strings.ToUpper(snowflake.ID(t).Base36())
}

// uuid snowflake:  1861314769886121984
func UUIDSnowFlake() string {
	// node 编号需要不同。最好是某个服务有一个全局的 node
	node, err := snowflake.NewNode(1)
	if err != nil {
		xlog.Error("snow flake err", "err", err)
		return ""
	}

	// return node.Generate().Base32()
	// return node.Generate().Base36()
	return node.Generate().String()
}

func GenerateUUID(nodeId int64) string {
	node, err := snowflake.NewNode(nodeId)
	if err != nil {
		xlog.Error("snowflake node failed:", err.Error())
		return UUIDShort()
	}
	return node.Generate().Base36()
}

// FU56KC
func UUIDShort4() string {
	t1 := time.Date(2024, 9, 6, 16, 10, 10, 0, time.Local)
	num := time.Since(t1).Nanoseconds() / 1000
	return strings.ToUpper(strconv.FormatInt(int64(num), 36))
}
