package xutils

import (
	"testing"

	"github.com/Masterminds/semver"
)

func TestSemVer(t *testing.T) {
	var err error
	left, right := "22.0815.01", "22.1201.08"

	var v1, v2 *semver.Version
	v1, err = semver.NewVersion(left)
	if err != nil {
		return
	}

	v2, err = semver.NewVersion(right)
	if err != nil {
		return
	}

	t.Log("v1 : ", v1.String())
	t.Log("v2 : ", v2.String())

	ret := v1.<PERSON><PERSON>han(v2)

	if err != nil {
		t.Error("semVer failed:", err)
	}

	t.Log("result : ", ret)
}

func TestNumver(t *testing.T) {
	//version := "01.2.33"
	version := "22.309.3"
	result1, err1 := VersionStringToNumber(version)
	if err1 != nil {
		t.Error("convert err : ", err1)
		return
	}
	t.Log("result1 : ", result1)

	result2, err2 := VersionNumberToString(22030903)
	if err2 != nil {
		t.Error("convert err : ", err2)
		return
	}

	t.Log("result2 : ", result2)
}

func TestVersionNumberToString(t *testing.T) {
	type args struct {
		version int64
	}
	tests := []struct {
		name       string
		args       args
		wantResult string
		wantErr    bool
	}{
		{
			name:       "1",
			args:       args{version: 24031301},
			wantResult: "24.0313.01",
			wantErr:    false,
		},
		{
			name:       "2",
			args:       args{version: 88032302},
			wantResult: "88.0323.02",
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotResult, err := VersionNumberToString(tt.args.version)
			if (err != nil) != tt.wantErr {
				t.Errorf("VersionNumberToString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotResult != tt.wantResult {
				t.Errorf("VersionNumberToString() = %v, want %v", gotResult, tt.wantResult)
			}
		})
	}
}
