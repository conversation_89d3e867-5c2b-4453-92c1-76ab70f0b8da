package xutils

import (
	"bufio"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"

	"bs.com/app/pkg/xlog"
)

// 返回 bin  jpg 等文件后缀
func FileType(filePath string) string {
	i := strings.LastIndex(strings.TrimSpace(filePath), ".")
	return filePath[i+1:]
}

func FileName(filePath string) string {
	i := strings.LastIndex(strings.TrimSpace(filePath), "/")
	return filePath[i+1:]
}

// 下载文件到目录：
func Download(url, dir string) error {
	res, err := http.Get(url)
	if err != nil {
		xlog.Error("file download : A error occurred!")
		return err
	}
	defer res.Body.Close()

	fileLocalName := FileName(url)

	if dir != "" {
		fileLocalName = fmt.Sprintf("%s/%s", dir, fileLocalName)
	}
	file, err := os.Create(fileLocalName)
	if err != nil {
		return err
	}
	// 获得文件的writer对象
	writer := bufio.NewWriter(file)

	written, _ := io.Copy(writer, res.Body)
	fmt.Printf("file size: %d", written)
	return nil
}
