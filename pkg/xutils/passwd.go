package xutils

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
)

// 固定密钥（建议从加密文件或环境变量读取，此处仅为演示）
var secretKey = []byte("QTaBVRbIuLCJTxWduYXRpcmUeW")

// GeneratePassword 生成 N 位数字密码（12位随机 + 8位校验值）

const (
	NumRandom = 4
	NumSign   = 4
)

func GeneratePassword(seed string) (string, error) {
	// 生成 4 位随机数字
	randomPart, err := generateRandomDigits(NumRandom)
	if err != nil {
		return "", err
	}

	// 计算校验值
	checksum, err := calculateChecksum(seed, randomPart, NumSign)
	if err != nil {
		return "", err
	}

	// 组合为完整密码
	return randomPart + checksum, nil
}

// VerifyPassword 验证密码合法性
func VerifyPassword(seed, password string) bool {
	// 基础格式检查
	if len(password) != (NumRandom+NumSign) || !isAllDigits(password) {
		return false
	}

	// 提取随机部分和校验值
	randomPart := password[:NumRandom]
	inputChecksum := password[NumRandom:]

	// 重新计算校验值
	expectedChecksum, err := calculateChecksum(seed, randomPart, NumSign)
	if err != nil {
		return false
	}

	return inputChecksum == expectedChecksum
}

// ---------------------- 内部工具函数 ----------------------

// 生成指定长度的随机数字字符串
func generateRandomDigits(length int) (string, error) {
	result := make([]byte, length)
	for i := 0; i < length; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(10))
		if err != nil {
			return "", err
		}
		result[i] = byte(num.Int64() + '0') // 转换为 ASCII 数字字符
	}
	return string(result), nil
}

// 计算校验值（基于 HMAC-SHA256）
func calculateChecksum(seed, randomPart string, signNum int) (string, error) {
	// 拼接随机部分和密钥
	message := []byte(randomPart + string(secretKey) + seed)

	// 计算 HMAC-SHA256
	mac := hmac.New(sha256.New, secretKey)
	_, err := mac.Write(message)
	if err != nil {
		return "", err
	}
	hashBytes := mac.Sum(nil)

	// 取后8位十六进制字符，转换为十进制后截取8位数字
	hexHash := hex.EncodeToString(hashBytes)
	last8Hex := hexHash[len(hexHash)-8:]
	decimalValue, ok := new(big.Int).SetString(last8Hex, 16)
	if !ok {
		return "", fmt.Errorf("failed to parse hex: %s", last8Hex)
	}
	decimalStr := decimalValue.String()

	// 确保长度为8位（不足补零）
	if len(decimalStr) > signNum {
		decimalStr = decimalStr[len(decimalStr)-signNum:]
	} else if len(decimalStr) < signNum {
		decimalStr = strings.Repeat("0", signNum-len(decimalStr)) + decimalStr
	}

	return decimalStr, nil
}

// 检查字符串是否全为数字
func isAllDigits(s string) bool {
	for _, c := range s {
		if c < '0' || c > '9' {
			return false
		}
	}
	return true
}
