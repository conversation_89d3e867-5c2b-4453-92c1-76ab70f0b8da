package xutils

import (
	"bytes"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math"
	"reflect"
	"strconv"
	"strings"

	"bs.com/app/pkg/xlog"
	"github.com/shopspring/decimal"
)

func JsonStringIndent(v interface{}) string {
	//b, _ := json.Marshal(v)
	b, _ := json.MarshalIndent(v, "", "  ")
	return string(b)
}

// 仅用于打印，去掉所有转义符
func CleanString(v string) string {
	// 创建一个映射，包含所有常见的转义字符及其替换值
	escapeChars := map[string]string{
		"\\": "",  // 反斜杠
		"\"": "",  // 双引号
		"\n": " ", // 换行符替换为空格
		"\r": " ", // 回车符替换为空格
		"\t": " ", // 制表符替换为空格
		"\b": "",  // 退格符
		"\f": "",  // 换页符
	}

	result := v
	for escape, replacement := range escapeChars {
		result = strings.ReplaceAll(result, escape, replacement)
	}

	return result
}
func JSONString(v interface{}) string {
	// bf := new(bytes.Buffer)
	// jsonEncoder := json.NewEncoder(bf)
	// jsonEncoder.SetEscapeHTML(false)
	// _ = jsonEncoder.Encode(v)

	// // 转换为字符串并去掉双引号
	// return strings.ReplaceAll(bf.String(), "\"", "")

	buf, _ := json.Marshal(v)
	return strings.ReplaceAll(string(buf), "\"", "")
}

const hextable = "0123456789abcdef"

// Encode encodes src into EncodedLen(len(src))
// bytes of dst. As a convenience, it returns the number
// of bytes written to dst, but this value is always EncodedLen(len(src)).
// Encode implements hexadecimal encoding.
func hexEncode(src []byte) string {
	j := 0
	dst := make([]byte, len(src)*3)
	for _, v := range src {
		dst[j] = hextable[v>>4]
		dst[j+1] = hextable[v&0x0f]
		dst[j+2] = ' '
		j += 3
	}
	//fmt.Println("len:", len(src), len(dst))
	return string(dst)
}

func Str2Hex(str string) ([]byte, error) {
	str = strings.TrimSpace(str)
	arr1 := strings.Split(str, " ")
	out1 := strings.Join(arr1, "")
	if len(out1)%2 != 0 {
		//fmt.Println("len:", len(out1))
		out1 = fmt.Sprintf("0%s", out1)
	}
	return hex.DecodeString(out1)
}

// Str2HexN 将16进制字符串转换为字节数组，长度为n字节
// 如果解析后的数据长度不足n字节，则在末尾补0
// 如果解析后的数据长度超过n字节，则截断为n字节
func Str2HexN(str string, n int64) ([]byte, error) {
	str = strings.TrimSpace(str)
	arr1 := strings.Split(str, " ")
	out1 := strings.Join(arr1, "")
	if len(out1)%2 != 0 {
		// 如果是奇数长度，在前面补0
		out1 = fmt.Sprintf("0%s", out1)
	}

	// 解析16进制字符串
	data, err := hex.DecodeString(out1)
	if err != nil {
		return nil, err
	}

	// 处理长度
	dataLen := int64(len(data))
	if dataLen == n {
		// 长度刚好，直接返回
		return data, nil
	} else if dataLen < n {
		// 长度不足，补0
		result := make([]byte, n)
		copy(result, data)
		// 后面的字节默认为0，不需要额外处理
		return result, nil
	} else {
		// 长度超出，截断
		return data[:n], nil
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func viewString(b []byte) string {
	r := []rune(string(b))
	for i := range r {
		if r[i] < 32 || r[i] > 126 {
			r[i] = '.'
		}
	}
	return string(r)
}

// 将二进制buffer 打印成1行
func DumpHexRaw(by []byte) string {
	// sb := strings.Builder{}
	// for _, one := range by {
	// 	sb.WriteString(fmt.Sprintf("%02X", one))
	// }
	// return sb.String()

	return strings.ToUpper(hex.EncodeToString(by))
}

func Str2Raw(src string) []byte {
	src = strings.TrimSpace(src)
	buf, err := hex.DecodeString(src)
	if err != nil {
		return []byte{}
	}
	return buf
}

func ByteToMap(payload []byte) (result map[string]interface{}, err error) {
	err = json.Unmarshal(payload, &result)
	if err != nil {
		xlog.Error("byte to map err", "err", err)
		return result, err
	}
	return result, nil
}

// byte 与 hex
func IsHexChar(c byte) bool {
	return (c >= '0' && c <= '9') ||
		(c >= 'a' && c <= 'f') ||
		(c >= 'A' && c <= 'F')
}

func FromHexChar(c byte) byte {
	switch {
	case '0' <= c && c <= '9':
		return c - '0'
	case 'a' <= c && c <= 'f':
		return c - 'a' + 10
	case 'A' <= c && c <= 'F':
		return c - 'A' + 10
	}
	return 0
}

func InterfaceToByte(v interface{}) (dataByte []byte) {
	dataByte, err := json.Marshal(v)
	if err != nil {
		xlog.Error("any json.marshal to byte", "err", err.Error())
		return
	}
	return dataByte
}

func Struct2Map2(obj interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < t.NumField(); i++ {
		data[strings.ToLower(t.Field(i).Name)] = v.Field(i).Interface()
	}
	return data
}

// 这个在数据库的查询里面用到没问题，但是 tlv3 中就有问题
func Struct2Map222(obj any) map[string]any {
	var m map[string]any
	buf, _ := json.Marshal(obj)
	d := json.NewDecoder(bytes.NewReader(buf))

	// 设置将float64转为一个number
	d.UseNumber()
	_ = d.Decode(&m)
	return m
}

func Struct2MapSimple(obj any) map[string]any {
	// 将结构体编码为 JSON 字符串
	buf, _ := json.Marshal(obj)

	// 创建一个 map 来存储 JSON 数据
	var m map[string]any
	_ = json.Unmarshal(buf, &m)
	return m
}

// Struct2Map 使用反射将结构体或结构体指针转换为 map[string]interface{}
// 并使用 JSON 标签作为 map 的键
func Struct2Map(obj interface{}) map[string]interface{} {
	val := reflect.ValueOf(obj)

	// 如果传入的是指针，获取指向的元素
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return nil
		}
		val = val.Elem()
	}

	typ := val.Type()

	// 确保传入值是结构体或结构体指针
	if typ.Kind() != reflect.Struct {
		return nil
	}

	result := make(map[string]interface{})
	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)
		value := val.Field(i)

		// 获取字段的 JSON 标签
		jsonTag := field.Tag.Get("json")
		if jsonTag == "" {
			jsonTag = field.Name // 使用字段名作为默认键
		} else {
			jsonTag = strings.Split(jsonTag, ",")[0] // 获取 JSON 标签的主名称
		}

		if value.CanInterface() {
			result[jsonTag] = value.Interface()
		}
	}

	return result
}

func Interface2Float64(i interface{}) float64 {
	switch val := i.(type) {
	case string:
		// fmt.Println("string", i.(string))
		fnum, err := strconv.ParseFloat(val, 64)
		if err != nil {
			xlog.Error("string to float64 error:", err.Error())
			return float64(0.0)
		}
		return fnum
	case int:
		return float64(val)
	case int32:
		return float64(val)
	case int64:
		return float64(val)
	case float32:
		return float64(val)
	case float64:
		return val
	}
	return float64(0.0)
}

// formatFloat 格式化浮点数:保留 2 位小数，或者 3 位有效位数
func FloatFormat(val float64) float64 {
	// 检查特殊的无效值
	if math.IsNaN(val) || math.IsInf(val, 0) {
		return 0
	}

	// 检查是否为0或者接近0的值
	if math.Abs(val) < 1e-10 {
		return 0
	}
	// 转换为decimal以保持精度
	d := decimal.NewFromFloat(val)

	// 获取数字的量级
	scale := int32(0)
	abs := math.Abs(val)
	if abs < 1 {
		// 对于小于1的数,计算第一个非零数字的位置
		scale = int32(math.Ceil(-math.Log10(abs)))
		// 保留到第一个非零数字后2位
		scale += 2
	} else {
		// 对于大于1的数,保留2位小数
		scale = 2
	}

	// 使用合适的精度进行四舍五入
	d = d.Round(scale)

	// 转回float64
	result, _ := d.Float64()
	return result
}
