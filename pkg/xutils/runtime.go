package xutils

import (
	"bufio"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"log"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"runtime/debug"

	"github.com/hashicorp/go-version"
	"github.com/tidwall/gjson"

	"bs.com/app/pkg/xlog"
)

// 域名解析：给定IP地址或者域名，返回IP地址
func HostIPAddress(host string) (ip string, err error) {
	address := net.ParseIP(host)
	if address != nil {
		ip = host
		return
	}

	//域名解析
	var addr *net.IPAddr
	addr, err = net.ResolveIPAddr("ip4", host)
	if err != nil {
		xlog.Info("parse server1 ip address failed, err:", err.Error())
		return
	}
	return addr.String(), nil
}

// 获取本机IP地址
// 利用 UDP 协议来实现的，生成一个UDP包，把自己的 IP 放如到 UDP 协议头中，然后从UDP包中获取本机的IP。
// 这个方法并不会真实的向外部发包，所以用抓包工具是看不到的。
// 但是会申请一个 UDP 的端口，所以如果经常调用也会比较耗时的，这里如果需要可以将查询到的IP给缓存起来，性能可以获得很大提升。
func GetOutboundIP() (string, error) {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "", err
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)

	return localAddr.IP.String(), nil
}

// IsExist 检查文件是否存在
func IsExist(src string) bool {
	_, err := os.Stat(src)

	return !os.IsNotExist(err)
}

// MkDirIfNotExist 检查文件夹是否存在
// 如果不存在则新建文件夹
func MkDirIfNotExist(src string) error {
	if IsExist(src) {
		return nil
	}
	//mkdir if not exist
	return os.MkdirAll(src, os.ModePerm)
}

// 按行读取文件
func ReadFileLines(filename string) (lines []string, err error) {
	file, err := os.OpenFile(filename, os.O_RDONLY, 0644)
	if err != nil {
		return
	}
	defer file.Close()

	bio := bufio.NewReader(file)
	for {
		var line []byte

		line, _, err = bio.ReadLine()
		if err != nil {
			if err == io.EOF {
				file.Close()
				return lines, nil
			}
			return
		}

		lines = append(lines, string(line))
	}
}
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		err = os.MkdirAll(path, os.ModePerm)
		if err != nil {
			log.Println(err)
			return false, err
		}
		return true, nil
	}
	return false, err
}

func FileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	return false
}

func CheckAndCreateDirectory(path string) error {
	_, err := os.Stat(path)
	if os.IsNotExist(err) {
		return os.MkdirAll(path, 0755)
	}
	return err
}

func CheckAndLinkSynbol(oldPath, newPath string) error {
	err := os.Symlink(oldPath, newPath)
	return err
}

func CheckFileExists(f string) bool {
	_, e := os.Stat(f)
	if e != nil {
		return false
	}
	return true
}

func CopyFile(dstName, srcName string) (written int64, err error) {
	src, err := os.Open(srcName)
	if err != nil {
		return
	}
	defer src.Close()
	dst, err := os.OpenFile(dstName, os.O_WRONLY|os.O_CREATE, 0644)
	if err != nil {
		return
	}
	defer dst.Close()
	return io.Copy(dst, src)
}

// 对比版本
func IsNewVersion(verOld, verNew string) (bool, error) {
	var err error
	vOld, err := version.NewVersion(verOld)
	if err != nil {
		return false, err
	}
	vNew, err := version.NewVersion(verNew)
	if err != nil {
		return false, err
	}

	// Comparison example. There is also GreaterThan, Equal, and just
	// a simple Compare that returns an int allowing easy >=, <=, etc.
	return vOld.LessThan(vNew), nil
}

// 检查 json byte 里面是否存在这个key值

func KeyIsExists(dataByte []byte, key string) bool {
	return gjson.GetBytes(dataByte, key).Exists()
}

// 检查map是否含有某个key
func CheckMapKey(dmap map[string]int32, key string) bool {
	if _, ok := dmap[key]; !ok {
		return false
	}
	return true
}

// tcp client task：tcp 长连接，接收服务器端下发的shuili相关消息
const (
	defaultStackSize = 4096
)

func DumpStack() string {
	var buf [defaultStackSize]byte
	n := runtime.Stack(buf[:], false)
	return string(buf[:n])
}

func Recovery(name string) {
	if err := recover(); err != nil {
		xlog.Errorf("panic recover: %s, err : %v", name, err)
		fmt.Println(DumpStack())
	}
}

func RecoverDump() {
	if e := recover(); e != nil {
		pc := make([]uintptr, 1)
		runtime.Callers(3, pc)
		f := runtime.FuncForPC(pc[0])
		fmt.Printf("panic | func=%s | error=%#v|stack=%s\n", f, e, string(debug.Stack()))
	}
}

// 获取程序自身的MD5
func SelfMd5sum() string {
	path, e := exec.LookPath(os.Args[0])
	if e != nil {
		log.Println(`Self md5sum error: look path fail:`, e)
		return ""
	}
	bs, e := os.ReadFile(path)
	if e != nil {
		log.Println(`SelfMd5sum error: read file fail:`, e)
		return ""
	}
	sum := md5.Sum(bs)
	return hex.EncodeToString(sum[:])
}

func GetExecPath() string {
	exePath, err := os.Executable()
	if err != nil {
		panic(err)
	}
	return filepath.Dir(exePath)
}
