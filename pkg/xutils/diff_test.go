package xutils

import (
	"fmt"
	"testing"
)

type TestDataStruct struct {
	A int64  `json:"A"`
	B string `json:"B"`
}

func TestDeepEqual(t *testing.T) {
	dataMap := make(map[string]interface{})
	dataMap["data1"] = TestDataStruct{A: 1, B: "c"} //`{"A":1, "B":"c"}`
	dataMap["data2"] = TestDataStruct{A: 1, B: "c"}

	resultEq := DeepEqual(dataMap["data1"], dataMap["data2"])
	fmt.Println(" result eq:", resultEq)
	if !resultEq {
		t.Fail()
	}
}
