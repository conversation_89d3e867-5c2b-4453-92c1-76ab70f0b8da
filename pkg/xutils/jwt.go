package xutils

import (
	"crypto/sha256"
	"fmt"
)

// 生成密码
// 明文密码，测试用
func HashPassword(pwd string) string {
	return pwd
}

// 密码 Hash ,并反转
func HashPassword2(pwd string) string {
	h := sha256.New()
	h.Write([]byte(pwd))
	bs := h.Sum(nil)
	s := fmt.Sprintf("%x", bs)
	return ReverseString(s)
}

// 反转
func ReverseString(s string) string {
	runes := []rune(s)
	for from, to := 0, len(runes)-1; from < to; from, to = from+1, to-1 {
		runes[from], runes[to] = runes[to], runes[from]
	}
	return string(runes)
}
