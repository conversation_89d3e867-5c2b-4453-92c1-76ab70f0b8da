package xutils

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"bs.com/app/pkg/xlog"
	"github.com/Masterminds/semver"
)

// 关于 semver：语义化版本控制规范（SemVer）：https://semver.org/lang/zh-CN/
// 版本格式：主版本号.次版本号.修订号，版本号递增规则如下：

// 主版本号：当你做了不兼容的 API 修改，
// 次版本号：当你做了向下兼容的功能性新增，
// 修订号：当你做了向下兼容的问题修正。

// 判断版本号是否合法
func IsValidVersion(val string) bool {
	_, err := semver.NewVersion(val)
	return err == nil
}

// 如果版本号不合法，则返回 err
// 如果 left < right ，则返回 ret = true
func VersionLeftLessThanRight(left, right string) (ret bool, err error) {
	var v1, v2 *semver.Version
	v1, err = semver.NewVersion(left)
	if err != nil {
		return
	}

	v2, err = semver.NewVersion(right)
	if err != nil {
		return
	}

	ret = v1.LessThan(v2)
	return
}

// 如果 left > right ，则返回 ret = true
func VersionLeftGreaterThanRight(left, right string) (ret bool, err error) {
	var v1, v2 *semver.Version

	v1, err = semver.NewVersion(left)
	if err != nil {
		return
	}

	v2, err = semver.NewVersion(right)
	if err != nil {
		return
	}

	ret = v1.GreaterThan(v2)
	return
}

// yymmddhhxx
// xx.yyyy.zz
func VersionStringToNumber(version string) (result int64, err error) {
	if _, err = semver.NewVersion(version); err != nil {
		xlog.Error("invalid semVer format: ", version)
		return
	}
	arr := strings.Split(version, ".")
	if len(arr) != 3 {
		err = errors.New("invalid version, need  x.y.z")
		return
	}
	x, e1 := strconv.ParseInt(arr[0], 10, 64)
	y, e2 := strconv.ParseInt(arr[1], 10, 64)
	z, e3 := strconv.ParseInt(arr[2], 10, 64)
	if e1 != nil || e2 != nil || e3 != nil {
		err = errors.New("parse version to int failed")
		return
	}
	result = x*1000000 + y*100 + z
	return
}

// xx.yyyy.zz
func VersionNumberToString(version int64) (result string, err error) {
	if version < 1000000 {
		err = errors.New("invalid number version")
		return
	}
	x := version / 1000000
	y := (version / 100) % 10000
	z := version % 100

	result = fmt.Sprintf("%d.%04d.%02d", x, y, z)

	// if _, err = semver.NewVersion(result); err != nil {
	// 	return
	// }
	return
}
