package xutils

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"regexp"
	"strings"
	"time"

	"bs.com/app/pkg/xlog"
)

// mac是小端
func IsBigEndian() bool {
	var a int16 = 0x1234
	return int8(a) == 0x12
}

// 判断字符串是否是字母+数字的
func IsNumberOrLetter(input string) bool {
	reg := regexp.MustCompile(`^[^\d]+[a-zA-Z\d]+$`)
	return reg.MatchString(input)
}

func Uint16Reverse(v uint16) uint16 {
	return (^v) & 0xffff
}

// GetDID 去掉冒号，去掉空格，拼接，转小写
// 根据pid和mac地址，生成设备唯一ID的方式
func GetDID(pid, hwid string) (fmtMac string, did string) {

	// 格式化 mac 地址：去掉冒号，转小写
	macParts := strings.Split(strings.TrimSpace(hwid), ":")
	fmtMac = strings.ToLower(strings.Join(macParts, ""))

	pidStr := strings.ToLower(strings.TrimSpace(pid))

	did = pidStr + fmtMac

	return
}

// 密码加盐 hash
func PasswordHash(account, pwd string) string {
	accountHex := hex.EncodeToString([]byte(account))
	salt1 := "MayTheForceBeWithYou"
	salt2 := "GodBlessUs"
	data := strings.Join([]string{account, accountHex, salt1, pwd, salt2}, "And")

	hash := sha1.New()
	hash.Write([]byte(data))
	result := hash.Sum(nil)

	return hex.EncodeToString(result)
}

func PasswordVerify(accout, pwd, hashpwd string) bool {
	return PasswordHash(accout, pwd) == hashpwd
}

func ComputeHmacSha1(message string, secret string) string {
	key := []byte(secret)
	h := hmac.New(sha1.New, key)
	h.Write([]byte(message))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func ComputeHmacSha256(message string, secret string) string {
	key := []byte(secret)
	h := hmac.New(sha256.New, key)
	h.Write([]byte(message))
	return fmt.Sprintf("%x", h.Sum(nil))
}

func ComputeHmacMd5(message, secret string) string {
	w := md5.New()
	io.WriteString(w, secret)
	md5str := fmt.Sprintf("%x", w.Sum(nil))
	return md5str
}

func HmacSha256(data string, secret []byte) string {
	h := hmac.New(sha256.New, secret)
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

func GenDevicePassword(did string) string {
	salt := "hntmwsPi17*_L@@J$"

	mdtStr := fmt.Sprintf("%v.%v.%v", did, salt, time.Now().String())

	w := md5.New()
	io.WriteString(w, mdtStr)
	md5str := fmt.Sprintf("%x", w.Sum(nil))
	return md5str

}

// binary
func BytesToInt64(buf []byte) int64 {
	return int64(binary.BigEndian.Uint64(buf))
}

func Int64ToBytes(i int64) []byte {
	var buf = make([]byte, 8)
	binary.BigEndian.PutUint64(buf, uint64(i))
	return buf
}

// BytesCombine 多个[]byte数组合并成一个[]byte
func BytesCombine(pBytes ...[]byte) []byte {
	return bytes.Join(pBytes, []byte(""))
}

func Base64ToString(encodeString string) string {
	decodeBytes, err := base64.StdEncoding.DecodeString(encodeString)
	if err != nil {
		xlog.Error("base64 to string", "err", err)
		return ""
	}
	return string(decodeBytes)
}

func StringToBase64(codeString string) string {
	return base64.StdEncoding.EncodeToString([]byte(codeString))
}

// HmacSha256 签名算法，结果使用 Base64编码成字符串
func HmacSha256Base64(message string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(message))
	sha := hex.EncodeToString(h.Sum(nil))
	// return base64.StdEncoding.EncodeToString([]byte(sha))
	return base64.URLEncoding.EncodeToString([]byte(sha))

}

// HmacSha256 签名算法，结果使用 hex string
func HmacSha256Hex(data string, secret []byte) string {
	h := hmac.New(sha256.New, secret)
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// 把 "0xff12" 转换为整数（uint16）
func HexStr2Uint16(hexStr string) (uint16, error) {
	// 去掉空格和前缀"0x"
	hexStr = strings.ToLower(hexStr)
	hexStr = strings.TrimSpace(hexStr)
	hexStr = strings.TrimPrefix(hexStr, "0x")

	// 将十六进制字符串转换为字节数组
	bytes, err := hex.DecodeString(hexStr)
	if err != nil {
		return 0, err
	}

	// 确保字节数组的长度为2（uint16需要2个字节）
	switch len(bytes) {
	case 1:
		// 只有一个字节，在前面填充一个0字节
		return uint16(bytes[0]), nil
	case 2:
		// 刚好两个字节，不需要处理
	default:
		// 超过两个字节
		return 0, errors.New("invalid uint16")
	}

	// 转换为uint16
	return binary.BigEndian.Uint16(bytes), nil
}
