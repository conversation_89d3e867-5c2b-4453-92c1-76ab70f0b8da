package xutils

import (
	"fmt"
	"runtime"
	"strconv"
	"time"

	"bs.com/app/pkg/xlog"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
)

const (
	B  = 1
	KB = 1024 * B
	MB = 1024 * KB
	GB = 1024 * MB
)

type OS struct {
	GOOS         string `json:"goos"`
	NumCPU       int    `json:"num_cpu"`
	Compiler     string `json:"compiler"`
	GoVersion    string `json:"go_version"`
	NumGoroutine int    `json:"num_goroutine"`
	AliveTime    string `json:"alive_time"`
}

func (os *OS) String() string {
	return JSONString(os)
}

type CPU struct {
	Cpus     []float64 `json:"cpus"`
	Cores    int       `json:"cores"`
	CpuSN    string    `json:"cpu_sn"`
	VendorID string    `json:"vendor_id"`
	Family   string    `json:"family"`
	Model    string    `json:"model"`
}

func (c *CPU) String() string {
	return JSONString(c)
}

type RAM struct {
	UsedMB      int `json:"used_mb"`
	TotalMB     int `json:"total_mb"`
	UsedPercent int `json:"used_percent"`
}

func (r *RAM) String() string {
	return JSONString(r)
}

type Disk struct {
	UsedMB      int `json:"used_mb"`
	UsedGB      int `json:"used_gb"`
	TotalMB     int `json:"total_mb"`
	TotalGB     int `json:"total_gb"`
	UsedPercent int `json:"used_percent"`
}

func (d *Disk) String() string {
	return JSONString(d)
}

// =====================================================================
var startTime time.Time

func init() {
	startTime = time.Now()
}

// OS信息
func InfoOS() (o *OS) {
	o = &OS{
		GOOS:         runtime.GOOS,
		NumCPU:       runtime.NumCPU(),
		Compiler:     runtime.Compiler,
		GoVersion:    runtime.Version(),
		NumGoroutine: runtime.NumGoroutine(),
	}
	elapsed := time.Since(startTime)
	sec := int64(elapsed.Seconds())
	day := sec / 86400
	hour := sec % 86400 / 3600
	minute := sec % 3600 / 60
	sec = sec % 60

	o.AliveTime = fmt.Sprintf("%d天%d时%d分%d秒", day, hour, minute, sec)

	return
}

// CPU信息
func InfoCPU() (c *CPU) {
	c = &CPU{}
	if cores, err := cpu.Counts(false); err != nil {
		xlog.Error("psutil failed:" + err.Error())
		return
	} else {
		c.Cores = cores
	}
	//if cpus, err := cpu.Percent(time.Duration(200)*time.Millisecond, true); err != nil {
	if cpus, err := cpu.Percent(0, true); err != nil {
		xlog.Error("psutil failed:" + err.Error())
		return
	} else {
		c.Cpus = cpus
	}
	if cpuStat, err := cpu.Info(); err != nil {
		xlog.Error("psutil failed:" + err.Error())
		return
	} else {
		c.CpuSN = strconv.FormatInt(int64(cpuStat[0].CPU), 10)
		c.VendorID = cpuStat[0].VendorID
		c.Family = cpuStat[0].Family
		c.Model = cpuStat[0].ModelName
	}

	return
}

type Host struct {
	HostName string `json:"host_name"`
	Uptime   string
	OS       string
	Platform string
	HostID   string
}

type SystemStateTemplate struct {
	MemTotal         string `json:"MemTotal"`
	MemUse           string `json:"MemUse"`
	DiskTotal        string `json:"DiskTotal"`
	DiskUse          string `json:"DiskUse"`
	Name             string `json:"Name"`
	SN               string `json:"SN"`
	HardVer          string `json:"HardVer"`
	SoftVer          string `json:"SoftVer"`
	SystemRTC        string `json:"SystemRTC"`
	RunTime          string `json:"RunTime"`          //累计时间
	DeviceOnline     string `json:"DeviceOnline"`     //设备在线率
	DevicePacketLoss string `json:"DevicePacketLoss"` //设备丢包率
}

func InfoHost() (h *Host) {
	h = &Host{}
	if hostStat, err := host.Info(); err != nil {
		h.HostName = hostStat.Hostname + "<br>"
		h.Uptime = strconv.FormatUint(hostStat.Uptime, 10) + "Second"
		h.OS = hostStat.OS
		h.Platform = hostStat.Platform
		h.HostID = hostStat.HostID
	}
	return
}

// ARM信息
func InfoRAM() (r *RAM) {
	r = &RAM{}
	if u, err := mem.VirtualMemory(); err != nil {
		xlog.Error("psutil failed:" + err.Error())
	} else {
		r.UsedMB = int(u.Used) / MB
		r.TotalMB = int(u.Total) / MB
		r.UsedPercent = int(u.UsedPercent)
	}
	return
}

// 硬盘信息
func InfoDisk() (d *Disk) {
	d = &Disk{}
	if u, err := disk.Usage("/"); err != nil {
		xlog.Error("psutil failed:" + err.Error())
	} else {
		d.UsedMB = int(u.Used) / MB
		d.UsedGB = int(u.Used) / GB
		d.TotalMB = int(u.Total) / MB
		d.TotalGB = int(u.Total) / GB
		d.UsedPercent = int(u.UsedPercent)
	}
	return
}
