package xutils

import (
	"fmt"
	"strings"
	"testing"
	"time"
)

func TestUUIDV4(t *testing.T) {
	got := UUID()
	fmt.Println("uuid : ", got)
}

func TestUUIDShort(t *testing.T) {
	fmt.Println("uuid v4 : ", UUID())
	fmt.Println("uuid short 1: ", UUIDShort())
	fmt.Println("uuid short 2: ", UUIDShort2())
	fmt.Println("uuid short 3: ", UUIDShort3())
	fmt.Println("uuid short 4: ", UUIDShort4())
	fmt.Println("uuid snowflake: ", UUIDSnowFlake())
}

func TestEvery(t *testing.T) {
	Every(time.Second*2, func() {
		fmt.Println("every 2 second")
	})

	time.Sleep(10 * time.Second)
}

func TestAfter(t *testing.T) {
	After(time.Second*2, func() {
		fmt.Println("work...")
	})

	time.Sleep(3 * time.Second)
}

func TestUtils222(t *testing.T) {
	ret, _ := GetOutboundIP()

	arr := strings.Split(ret, ".")
	fmt.Println(arr, arr[3])

}

func TestCRC16Modbus(t *testing.T) {
	buf := []byte("1234567890")
	ret1 := CRC16Modbus(buf)
	ret2, _ := CRC16IBM5(buf)

	t.Logf("ret1 : %x", ret1)
	t.Logf("ret2 : %x", ret2)
}
