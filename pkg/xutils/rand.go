package xutils

import (
	"bytes"
	"crypto/md5"
	"fmt"
	"math/rand"
	"sync"
	"time"
)

var (
	r  = rand.New(rand.NewSource(time.Now().UnixNano()))
	mu sync.Mutex
)
var globalSeed = time.Now().UnixNano()

var letterRunes = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
var numberRunes = []rune("0123456789")

func RandNumbers(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = numberRunes[rand.Intn(len(numberRunes))]
	}
	return string(b)
}

func RandStringN(n int) string {
	b := make([]rune, n)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}

func RandUint32n(n uint32) uint32 {
	mu.Lock()
	res := r.Int31n(int32(n))
	mu.Unlock()
	return uint32(res)
}

// 使用互斥锁是为了确保在并发环境下对共享资源的安全访问。在这个文件中，rand.NewSource()和rand.Intn()等函数会使用全局的随机数生成器。
// 如果多个goroutine同时调用这些函数，可能会导致数据竞争和不确定的行为。通过使用互斥锁，我们可以确保在同一时间只有一个goroutine能够访问和修改随机数生成器的状态，从而保证生成的随机数是正确和可预测的。

func RandIntn(n int) int {
	mu.Lock()
	res := r.Intn(n)
	mu.Unlock()
	return res
}

func RandBetween(min, max int) int {
	// 初始化随机数种子
	rand.Seed(time.Now().UnixNano())

	// 生成min到max之间的随机数（包含min，不包含max）
	return rand.Intn(max-min) + min
}

func RandU8() uint8 {
	return 15 + uint8(rand.Intn(15))
}

func RandU16() uint16 {
	return 15 + uint16(rand.Intn(15))
}

func RandU32() uint32 {
	return rand.Uint32()
}

func RandFloat32() float32 {
	n := rand.Intn(10)
	if n > 6 {
		return float32(n) + 0.5
	} else {
		return float32(n)
	}
}

func RandFloat64() float64 {
	return float64(rand.Intn(50)) / 10
}

func RandInt64() int64 {
	// rand.Seed(time.Now().UnixNano())
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return r.Int63()
}

func RandText() string {
	return RandString(10)
}

const letterMacs = "abcdef0123456789"

func RandMac() string {

	b := make([]byte, 12)
	for i := range b {
		b[i] = letterMacs[rand.Int63()%int64(len(letterMacs))]
	}
	return string(b)
}

// 发送手机或者邮件验证码的时候，用于生成随机验证码
const (
	numRange = "012356789"
)

func RandNumCode(count int) string {
	textNum := len(numRange)
	text := ""
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < count; i++ {
		text = text + string(numRange[r.Intn(textNum)])
	}
	return text
}

const letterBytes = "abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"

func RandString(n int) string {

	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand.Int63()%int64(len(letterBytes))]
	}
	return string(b)
}

// 18位数字字符串：YYYYMMDDhhmmss + 4位随机数字
func GetTimeRandNum() string {
	now := time.Now().Format("20060102150405")
	randNum := RandNumCode(4)
	return fmt.Sprintf("%v%v", now, randNum)
}

func GetRandomNumCode(count int) string {
	textNum := len(numRange)
	text := ""
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < count; i++ {
		text = text + string(numRange[r.Intn(textNum)])
	}
	return text
}

func GenerateKey(params ...interface{}) string {
	buf := bytes.Buffer{}
	for _, param := range params {
		buf.WriteString(fmt.Sprintf("%v", param))
	}

	bytes := md5.Sum(buf.Bytes())
	md5str := fmt.Sprintf("%x", bytes)
	return md5str
}
