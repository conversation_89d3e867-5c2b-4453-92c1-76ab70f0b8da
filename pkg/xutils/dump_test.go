package xutils

import (
	"log/slog"
	"testing"
)

func TestObj2Map(t *testing.T) {

	type TestData struct {
		A    int64   `json:"A,omitempty"`
		B    string  `json:"B,omitempty"`
		Flow float64 `json:"flow,omitempty"`
	}

	d1 := TestData{
		A:    1024123124444787,
		B:    "",
		Flow: 1.2345678987654567876545678765456787654345678654,
	}
	m1 := Struct2Map222(d1)
	m2 := Struct2MapSimple(d1)
	m3 := Struct2MapSimple(m1)

	t.Log("m1 :", m1)
	t.Log("m2 :", m2)
	t.Log("m3 :", m3)

	t.Log(JSONString(d1))
	t.Log(JSONString(m1))
	t.Log(JSONString(m2))
	t.Log(JSONString(m3))

	t.Log(JSONString(d1))
	slog.Info("some", "out", JSONString(d1))
}
