package xlog

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"runtime"
	"strings"
	"time"
)

var Trace = false

type myLogger struct {
	*slog.LevelVar // 整个进程共享 level
	*slog.Logger
	depth int
}

// override default log
func (l *myLogger) log(level slog.Level, msg string, args ...any) {
	ctx := context.Background()

	if !l.Logger.Enabled(ctx, level) {
		return
	}

	var pcs [1]uintptr
	runtime.Callers(l.depth, pcs[:])

	record := slog.NewRecord(time.Now(), level, msg, pcs[0])
	record.Add(args...)

	_ = l.Logger.Handler().Handle(ctx, record)
}

// ===============================================================================================

func WithKV(k string, v any) ILogger {
	// clone default logger,sed global fileds
	return &myLogger{
		Logger:   xlogger.Logger.With(slog.Any(k, v)),
		LevelVar: xlogger.LevelVar,
		depth:    3, //add caller skip for helper functions
	}
}

// args 必须是 slog.Attr， 或者 kv键值对的排列
func WithFileds(args ...any) ILogger {
	// clone default logger,sed global fileds
	return &myLogger{
		Logger:   xlogger.Logger.With(args...),
		LevelVar: xlogger.LevelVar,
		depth:    3, //add caller skip for helper functions
	}
}

// --------------------------------------
var (
	stationSN = "stationSN"
	imei_id   = "IMEI"
)

// for gorm logger，输出到 stdout
func NewWithDepth(depth int) ILogger {

	// 更多选项
	lvl := new(slog.LevelVar)
	// lvl.Set(slog.LevelWarn)
	lvl.Set(slog.LevelDebug)

	option := &slog.HandlerOptions{
		AddSource: true, //文件名、行数、函数
		Level:     lvl,
		ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
			// 格式化时间
			if a.Key == slog.TimeKey {
				layout := "2006-01-02 15:04:05.000000"
				if t, ok := a.Value.Any().(time.Time); ok {
					a.Value = slog.StringValue(t.Format(layout))
				}
			}

			if a.Key == slog.SourceKey {
				// 简化 sourceKey.标准库后面可能会优化这部分实现
				source, ok := a.Value.Any().(*slog.Source)
				if ok {
					source.File = source.File[strings.LastIndex(source.File, "/")+1:]
					// a.Value = slog.AnyValue(source)
					a.Key = "_trace_" //避免命名冲突
					if strings.HasPrefix(source.File, "gorm_logger.go") {
						a.Value = slog.StringValue("[ ]")
					} else {
						// a.Value = slog.StringValue(fmt.Sprintf("%s:%d  %s", source.File, source.Line, source.Function))
						a.Value = slog.StringValue(fmt.Sprintf("%s:%d", source.File, source.Line))
					}
				}
			}
			return a
		},
	}

	// jsonLogger := slog.New(slog.NewJSONHandler(writer, option))
	writer := os.Stdout
	textLogger := slog.New(slog.NewTextHandler(writer, option))
	return &myLogger{
		Logger:   textLogger,
		LevelVar: lvl,
		depth:    depth, //add caller skip for helper functions
	}
}
func WithSSN(station_sn string) ILogger {
	if station_sn != "" {
		return &myLogger{
			Logger:   xlogger.Logger.With(slog.String(stationSN, station_sn)),
			LevelVar: xlogger.LevelVar,
			depth:    3, //add caller skip for helper functions
		}
	}
	return xlogger
}

func WithIMEI(imei string) ILogger {
	if imei != "" {
		return &myLogger{
			Logger:   xlogger.Logger.With(slog.String(imei_id, imei)),
			LevelVar: xlogger.LevelVar,
			depth:    3, //add caller skip for helper functions
		}
	}
	return xlogger
}

// --------------------------------------

// 结构化日志：强制如下接口,其中，args 必须是 slog.Attr， 或者 kv键值对的排列
type ILogger interface {
	SetLevel(level string)
	GetLevel() string

	Debug(msg string, args ...any)
	Debugf(msg string, args ...any)
	Info(msg string, args ...any)
	Infof(msg string, args ...any)
	Warn(msg string, args ...any)
	Warnf(msg string, args ...any)
	Error(msg string, args ...any)
	Errorf(msg string, args ...any)

	WithKV(k string, v any) ILogger
	WithFileds(args ...any) ILogger
}

func (l *myLogger) SetLevel(level string) {

	Info(">>>>>>>>> logger set level", "level", level)

	switch level {
	case "debug":
		l.LevelVar.Set(slog.LevelDebug)
	case "info":
		l.LevelVar.Set(slog.LevelInfo)
	case "warn":
		l.LevelVar.Set(slog.LevelWarn)
	default:
		l.LevelVar.Set(slog.LevelError)
	}
}

func (l *myLogger) GetLevel() string {
	return l.LevelVar.Level().String()
}

func (l *myLogger) Debug(msg string, args ...any) {
	l.log(slog.LevelDebug, msg, args...)
}

func (l *myLogger) Debugf(msg string, args ...any) {
	l.log(slog.LevelDebug, fmt.Sprintf(msg, args...))
}

func (l *myLogger) Info(msg string, args ...any) {
	l.log(slog.LevelInfo, msg, args...)
}
func (l *myLogger) Infof(msg string, args ...any) {
	l.log(slog.LevelInfo, fmt.Sprintf(msg, args...))
}

func (l *myLogger) Warn(msg string, args ...any) {
	l.log(slog.LevelWarn, msg, args...)
}
func (l *myLogger) Warnf(msg string, args ...any) {
	l.log(slog.LevelWarn, fmt.Sprintf(msg, args...))
}

func (l *myLogger) Error(msg string, args ...any) {
	l.log(slog.LevelError, msg, args...)
}
func (l *myLogger) Errorf(msg string, args ...any) {
	l.log(slog.LevelError, fmt.Sprintf(msg, args...))
}

func (l *myLogger) WithKV(k string, v any) ILogger {
	// clone default logger,sed global fileds
	return &myLogger{
		Logger:   l.Logger.With(slog.Any(k, v)),
		LevelVar: l.LevelVar,
		depth:    3, //add caller skip for helper functions
	}
}

func (l *myLogger) WithFileds(args ...any) ILogger {
	// clone default logger,sed global fileds
	return &myLogger{
		Logger:   l.Logger.With(args...),
		LevelVar: l.LevelVar,
		depth:    3, //add caller skip for helper functions
	}
}

// ===============================================================================================
//
//	sugar 接口，直接调用

func SetLevel(level string) {
	switch level {
	case "debug":
		xlogger.LevelVar.Set(slog.LevelDebug)
	case "info":
		xlogger.LevelVar.Set(slog.LevelInfo)
	case "warn":
		xlogger.LevelVar.Set(slog.LevelWarn)
	default:
		xlogger.LevelVar.Set(slog.LevelError)
	}
}

func GetLevel() string {
	return xlogger.LevelVar.Level().String()
}

func Debug(msg string, args ...any) {
	xlogger.Debug(msg, args...)
}
func Debugf(msg string, args ...any) {
	xlogger.Debug(fmt.Sprintf(msg, args...))
}

func Info(msg string, args ...any) {
	xlogger.Info(msg, args...)
}

func InfoMsg(args ...any) {
	// 只输出key为msg的信息
	msg := ""
	for _, arg := range args {
		msg += fmt.Sprintf("%v ", arg)
	}
	xlogger.Info(msg)
}

func Infof(msg string, args ...any) {
	xlogger.Info(fmt.Sprintf(msg, args...))
}

func Warn(msg string, args ...any) {
	xlogger.Warn(msg, args...)
}
func Warnf(msg string, args ...any) {
	xlogger.Warn(fmt.Sprintf(msg, args...))
}

func Error(msg string, args ...any) {
	xlogger.Error(msg, args...)
}
func Errorf(msg string, args ...any) {
	xlogger.Error(fmt.Sprintf(msg, args...))
}
