package xlog

import (
	"fmt"
	"io"
	"log/slog"
	"os"
	"path"
	"strings"
	"sync"
	"time"

	"bs.com/app/pkg/xlog/rotatelogs"
	"github.com/natefinch/lumberjack"
)

// 全局的，内部的 logger
// 用于初始化，避免在执行 InitLogger 之前，调用 mylogger 导致空指针
var xlogger *myLogger = initLogger("text", "ebox", "debug", "", "")

var once sync.Once

// 如果需要保存日志到文件，则需要手动初始化一次，覆盖掉 xlogger
func InitLogger(format, servicename, level, filename, filepath string) ILogger {
	once.Do(func() {
		xlogger = initLogger(format, servicename, level, filename, filepath)
	})
	return xlogger
}

// 根据日志文件 size 分割
func rotateBySize(logFile string) io.Writer {
	// 按照 size 来份文件
	return &lumberjack.Logger{
		Filename:   logFile,
		LocalTime:  true,
		MaxSize:    1,
		MaxAge:     3,
		MaxBackups: 5,
		Compress:   true,
	}
}

// 根据日期分割
func rotateByDate(logFile string) io.Writer {
	// maxSize := 1024 * 10           // 日志文件最大大小（MB）
	// maxBackups := 10               // 保留日志文件的最大数量
	maxAge := 10                   //保存最近 10 天的日志
	rotationTime := time.Hour * 24 // 24 小时轮转一次
	opts := []rotatelogs.Option{
		rotatelogs.WithMaxAge(time.Duration(maxAge) * time.Hour * 24),
		rotatelogs.WithRotationTime(rotationTime),
		// rotatelogs.WithRotationCount(maxBackups),
		// rotatelogs.WithRotationSize(maxSize),
		rotatelogs.WithClock(rotatelogs.Local),
	}

	filename := strings.SplitN(logFile, ".", 2)
	var l *rotatelogs.RotateLogs
	if len(filename) >= 2 {
		l, _ = rotatelogs.New(
			filename[0]+".log.%Y%m%d."+filename[1],
			opts...,
		)
	} else {
		l, _ = rotatelogs.New(
			filename[0]+".log.%Y%m%d",
			opts...,
		)
	}
	return l
}

// filename: 带后缀或者不带后缀都可以，建议不带后缀
func initLogger(format, servicename, level, filename, filepath string) *myLogger {
	var writer io.Writer

	if filename == "" || filepath == "" {
		writer = os.Stdout
	} else {
		fmt.Printf("========== do initLogger,  format= %s, servicename = %s, level = %s,  filename = %s,filepath = %s\n", format, servicename, level, filename, filepath)

		logFile := path.Join(filepath, filename)
		rotate := rotateByDate(logFile)

		// 使用日志文件，自动分片，同时也输出到终端
		// writer = io.MultiWriter(os.Stdout, rotate)
		writer = rotate
	}

	_ = writer

	// 更多选项
	lvl := new(slog.LevelVar)
	lvl.Set(slog.LevelDebug)

	option := &slog.HandlerOptions{
		AddSource: true, //文件名、行数、函数
		Level:     lvl,
		ReplaceAttr: func(groups []string, a slog.Attr) slog.Attr {
			// 格式化时间
			if a.Key == slog.TimeKey {
				layout := "2006-01-02 15:04:05.000000"
				if t, ok := a.Value.Any().(time.Time); ok {
					a.Value = slog.StringValue(t.Format(layout))
				}
			}

			if a.Key == slog.SourceKey {
				// 简化 sourceKey.标准库后面可能会优化这部分实现
				source, ok := a.Value.Any().(*slog.Source)
				if ok {
					source.File = source.File[strings.LastIndex(source.File, "/")+1:]
					// a.Value = slog.AnyValue(source)
					a.Key = "_trace_" //避免命名冲突
					if strings.HasPrefix(source.File, "gorm_logger.go") {
						a.Value = slog.StringValue("[ ]")
					} else {
						// a.Value = slog.StringValue(fmt.Sprintf("%s:%d  %s", source.File, source.Line, source.Function))
						a.Value = slog.StringValue(fmt.Sprintf("%s:%d", source.File, source.Line))
					}
				}
			}
			return a
		},
	}

	jsonLogger := slog.New(slog.NewJSONHandler(writer, option))
	textLogger := slog.New(slog.NewTextHandler(writer, option)).With("service", servicename)

	_ = jsonLogger
	_ = textLogger

	choose := jsonLogger
	if format == "text" {
		choose = textLogger
	}
	l := &myLogger{
		LevelVar: lvl,
		Logger:   choose,
		depth:    4,
	}
	return l
}
