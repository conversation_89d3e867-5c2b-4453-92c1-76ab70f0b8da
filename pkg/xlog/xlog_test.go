package xlog

import (
	"errors"
	"log/slog"
	"testing"
)

func TestLog(t *testing.T) {

	InitLogger("text", "test", "debug", "", "")

	slog.Info("msg", "k1", 1, "k2", 2.235)

	l := slog.With(slog.String("name", "jack"))
	l<PERSON>("msg", slog.Any("age", 100))

	slog.Debug("........... 1")
	SetLevel("debug")
	slog.Debug("........... 2")
	Debug("........... 3")

	iotLogger := WithKV("module", "iot")
	iotLogger.Info("hello i am iot logger", "some", "other")
	// iotLogger.SetLevel("info")
	Debug("........... 4")
	iotLogger.Debug("........... 5")

	err := errors.New("some error..........8")

	Error("some err:", err, 1, "jack")

	shuiLogger := WithKV("module", "shui")
	shuiLogger.Info("hello some shui")

	shuiLogger.Debug("............ 9")

	// shuiLogger.WithKV("k1", 111).WithKV("k2", "2222").Info("ok test")
	shuiLogger.WithFileds("k1", 111).WithFileds("k2", "2222", "k3", 1.2333).Info("ok test")

	// // logger2 := WithFileds("k1", 1, "k2", "v2")
	// logger2 := WithFileds(slog.Int("k1", 1), slog.String("k22", "v22"))
	// logger2.Info("something happen", "err", "err......11")

	l3 := NewWithDepth(3)
	l3.Info("with depth")
}
