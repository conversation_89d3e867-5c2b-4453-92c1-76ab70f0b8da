# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/
doc/apitest/token.txt

.DS_Store

shutdown*.sh

# 日志文件路径、临时文件存储等
.idea/
*.run
*.P0G
log-*.link
*.response

*.bin
*.apk
*.gz
*.rnx
app_linux
ebox_mac
ebox_arm

cdn/
bak/
build/
deploy/datastore/

.vscode/


**/__pycache__
*/__pycache__

***/__pycache__
__pycache__/
*.pyc

*.xlsx
*.log


config_local.yaml
local.sh
